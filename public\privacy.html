<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>隐私管理 - 四川省数字资产发行平�?/title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: #f8f0e0;
        }
        
        .privacy-section {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .privacy-item {
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(218, 165, 32, 0.2);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .privacy-item:hover {
            background: rgba(80, 60, 40, 0.5);
            border-color: rgba(218, 165, 32, 0.4);
            transform: translateY(-1px);
        }
        
        .privacy-item:last-child {
            margin-bottom: 0;
        }
        
        .item-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .item-title {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
        }
        
        .item-version {
            font-size: 11px;
            color: #d4af37;
            background: rgba(218, 165, 32, 0.2);
            padding: 2px 8px;
            border-radius: 8px;
        }
        
        .item-description {
            font-size: 13px;
            color: rgba(240, 224, 208, 0.7);
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .item-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .item-date {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .item-action {
            color: #daa520;
            text-decoration: none;
            font-weight: 500;
        }
        
        .item-action:hover {
            color: #f8f0e0;
        }
        
        .toggle-switch {
            position: relative;
            width: 50px;
            height: 28px;
            background: rgba(60, 40, 30, 0.7);
            border-radius: 14px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            border-color: #daa520;
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 22px;
            height: 22px;
            background: #f8f0e0;
            border-radius: 50%;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(22px);
            background: #2a1616;
        }
        
        .settings-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            margin-bottom: 8px;
            border: 1px solid rgba(218, 165, 32, 0.2);
        }
        
        .settings-info {
            flex: 1;
        }
        
        .settings-title {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 4px;
        }
        
        .settings-description {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
            line-height: 1.3;
        }
        
        .permission-list {
            margin-top: 12px;
        }
        
        .permission-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid rgba(218, 165, 32, 0.1);
        }
        
        .permission-item:last-child {
            border-bottom: none;
        }
        
        .permission-info {
            flex: 1;
        }
        
        .permission-name {
            font-size: 14px;
            color: #f0e0d0;
            margin-bottom: 2px;
        }
        
        .permission-desc {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .data-summary {
            background: rgba(218, 165, 32, 0.1);
            border: 1px solid rgba(218, 165, 32, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .summary-title {
            font-size: 14px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .summary-content {
            font-size: 13px;
            color: rgba(240, 224, 208, 0.8);
            line-height: 1.5;
        }
        
        .data-actions {
            margin-top: 16px;
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 100;
            padding: 20px;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 16px;
            padding: 24px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
        }
        
        .modal-close {
            background: none;
            border: none;
            color: #f0e0d0;
            font-size: 20px;
            cursor: pointer;
            padding: 4px;
        }
        
        .modal-body {
            font-size: 14px;
            color: #f0e0d0;
            line-height: 1.6;
        }
        
        .modal-body h3 {
            color: #daa520;
            margin: 20px 0 10px;
            font-size: 16px;
        }
        
        .modal-body p {
            margin-bottom: 12px;
        }
        
        .modal-body ul {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .modal-body li {
            margin-bottom: 6px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">隐私管理</h1>
                <div></div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 隐私协议 -->
            <section class="privacy-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-file-contract"></i>
                    </div>
                    隐私协议
                </div>
                
                <div class="privacy-item" onclick="openModal('privacy-policy')">
                    <div class="item-header">
                        <div class="item-title">隐私政策</div>
                        <div class="item-version">v2.1</div>
                    </div>
                    <div class="item-description">
                        详细说明我们如何收集、使用、存储和保护您的个人信息，以及您享有的权利�?
                    </div>
                    <div class="item-footer">
                        <div class="item-date">
                            <i class="fas fa-clock"></i>
                            <span>更新�?2024-01-15</span>
                        </div>
                        <a href="#" class="item-action">查看详情</a>
                    </div>
                </div>
                
                <div class="privacy-item" onclick="openModal('user-agreement')">
                    <div class="item-header">
                        <div class="item-title">用户协议</div>
                        <div class="item-version">v3.0</div>
                    </div>
                    <div class="item-description">
                        规定您使用本平台服务的条款和条件，包括权利义务和服务范围�?
                    </div>
                    <div class="item-footer">
                        <div class="item-date">
                            <i class="fas fa-clock"></i>
                            <span>更新�?2024-01-10</span>
                        </div>
                        <a href="#" class="item-action">查看详情</a>
                    </div>
                </div>
                
                <div class="privacy-item" onclick="openModal('data-processing')">
                    <div class="item-header">
                        <div class="item-title">数据处理说明</div>
                        <div class="item-version">v1.5</div>
                    </div>
                    <div class="item-description">
                        详细介绍数字资产交易过程中的数据处理流程和安全保障措施�?
                    </div>
                    <div class="item-footer">
                        <div class="item-date">
                            <i class="fas fa-clock"></i>
                            <span>更新�?2024-01-05</span>
                        </div>
                        <a href="#" class="item-action">查看详情</a>
                    </div>
                </div>
            </section>

            <!-- 隐私设置 -->
            <section class="privacy-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    隐私设置
                </div>
                
                <div class="settings-item">
                    <div class="settings-info">
                        <div class="settings-title">个性化推荐</div>
                        <div class="settings-description">基于您的浏览和购买记录为您推荐感兴趣的数字藏�?/div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                
                <div class="settings-item">
                    <div class="settings-info">
                        <div class="settings-title">消息推�?/div>
                        <div class="settings-description">接收预售通知、活动信息和交易状态更�?/div>
                    </div>
                    <div class="toggle-switch active" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                
                <div class="settings-item">
                    <div class="settings-info">
                        <div class="settings-title">数据分析</div>
                        <div class="settings-description">允许我们分析您的使用数据以改善产品体�?/div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                
                <div class="settings-item">
                    <div class="settings-info">
                        <div class="settings-title">第三方分�?/div>
                        <div class="settings-description">在法律允许范围内与合作伙伴分享必要信�?/div>
                    </div>
                    <div class="toggle-switch" onclick="toggleSetting(this)">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
            </section>

            <!-- 权限管理 -->
            <section class="privacy-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    权限管理
                </div>
                
                <div class="permission-list">
                    <div class="permission-item">
                        <div class="permission-info">
                            <div class="permission-name">相机权限</div>
                            <div class="permission-desc">用于身份认证和资产拍�?/div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleSetting(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="permission-item">
                        <div class="permission-info">
                            <div class="permission-name">位置信息</div>
                            <div class="permission-desc">用于展示附近的文化场馆活�?/div>
                        </div>
                        <div class="toggle-switch" onclick="toggleSetting(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="permission-item">
                        <div class="permission-info">
                            <div class="permission-name">通知权限</div>
                            <div class="permission-desc">用于推送重要消息和提醒</div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleSetting(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="permission-item">
                        <div class="permission-info">
                            <div class="permission-name">存储权限</div>
                            <div class="permission-desc">用于保存下载的数字藏�?/div>
                        </div>
                        <div class="toggle-switch active" onclick="toggleSetting(this)">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 数据管理 -->
            <section class="privacy-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    数据管理
                </div>
                
                <div class="data-summary">
                    <div class="summary-title">
                        <i class="fas fa-info-circle"></i>
                        您的数据概览
                    </div>
                    <div class="summary-content">
                        我们收集的数据包括：账户信息、交易记录、浏览历史、设备信息等�?
                        这些数据用于提供服务、改善体验和保障安全。您可以随时查看、导出或删除这些数据�?
                    </div>
                    <div class="data-actions">
                        <button class="btn btn-small btn-secondary" onclick="exportData()">
                            <i class="fas fa-download"></i> 导出数据
                        </button>
                        <button class="btn btn-small btn-secondary" onclick="deleteData()">
                            <i class="fas fa-trash"></i> 删除数据
                        </button>
                        <button class="btn btn-small btn-primary" onclick="viewDataDetails()">
                            <i class="fas fa-eye"></i> 查看详情
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 协议详情模态框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modal-title">协议详情</div>
                <button class="modal-close" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 协议内容将在这里动态加�?-->
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 切换设置开�?
        function toggleSetting(switchElement) {
            switchElement.classList.toggle('active');
            
            // 模拟保存设置
            const isActive = switchElement.classList.contains('active');
            const settingItem = switchElement.closest('.settings-item, .permission-item');
            const settingName = settingItem.querySelector('.settings-title, .permission-name').textContent;
            
            console.log(`${settingName}: ${isActive ? '开�? : '关闭'}`);
            
            // 可以在这里添加实际的API调用来保存设�?
        }

        // 协议内容数据
        const agreementContent = {
            'privacy-policy': {
                title: '隐私政策',
                content: `
                    <h3>1. 信息收集</h3>
                    <p>我们收集您在使用服务过程中主动提供或自动生成的信息，包括但不限于�?/p>
                    <ul>
                        <li>账户注册信息（手机号、邮箱、实名认证信息）</li>
                        <li>交易记录和数字资产信�?/li>
                        <li>浏览行为和偏好设�?/li>
                        <li>设备信息和日志数�?/li>
                    </ul>
                    
                    <h3>2. 信息使用</h3>
                    <p>我们使用收集的信息用于：</p>
                    <ul>
                        <li>提供和改善平台服�?/li>
                        <li>处理交易和验证身�?/li>
                        <li>个性化推荐和客户支�?/li>
                        <li>安全防护和风险控�?/li>
                    </ul>
                    
                    <h3>3. 信息保护</h3>
                    <p>我们采用行业标准的安全措施保护您的个人信息，包括数据加密、访问控制、安全审计等�?/p>
                    
                    <h3>4. 您的权利</h3>
                    <p>您有权：</p>
                    <ul>
                        <li>查看和更新个人信�?/li>
                        <li>撤回同意和注销账户</li>
                        <li>导出个人数据</li>
                        <li>投诉和举报隐私问�?/li>
                    </ul>
                `
            },
            'user-agreement': {
                title: '用户协议',
                content: `
                    <h3>1. 服务条款</h3>
                    <p>本协议规定您使用凌云数资的条款和条件。使用本服务即表示您同意遵守本协议�?/p>
                    
                    <h3>2. 账户责任</h3>
                    <p>您需要：</p>
                    <ul>
                        <li>提供真实、准确的注册信息</li>
                        <li>妥善保管账户安全</li>
                        <li>遵守平台规则和法律法�?/li>
                        <li>承担账户下所有活动的责任</li>
                    </ul>
                    
                    <h3>3. 数字资产</h3>
                    <p>数字资产相关规定�?/p>
                    <ul>
                        <li>数字资产具有唯一性和不可复制�?/li>
                        <li>购买即视为获得使用权</li>
                        <li>禁止恶意炒作和价格操�?/li>
                        <li>遵守知识产权相关法律</li>
                    </ul>
                    
                    <h3>4. 服务变更</h3>
                    <p>我们保留随时修改或终止服务的权利，重要变更将提前通知用户�?/p>
                `
            },
            'data-processing': {
                title: '数据处理说明',
                content: `
                    <h3>1. 数据处理原则</h3>
                    <p>我们遵循最小化、透明化、安全化的数据处理原则�?/p>
                    
                    <h3>2. 区块链数�?/h3>
                    <p>数字资产相关数据�?/p>
                    <ul>
                        <li>存储在去中心化区块链网络</li>
                        <li>具有不可篡改和可追溯特�?/li>
                        <li>符合国家区块链技术标�?/li>
                        <li>保护用户隐私和数据安�?/li>
                    </ul>
                    
                    <h3>3. 数据共享</h3>
                    <p>在以下情况下可能共享数据�?/p>
                    <ul>
                        <li>获得您的明确同意</li>
                        <li>法律法规要求</li>
                        <li>维护平台安全</li>
                        <li>与合作伙伴的必要业务需�?/li>
                    </ul>
                    
                    <h3>4. 数据安全</h3>
                    <p>我们采用多重安全保障措施，包括加密传输、安全存储、访问控制等�?/p>
                `
            }
        };

        // 打开协议详情模态框
        function openModal(type) {
            const modal = document.getElementById('modal');
            const title = document.getElementById('modal-title');
            const body = document.getElementById('modal-body');
            
            const content = agreementContent[type];
            if (content) {
                title.textContent = content.title;
                body.innerHTML = content.content;
                modal.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        // 关闭模态框
        function closeModal() {
            const modal = document.getElementById('modal');
            modal.classList.remove('show');
            document.body.style.overflow = '';
        }

        // 数据管理功能
        function exportData() {
            alert('数据导出功能即将开放，导出的数据将包括您的账户信息、交易记录等�?);
        }

        function deleteData() {
            if (confirm('确定要删除您的数据吗？此操作不可恢复，将清除您的所有个人信息和交易记录�?)) {
                alert('数据删除请求已提交，我们将在7个工作日内处理您的请求�?);
            }
        }

        function viewDataDetails() {
            alert('查看数据详情功能即将开放，您可以详细了解我们收集和使用的所有数据类型�?);
        }

        // 点击模态框外部关闭
        document.getElementById('modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .privacy-item, .settings-item').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 阻止协议项点击时的事件冒�?
        document.querySelectorAll('.item-action').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
            });
        });
    </script>
</body>
</html> 
