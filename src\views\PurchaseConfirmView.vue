<template>
  <div class="purchase-confirm-container" :style="{ paddingTop: '40px' }">
    <AppPageHeader :title="'确认购买信息'" @back="$router.back()" />

    <main class="main-content">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载商品信息中...</div>
        </div>

        <!-- 商品信息和购买确认 -->
        <div v-else-if="asset" class="purchase-content">
          <!-- 商品信息卡片 -->
          <section class="asset-info-card">
            <div class="asset-image">
              <img
                v-if="asset.assetCover"
                :src="asset.assetCover"
                :alt="asset.assetName"
                class="asset-cover"
              />
              <div v-else class="asset-placeholder">
                {{ asset.assetName }}
              </div>
            </div>
            <div class="asset-details">
              <h3 class="asset-name">{{ asset.assetName }}</h3>
              <div class="asset-price">
                <span class="price-label">单价：</span>
                <span class="price-value">¥{{ formatPrice(asset.issuePrice) }}</span>
              </div>
              <div v-if="asset.availableStock" class="asset-stock">
                <span class="stock-label">库存：</span>
                <span class="stock-value">{{ asset.availableStock }}份</span>
              </div>
            </div>
          </section>

          <!-- 购买数量选择 -->
          <section class="quantity-section">
            <div class="section-header">
              <h3 class="section-title">购买数量</h3>
            </div>
            <div class="quantity-selector">
              <button
                class="quantity-btn decrease"
                :disabled="purchaseQuantity <= 1"
                @click="decreaseQuantity"
              >
                <i class="fas fa-minus"></i>
              </button>
              <div class="quantity-input-wrapper">
                <input
                  v-model.number="purchaseQuantity"
                  type="number"
                  class="quantity-input"
                  min="1"
                  :max="purchaseLimit"
                  @input="validateQuantity"
                />
              </div>
              <button
                class="quantity-btn increase"
                :disabled="purchaseQuantity >= purchaseLimit"
                @click="increaseQuantity"
              >
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="quantity-tips">
              <p v-if="userIdentityLoading">正在获取限购信息...</p>
              <p v-else>{{ userIdentity === 'enterprise' ? '企业' : '个人' }}用户限购{{ purchaseLimit }}份</p>
              <p>下单后数量不可修改，请谨慎选择</p>
            </div>
          </section>

          <!-- 价格汇总 -->
          <section class="price-summary">
            <div class="summary-row">
              <span class="summary-label">商品单价</span>
              <span class="summary-value">¥{{ formatPrice(asset.issuePrice) }}</span>
            </div>
            <div class="summary-row">
              <span class="summary-label">购买数量</span>
              <span class="summary-value">{{ purchaseQuantity }}份</span>
            </div>
            <div class="summary-divider"></div>
            <div class="summary-row total">
              <span class="summary-label">合计金额</span>
              <span class="summary-value total-price">¥{{ formatPrice(totalPrice) }}</span>
            </div>
          </section>

          <!-- 购买须知 -->
          <section class="purchase-notice">
            <div class="section-header">
              <h3 class="section-title">购买须知</h3>
            </div>
            <div class="notice-content">
              <p>数字资产仅限实名认证为年满18周岁的中国大陆用户购买</p>
              <p>盲盒类产品不支持7天无理由退货，请谨慎选择</p>
              <p>请勿进行炒作、场外交易等违规行为</p>
            </div>
          </section>
        </div>

        <!-- 错误状态 -->
        <div v-else class="error-state">
          <i class="fas fa-exclamation-triangle error-icon"></i>
          <div class="error-text">商品信息加载失败</div>
          <button class="retry-btn" @click="loadAssetDetail">重试</button>
        </div>
      </div>
    </main>

    <!-- 底部固定操作区 -->
    <section class="bottom-actions">
      <div class="actions-container">
        <div class="price-display">
          <div class="total-label">合计</div>
          <div class="total-amount">¥{{ formatPrice(totalPrice) }}</div>
        </div>
        <button
          class="confirm-btn"
          :disabled="isPurchasing || !asset"
          @click="handleConfirmPurchase"
        >
          <i v-if="isPurchasing" class="fas fa-spinner fa-spin"></i>
          <span>{{ isPurchasing ? '处理中...' : '确认购买' }}</span>
        </button>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import AppPageHeader from '@/components/AppPageHeader.vue'
import { getAssetDetail } from '@/api/exhibition'
import type { AssetItemMore } from '@/api/exhibition'
import PayAPI, { type AddOrderRequest, type AddOrderResponse } from '@/api/pay'
import { UserAPI } from '@/api/user'

// 路由和通知
const router = useRouter()
const route = useRoute()
const { success, error: showError } = useNotification()

// 响应式数据
const loading = ref(false)
const isPurchasing = ref(false)
const asset = ref<AssetItemMore | null>(null)
const purchaseQuantity = ref(1) // 默认数量为1
const userIdentity = ref<'person' | 'enterprise' | null>(null) // 用户身份类型
const userIdentityLoading = ref(false) // 用户身份加载状态

// 计算属性
const assetId = computed(() => route.params.assetId as string)

// 根据用户身份计算限购数量
const purchaseLimit = computed(() => {
  if (!asset.value) return 10 // 默认限购数量

  // 如果用户身份未加载完成，使用默认值
  if (userIdentity.value === null) return 10

  // 根据用户身份返回对应的限购数量
  if (userIdentity.value === 'enterprise') {
    const limit = asset.value.enterpriseLimit || 10
    console.log('企业用户限购数量:', limit)
    return limit
  } else {
    const limit = asset.value.individualLimit || 10
    console.log('个人用户限购数量:', limit)
    return limit
  }
})

const totalPrice = computed(() => {
  if (!asset.value?.issuePrice) return 0
  const price = typeof asset.value.issuePrice === 'string'
    ? parseFloat(asset.value.issuePrice)
    : asset.value.issuePrice
  return price * purchaseQuantity.value
})

// 方法
const formatPrice = (price: number | string | undefined): string => {
  if (!price) return '0.00'
  const numPrice = typeof price === 'string' ? parseFloat(price) : price
  return numPrice.toFixed(2)
}

const decreaseQuantity = () => {
  if (purchaseQuantity.value > 1) {
    purchaseQuantity.value--
  }
}

const increaseQuantity = () => {
  if (purchaseQuantity.value < purchaseLimit.value) {
    purchaseQuantity.value++
  }
}

const validateQuantity = () => {
  if (purchaseQuantity.value < 1) {
    purchaseQuantity.value = 1
  } else if (purchaseQuantity.value > purchaseLimit.value) {
    purchaseQuantity.value = purchaseLimit.value
  }
}

/**
 * 获取用户身份信息
 */
const getUserIdentity = async () => {
  try {
    userIdentityLoading.value = true
    const response = await UserAPI.getVerificationStatus()

    if (response.code === 200 && response.data) {
      userIdentity.value = response.data.identifyType || 'person'
      console.log('用户身份类型:', userIdentity.value)
    } else {
      // 如果获取失败，默认为个人用户
      userIdentity.value = 'person'
      console.warn('获取用户身份失败，默认为个人用户')
    }
  } catch (err: unknown) {
    console.error('获取用户身份失败:', err)
    // 发生错误时，默认为个人用户
    userIdentity.value = 'person'
  } finally {
    userIdentityLoading.value = false
  }
}

/**
 * 加载资产详情
 */
const loadAssetDetail = async () => {
  if (!assetId.value) {
    showError('资产ID不存在')
    router.back()
    return
  }

  try {
    loading.value = true
    const response = await getAssetDetail(assetId.value) as {
      code: number
      msg: string
      data: AssetItemMore | null
    }

    if (response.code === 200 && response.data) {
      asset.value = response.data
    } else {
      showError(`加载商品信息失败: ${response.msg}`)
    }
  } catch (err: unknown) {
    console.error('加载商品信息失败:', err)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    const error = err as { code?: number; status?: number }
    if (error?.code === 401 || error?.status === 401) {
      asset.value = null
      return
    }

    showError('加载商品信息失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

/**
 * 确认购买
 */
const handleConfirmPurchase = async () => {
  if (!asset.value?.assetId) {
    showError('商品信息加载中，请稍后再试')
    return
  }

  // 再次检查登录状态
  if (!checkLoginStatus()) {
    return
  }

  try {
    isPurchasing.value = true

    // 构建下单参数
    const orderData: AddOrderRequest = {
      buyQuantity: purchaseQuantity.value,
      tradeType: 'issueBuy',
      assetId: String(asset.value.assetId)
    }

    console.log('🛒 开始下单:', orderData)

    // 调用下单接口
    const response = await PayAPI.addOrder(orderData)

    if (response.code === 200 && response.data) {
      console.log('✅ 下单成功:', response.data)

      // 检查返回的数据类型，进行相应处理
      let paymentData = response.data

      // 情况1：如果返回的data直接是URL字符串（新的支付方式）
      if (typeof response.data === 'string' &&
          (response.data as string).startsWith('http') &&
          !(response.data as string).includes('<form')) {
        const paymentUrl = response.data as string
        console.log('🔗 检测到API直接返回支付URL:', paymentUrl)
        // 包装成对象格式，以便统一处理
        paymentData = {
          payUrl: paymentUrl,
          orderId: '', // 可以尝试从URL参数中提取订单号
        }

        // 尝试从URL参数中提取订单号
        try {
          const url = new URL(paymentUrl)
          const orderIdFromUrl = url.searchParams.get('out_trade_no') ||
                                url.searchParams.get('orderId') ||
                                url.searchParams.get('order_id')
          if (orderIdFromUrl) {
            paymentData.orderId = orderIdFromUrl
            console.log('📋 从支付URL中提取到订单号:', paymentData.orderId)
          }
        } catch (urlError) {
          console.warn('⚠️ 解析支付URL参数失败:', urlError)
        }
      }
      // 情况2：如果返回的data直接是HTML字符串（支付宝H5支付表单）
      else if (typeof response.data === 'string' && (response.data as string).includes('<form')) {
        const htmlString = response.data as string
        console.log('🔍 检测到API直接返回HTML表单字符串')
        // 包装成对象格式，以便统一处理
        paymentData = {
          payForm: htmlString,
          orderId: '', // 尝试从HTML中提取订单号
        }

        // 尝试从HTML中提取订单号
        const orderIdMatch = htmlString.match(/out_trade_no["']\s*:\s*["'](.*?)["']/i)
        if (orderIdMatch && orderIdMatch[1]) {
          paymentData.orderId = orderIdMatch[1]
          console.log('📋 从HTML表单中提取到订单号:', paymentData.orderId)
        }
      }
      // 情况3：返回的是对象格式（标准格式）
      else if (typeof response.data === 'object') {
        console.log('📦 检测到API返回标准对象格式')
        paymentData = response.data
      }

      // 处理支付返回的数据
      await handlePaymentResponse(paymentData)

    } else {
      console.error('❌ 下单失败:', response.msg)
      showError(response.msg || '下单失败，请稍后重试')
    }
  } catch (err: unknown) {
    console.error('❌ 下单异常:', err)

    // 类型安全的错误处理
    const isAuthError = (error: unknown): boolean => {
      if (error && typeof error === 'object') {
        const e = error as { code?: number; status?: number; message?: string }
        return e.code === 401 || e.status === 401 || (e.message?.includes('认证失败') ?? false)
      }
      return false
    }

    const getErrorMessage = (error: unknown): string => {
      if (error && typeof error === 'object' && 'message' in error) {
        return (error as { message: string }).message
      }
      return '下单失败，请稍后重试'
    }

    // 如果是401认证错误，提示用户重新登录
    if (isAuthError(err)) {
      showError('登录状态已过期，请重新登录')
      // 401错误会被request.ts拦截器自动处理跳转到登录页面
      return
    }

    showError(getErrorMessage(err))
  } finally {
    isPurchasing.value = false
  }
}

/**
 * 处理支付响应数据 - 跳转到支付页面
 */
const handlePaymentResponse = async (paymentData: AddOrderResponse | string | { orderNo?: string; orderId?: string; [key: string]: unknown }) => {
  try {
    console.log('💳 处理支付响应数据:', paymentData)

    // 提取订单号
    let orderNo = ''

    // 情况1：从paymentData对象中获取订单号
    if (typeof paymentData === 'object' && paymentData !== null && 'orderId' in paymentData && paymentData.orderId) {
      orderNo = paymentData.orderId
    }
    // 情况2：从paymentData对象中获取orderNo
    else if (typeof paymentData === 'object' && paymentData !== null && 'orderNo' in paymentData && paymentData.orderNo) {
      orderNo = paymentData.orderNo
    }
    // 情况3：如果返回的是URL字符串，尝试从URL中提取订单号
    else if (typeof paymentData === 'string' && paymentData.startsWith('http')) {
      try {
        const url = new URL(paymentData)
        orderNo = url.searchParams.get('out_trade_no') ||
                  url.searchParams.get('orderId') ||
                  url.searchParams.get('order_id') ||
                  url.searchParams.get('orderNo') || ''
      } catch (urlError) {
        console.warn('⚠️ 从URL提取订单号失败:', urlError)
      }
    }
    // 情况4：如果返回的是HTML表单，尝试从HTML中提取订单号
    else if (typeof paymentData === 'string' && paymentData.includes('<form')) {
      const htmlString = paymentData
      const orderIdMatch = htmlString.match(/out_trade_no["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/orderId["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/order_id["']\s*[:=]\s*["'](.*?)["']/i) ||
                          htmlString.match(/orderNo["']\s*[:=]\s*["'](.*?)["']/i)
      if (orderIdMatch && orderIdMatch[1]) {
        orderNo = orderIdMatch[1]
      }
    }

    if (orderNo) {
      console.log('📋 提取到订单号:', orderNo)

      // 保存订单号到本地存储
      localStorage.setItem('pendingOrderId', orderNo)

      // 显示成功提示
      success('订单创建成功，正在跳转到支付页面...', 1500)

      // 跳转到支付页面，传递必要的参数
      setTimeout(() => {
        router.push({
          name: 'payment',
          params: {
            orderNo: orderNo
          },
          query: {
            assetName: asset.value?.assetName || '',
            assetPrice: asset.value?.issuePrice?.toString() || '0',
            assetCover: asset.value?.assetCover || '',
            purchaseQuantity: purchaseQuantity.value.toString(),
            totalAmount: totalPrice.value.toString(),
            from: 'purchase-confirm' // 标记来源页面
          }
        })
      }, 800)

    } else {
      console.warn('⚠️ 未能提取到订单号，使用备用处理方案')

      // 如果无法提取订单号，但创建订单成功，跳转到我的订单页面
      success('订单创建成功！请前往我的订单页面查看')

      setTimeout(() => {
        router.push('/my-orders')
      }, 1000)
    }

  } catch (error) {
    console.error('❌ 处理支付响应失败:', error)
    showError('订单创建成功，但跳转支付页面失败，请前往我的订单页面查看')

    // 发生错误时，延迟跳转到我的订单页面
    setTimeout(() => {
      router.push('/my-orders')
    }, 2000)
  }
}

/**
 * 检查登录状态
 */
const checkLoginStatus = () => {
  const token = localStorage.getItem('token')
  if (!token) {
    showError('请先登录后再进行购买')
    // 保存当前页面路径，登录后可以回到这里
    localStorage.setItem('redirectPath', route.fullPath)
    // 跳转到登录页面
    router.push('/login')
    return false
  }
  return true
}

// 生命周期
onMounted(() => {
  // 先检查登录状态，再加载商品详情和用户身份
  if (checkLoginStatus()) {
    loadAssetDetail()
    getUserIdentity()
  }
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);

  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;

  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;

  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);

  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);

  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-300: var(--text-tertiary);
  --neutral-400: var(--text-muted);
  --neutral-50: var(--bg-secondary);
  --background-primary: var(--bg-primary);
  --background-secondary: var(--bg-card);
  --shadow-gold: var(--shadow-primary);
}

.purchase-confirm-container {
  min-height: 100vh;
  background: var(--gradient-hero);
  color: var(--text-primary);
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  padding-bottom: 80px;
}

.main-content {
  padding: 20px 0;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 3px solid var(--border-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.loading-text {
  color: var(--text-tertiary);
  font-size: 16px;
  font-weight: 500;
}

/* 商品信息卡片 */
.asset-info-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  gap: 16px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.asset-info-card:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-primary);
  transform: translateY(-2px);
}

.asset-image {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
}

.asset-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.asset-cover:hover {
  transform: scale(1.05);
}

.asset-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--text-muted);
  text-align: center;
  background: var(--bg-secondary);
}

.asset-details {
  flex: 1;
  min-width: 0;
}

.asset-name {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 12px;
  line-height: 1.3;
  letter-spacing: -0.3px;
}

.asset-price {
  margin-bottom: 8px;
}

.price-label,
.stock-label {
  font-size: 14px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.price-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-left: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.stock-value {
  font-size: 14px;
  color: var(--text-secondary);
  margin-left: 8px;
  font-weight: 500;
}

/* 购买数量选择 */
.quantity-section {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.quantity-section:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-primary);
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.3px;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
}

.quantity-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.quantity-btn {
  width: 44px;
  height: 44px;
  border: 1px solid var(--border-light);
  border-radius: 12px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  box-shadow: var(--shadow-sm);
}

.quantity-btn:hover:not(:disabled) {
  border-color: var(--border-primary);
  background: var(--bg-tertiary);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-primary);
}

.quantity-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.quantity-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: var(--bg-secondary);
}

.quantity-input-wrapper {
  flex: 1;
  max-width: 120px;
}

.quantity-input {
  width: 100%;
  height: 44px;
  border: 1px solid var(--border-light);
  border-radius: 12px;
  text-align: center;
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  background: var(--bg-secondary);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.quantity-input:focus {
  outline: none;
  border-color: var(--border-primary);
  background: var(--bg-tertiary);
  box-shadow: 0 0 0 3px var(--primary-alpha), inset 0 2px 4px rgba(0, 0, 0, 0.2);
}

.quantity-tips {
  font-size: 13px;
  color: var(--text-muted);
  line-height: 1.6;
  background: rgba(212, 165, 116, 0.08);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 12px;
}

.quantity-tips p {
  margin: 0 0 4px 0;
  position: relative;
  padding-left: 12px;
}

.quantity-tips p:last-child {
  margin-bottom: 0;
}

.quantity-tips p::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--primary-color);
  font-weight: bold;
}

/* 价格汇总 */
.price-summary {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.price-summary:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-primary);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 0;
}

.summary-row:last-child {
  margin-bottom: 0;
}

.summary-label {
  font-size: 15px;
  color: var(--text-tertiary);
  font-weight: 500;
}

.summary-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 600;
}

.summary-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--border-primary), transparent);
  margin: 20px 0;
  position: relative;
}

.summary-divider::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: var(--primary-color);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--primary-color);
}

.summary-row.total {
  background: rgba(212, 165, 116, 0.08);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px;
  margin: 0;
}

.summary-row.total .summary-label {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.total-price {
  font-size: 22px !important;
  font-weight: 800 !important;
  color: var(--primary-color) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 购买须知 */
.purchase-notice {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-md);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.purchase-notice:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-primary);
}

.notice-content {
  font-size: 14px;
  color: var(--text-tertiary);
  line-height: 1.7;
}

.notice-content p {
  margin: 0 0 12px 0;
  position: relative;
  padding-left: 16px;
}

.notice-content p:last-child {
  margin-bottom: 0;
}

.notice-content p::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--accent-color);
  font-weight: bold;
  font-size: 16px;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.error-icon {
  font-size: 64px;
  color: var(--error-color);
  margin-bottom: 20px;
  opacity: 0.8;
  text-shadow: 0 2px 8px rgba(230, 57, 70, 0.3);
}

.error-text {
  font-size: 18px;
  color: var(--text-tertiary);
  margin-bottom: 32px;
  font-weight: 500;
}

.retry-btn {
  padding: 12px 24px;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-primary);
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.4);
}

.retry-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-primary);
}

/* 底部固定操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(15, 15, 21, 0.95), rgba(26, 26, 37, 0.85));
  border-top: 1px solid var(--border-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  padding: 12px 16px 16px 16px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
  z-index: 100;
}

.actions-container {
  max-width: 600px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 16px;
}

.price-display {
  flex: 0 0 auto;
  width: 120px;
  padding: 12px 16px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.total-label {
  font-size: 11px;
  color: var(--text-tertiary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  margin-bottom: 2px;
}

.total-amount {
  font-size: 18px;
  font-weight: 800;
  color: var(--primary-color);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  line-height: 1;
  text-align: center;
}

.confirm-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 14px;
  font-size: 15px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 48px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--primary-color) 100%);
  background-size: 200% 100%;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.4);
  animation: gradient-shift 3s ease infinite;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.confirm-btn:hover:not(:disabled)::before {
  left: 100%;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(200, 134, 13, 0.5);
  background-position: 100% 0;
}

.confirm-btn:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.4);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.2) !important;
  animation: none;
}

.confirm-btn:disabled:hover {
  transform: none !important;
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.2) !important;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 页面进入动画 */
.purchase-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片进入动画 */
.asset-info-card,
.quantity-section,
.price-summary,
.purchase-notice {
  animation: slideInUp 0.5s ease-out;
  animation-fill-mode: both;
}

.asset-info-card {
  animation-delay: 0.1s;
}

.quantity-section {
  animation-delay: 0.2s;
}

.price-summary {
  animation-delay: 0.3s;
}

.purchase-notice {
  animation-delay: 0.4s;
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式优化 */
@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }

  .asset-info-card,
  .quantity-section,
  .price-summary,
  .purchase-notice {
    padding: 20px 16px;
    margin-bottom: 16px;
  }

  .asset-name {
    font-size: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .quantity-selector {
    gap: 16px;
  }

  .quantity-btn {
    width: 40px;
    height: 40px;
  }

  .quantity-input {
    height: 40px;
    font-size: 16px;
  }

  .bottom-actions {
    padding: 12px 16px;
  }

  .actions-container {
    gap: 12px;
  }

  .price-display {
    width: 100px;
  }

  .total-amount {
    font-size: 16px;
  }

  .confirm-btn {
    padding: 12px 20px;
    font-size: 14px;
    min-height: 44px;
  }

  .loading-state,
  .error-state {
    padding: 60px 20px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
  }

  .error-icon {
    font-size: 48px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .asset-info-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .asset-image {
    align-self: center;
  }

  .quantity-selector {
    flex-direction: column;
    gap: 12px;
  }

  .quantity-input-wrapper {
    max-width: 100px;
  }
}
</style>
