/**
 * 资产推荐相关API
 */
import request from './request'
import type { ApiResponse } from './types'

/**
 * 推荐资产数据类型 - 根据实际API字段更新
 */
export interface RecommendationAsset {
  id: number
  assetId: string
  assetName: string
  assetType?: string             // 资产类型
  issuePrice?: number             // 发行价格
  assetCover?: string            // 封面图片
  assetKeywords?: string         // 关键词（逗号分隔）
  saleStartTime?: string         // 开售时间
  saleEndTime?: string
  inventoryNum?: number          // 未售数量
  issueQuantity?: number         // 计划销售数量
  issuers?: Array<{              // 发行方集合
    issuerName: string           // 发行方名称
    issuerLogo?: string          // 发行方logo
  }>
  isOnSale: boolean
  createTime: string
  updateTime: string
  
  // 保留旧字段以兼容
  assetPrice?: number
  assetImage?: string
  publisherName?: string
  publisherLogo?: string
  tags?: string[]
  totalQuantity?: number
  soldQuantity?: number
}

/**
 * 推荐资产列表请求参数
 */
export interface RecommendationListParams {
  recType: string  // 推荐类型：A-热门推荐
  pageNum?: number
  pageSize?: number
}

/**
 * 推荐资产API响应类型
 */
export interface RecommendationListResponse extends ApiResponse {
  data: RecommendationAsset[]
}

/**
 * 资产推荐API类
 */
class RecommendationAPI {
  /**
   * 获取推荐资产列表
   * @param params 请求参数
   * @returns 推荐资产列表
   */
  async getRecommendationList(params: RecommendationListParams): Promise<RecommendationListResponse> {
    try {
      const response = await request.get<RecommendationListResponse>('/assetRec/assetRec/client/list', params)
      return response
    } catch (error) {
      console.error('获取推荐资产列表失败:', error)
      throw error
    }
  }

  /**
   * 获取热门推荐资产
   * @param pageNum 页码，默认1
   * @param pageSize 每页大小，默认10
   * @returns 热门推荐资产列表
   */
  async getHotRecommendations(pageNum: number = 1, pageSize: number = 10): Promise<RecommendationListResponse> {
    return this.getRecommendationList({
      recType: 'A',
      pageNum,
      pageSize
    })
  }
}

// 导出单例
export default new RecommendationAPI() 