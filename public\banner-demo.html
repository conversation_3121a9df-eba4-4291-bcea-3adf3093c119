<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>凌云数资 - 科幻Banner预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0F0F15;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            color: #E8A317;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: bold;
        }
        
        .banner-container {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            margin-bottom: 30px;
            border: 1px solid rgba(232, 163, 23, 0.3);
        }
        
        .description {
            color: #94A3B8;
            text-align: center;
            margin-top: 20px;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(26, 26, 37, 0.9);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(232, 163, 23, 0.3);
        }
        
        .feature-title {
            color: #E8A317;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #94A3B8;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="title">凌云数资 - 科幻Banner预览</h1>
        
        <div class="banner-container">
            <object data="images/lingyun-banner.svg" type="image/svg+xml" style="width: 100%; height: 400px;">
                您的浏览器不支持SVG
            </object>
        </div>

        <p class="description">
            这是专为"凌云数资"平台设计的科幻风格动画banner，采用SVG技术实现了复杂的动画效果，
            包括星空背景、数据流动、几何图形旋转等视觉元素，完美展现了数字资产平台的科技感和未来感。
        </p>

        <div class="features">
            <div class="feature-card">
                <div class="feature-title">🎨 视觉设计</div>
                <div class="feature-description">
                    采用深蓝渐变背景，金色主标题，青色副标题，营造出深邃的科技氛围
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">✨ 动画效果</div>
                <div class="feature-description">
                    星空闪烁、数据流动、几何图形旋转、粒子浮动，多层次动画创造沉浸感
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🔥 发光特效</div>
                <div class="feature-description">
                    主标题使用双层发光效果，副标题带有阴影，整体呈现梦幻科幻质感
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">📱 响应式</div>
                <div class="feature-description">
                    SVG矢量格式确保在任何屏幕尺寸下都能保持清晰，完美适配移动端
                </div>
            </div>
        </div>
    </div>
</body>
</html> 