<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深色金黄主题配色预览 - 中国文化传媒风格</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 深色金黄主题配色方案 - 基于参考图片设计 */
        :root {
            /* 主色系 - 优雅金黄 */
            --primary-color: #d4a574;           /* 主色：优雅金黄 */
            --primary-dark: #b8956a;            /* 主色深色：深金黄 */
            --primary-light: #e8c49a;           /* 主色浅色：浅金黄 */
            --primary-alpha: rgba(212, 165, 116, 0.15); /* 主色透明 */
            
            /* 辅助色系 - 温暖橙色 */
            --accent-color: #f4a261;            /* 辅助色：温暖橙色 */
            --accent-dark: #e76f51;             /* 辅助色深色：深橙红 */
            --accent-light: #f9c74f;            /* 辅助色浅色：浅橙黄 */
            --accent-alpha: rgba(244, 162, 97, 0.15); /* 辅助色透明 */
            
            /* 功能色系 - 深色主题适配 */
            --success-color: #90a955;           /* 成功色：橄榄绿 */
            --warning-color: #f9844a;           /* 警告色：暖橙 */
            --error-color: #e63946;             /* 错误色：红色 */
            --info-color: #457b9d;              /* 信息色：蓝色 */
            
            /* 文字颜色系统 - 深色主题 */
            --text-primary: #f8f6f0;            /* 主要文字：米白色 */
            --text-secondary: #e0ddd4;          /* 次要文字：浅米色 */
            --text-tertiary: #c4c0b1;           /* 三级文字：暖灰色 */
            --text-muted: #a39d8e;              /* 弱化文字：深暖灰 */
            --text-inverse: #2d2a24;            /* 反色文字：深棕色 */
            
            /* 背景色系统 - 深色渐变 */
            --bg-primary: #1a1a1a;              /* 主背景：深灰色 */
            --bg-secondary: #242424;            /* 次背景：中灰色 */
            --bg-tertiary: #2e2e2e;             /* 三级背景：浅灰色 */
            --bg-card: rgba(42, 42, 42, 0.9);   /* 卡片背景：半透明深灰 */
            --bg-card-hover: rgba(50, 50, 50, 0.95); /* 卡片悬浮：半透明中灰 */
            
            /* 边框和分割线 */
            --border-primary: rgba(212, 165, 116, 0.3);   /* 主边框 */
            --border-secondary: rgba(244, 162, 97, 0.25); /* 次边框 */
            --border-light: rgba(248, 246, 240, 0.1);     /* 浅边框 */
            --border-hover: rgba(212, 165, 116, 0.5);     /* 悬浮边框 */
            --divider: rgba(248, 246, 240, 0.08);         /* 分割线 */
            
            /* 阴影系统 - 深色主题适配 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
            --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
            --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
            
            /* 渐变系统 */
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
            --gradient-warm: linear-gradient(135deg, var(--accent-light) 0%, var(--primary-color) 50%, var(--accent-color) 100%);
            --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
            
            /* 特殊效果渐变 */
            --gradient-gold-shine: linear-gradient(90deg, transparent 0%, rgba(212, 165, 116, 0.6) 50%, transparent 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-bg);
            color: var(--text-secondary);
            line-height: 1.6;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 20px;
            background: var(--bg-card);
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gradient-primary);
        }

        .header h1 {
            color: var(--text-primary);
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
            max-width: 600px;
            margin: 0 auto;
        }

        .color-section {
            margin-bottom: 40px;
            background: var(--bg-card);
            border-radius: 16px;
            padding: 30px;
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(20px);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
        }

        .section-title {
            color: var(--text-primary);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .section-title i {
            color: var(--accent-color);
            text-shadow: 0 0 10px var(--accent-color);
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .color-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid var(--border-light);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .color-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--border-hover);
        }

        .color-sample {
            width: 100%;
            height: 80px;
            border-radius: 8px;
            margin-bottom: 12px;
            border: 1px solid var(--border-primary);
            position: relative;
            overflow: hidden;
        }

        .color-sample::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-gold-shine);
            animation: shine 4s infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            50% { left: -100%; }
            100% { left: 100%; }
        }

        .color-name {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .color-value {
            font-family: 'SF Mono', Monaco, 'Consolas', monospace;
            font-size: 12px;
            color: var(--text-tertiary);
            background: var(--bg-tertiary);
            padding: 4px 8px;
            border-radius: 4px;
        }

        .components-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .demo-card {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
        }

        .demo-title {
            color: var(--text-primary);
            font-weight: 600;
            margin-bottom: 16px;
        }

        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 4px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-gold-shine);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-inverse);
            box-shadow: var(--shadow-primary);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        .btn-secondary {
            background: var(--bg-card);
            color: var(--text-secondary);
            border: 1px solid var(--border-primary);
        }

        .btn-secondary:hover {
            background: var(--bg-card-hover);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }

        .btn-accent {
            background: var(--gradient-accent);
            color: var(--text-inverse);
        }

        .btn-accent:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(244, 162, 97, 0.4);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            backdrop-filter: blur(10px);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: var(--text-inverse);
        }

        .btn-ghost {
            background: rgba(212, 165, 116, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(212, 165, 116, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-ghost:hover {
            background: rgba(212, 165, 116, 0.2);
            border-color: var(--primary-color);
        }

        /* 标签样式 */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 4px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .tag-primary {
            background: var(--primary-alpha);
            color: var(--primary-color);
            border: 1px solid rgba(212, 165, 116, 0.3);
        }

        .tag-success {
            background: rgba(144, 169, 85, 0.15);
            color: var(--success-color);
            border: 1px solid rgba(144, 169, 85, 0.3);
        }

        .tag-warning {
            background: rgba(249, 132, 74, 0.15);
            color: var(--warning-color);
            border: 1px solid rgba(249, 132, 74, 0.3);
        }

        .tag-error {
            background: rgba(230, 57, 70, 0.15);
            color: var(--error-color);
            border: 1px solid rgba(230, 57, 70, 0.3);
        }

        .tag-info {
            background: rgba(69, 123, 157, 0.15);
            color: var(--info-color);
            border: 1px solid rgba(69, 123, 157, 0.3);
        }

        .tag:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        /* 输入框样式 */
        .input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-primary);
            border-radius: 8px;
            background: var(--bg-card);
            color: var(--text-primary);
            font-size: 14px;
            transition: all 0.3s ease;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
        }

        .input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--primary-alpha);
        }

        .input::placeholder {
            color: var(--text-muted);
        }

        /* 文字样式展示 */
        .text-demo h3 {
            color: var(--text-primary);
            font-size: 18px;
            margin-bottom: 8px;
        }

        .text-demo p {
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .text-demo small {
            color: var(--text-tertiary);
            font-size: 12px;
        }

        .text-muted-demo {
            color: var(--text-muted);
        }

        .gradient-demo {
            height: 100px;
            border-radius: 12px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-inverse);
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        .gradient-demo::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-gold-shine);
            animation: shine 3s infinite;
        }

        .gradient-primary {
            background: var(--gradient-primary);
        }

        .gradient-accent {
            background: var(--gradient-accent);
        }

        .gradient-warm {
            background: var(--gradient-warm);
        }

        .gradient-hero {
            background: var(--gradient-hero);
            color: var(--primary-color);
        }

        /* 特殊效果展示 */
        .effect-demo {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 12px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .glass-effect {
            backdrop-filter: blur(20px);
            background: rgba(42, 42, 42, 0.8);
            border: 1px solid rgba(248, 246, 240, 0.1);
        }

        .glow-effect {
            background: var(--bg-card);
            border: 1px solid var(--primary-color);
            box-shadow: 0 0 20px rgba(212, 165, 116, 0.3);
        }

        .shine-effect {
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            position: relative;
            overflow: hidden;
        }

        .shine-effect::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-gold-shine);
            animation: shine 3s infinite;
        }

        /* 仿原图样式元素 */
        .app-preview {
            background: var(--gradient-hero);
            border-radius: 16px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid var(--border-primary);
            backdrop-filter: blur(20px);
        }

        .app-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .app-logo {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .app-actions {
            display: flex;
            gap: 10px;
        }

        .search-bar {
            background: rgba(42, 42, 42, 0.6);
            border: 1px solid var(--border-primary);
            border-radius: 20px;
            padding: 12px 20px;
            color: var(--text-secondary);
            width: 100%;
            margin: 20px 0;
            backdrop-filter: blur(10px);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .feature-item {
            background: var(--bg-card);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid var(--border-light);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            transform: translateY(-2px);
            border-color: var(--primary-color);
            box-shadow: 0 4px 16px rgba(212, 165, 116, 0.2);
        }

        .feature-icon {
            font-size: 24px;
            color: var(--accent-color);
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .color-grid {
                grid-template-columns: 1fr;
            }
            
            .components-demo {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .feature-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1><i class="fas fa-palette"></i> 深色金黄主题配色预览</h1>
            <p>基于中国文化传媒风格设计的深色主题配色方案，融合优雅金黄与现代深色背景</p>
        </div>

        <!-- 应用风格预览 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-mobile-alt"></i>
                应用风格预览
            </h2>
            <div class="app-preview">
                <div class="app-header">
                    <div class="app-logo">
                        <i class="fas fa-sun"></i>
                        <span>文旅中国 · 数字资产平台</span>
                    </div>
                    <div class="app-actions">
                        <i class="fas fa-calendar-alt" style="color: var(--primary-color);"></i>
                        <i class="fas fa-bell" style="color: var(--text-secondary);"></i>
                    </div>
                </div>
                <input type="text" class="search-bar" placeholder="搜索藏品/动态/社区/用户">
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-gem"></i></div>
                        <div style="color: var(--text-primary);">数字藏品</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-store"></i></div>
                        <div style="color: var(--text-primary);">积分商城</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-cube"></i></div>
                        <div style="color: var(--text-primary);">数字空间</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon"><i class="fas fa-crown"></i></div>
                        <div style="color: var(--text-primary);">专题精选</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主色系 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-star"></i>
                主色系 - 优雅金黄
            </h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-sample" style="background: var(--primary-color);"></div>
                    <div class="color-name">主色</div>
                    <div class="color-value">#d4a574</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--primary-dark);"></div>
                    <div class="color-name">主色深色</div>
                    <div class="color-value">#b8956a</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--primary-light);"></div>
                    <div class="color-name">主色浅色</div>
                    <div class="color-value">#e8c49a</div>
                </div>
            </div>
        </div>

        <!-- 辅助色系 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-fire"></i>
                辅助色系 - 温暖橙色
            </h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-sample" style="background: var(--accent-color);"></div>
                    <div class="color-name">辅助色</div>
                    <div class="color-value">#f4a261</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--accent-dark);"></div>
                    <div class="color-name">辅助色深色</div>
                    <div class="color-value">#e76f51</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--accent-light);"></div>
                    <div class="color-name">辅助色浅色</div>
                    <div class="color-value">#f9c74f</div>
                </div>
            </div>
        </div>

        <!-- 功能色系 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-tools"></i>
                功能色系
            </h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-sample" style="background: var(--success-color);"></div>
                    <div class="color-name">成功色</div>
                    <div class="color-value">#90a955</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--warning-color);"></div>
                    <div class="color-name">警告色</div>
                    <div class="color-value">#f9844a</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--error-color);"></div>
                    <div class="color-name">错误色</div>
                    <div class="color-value">#e63946</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--info-color);"></div>
                    <div class="color-name">信息色</div>
                    <div class="color-value">#457b9d</div>
                </div>
            </div>
        </div>

        <!-- 文字颜色 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-font"></i>
                文字颜色系统
            </h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-sample" style="background: var(--text-primary);"></div>
                    <div class="color-name">主要文字</div>
                    <div class="color-value">#f8f6f0</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--text-secondary);"></div>
                    <div class="color-name">次要文字</div>
                    <div class="color-value">#e0ddd4</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--text-tertiary);"></div>
                    <div class="color-name">三级文字</div>
                    <div class="color-value">#c4c0b1</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--text-muted);"></div>
                    <div class="color-name">弱化文字</div>
                    <div class="color-value">#a39d8e</div>
                </div>
            </div>
        </div>

        <!-- 背景色系 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-layer-group"></i>
                背景色系统
            </h2>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-sample" style="background: var(--bg-primary);"></div>
                    <div class="color-name">主背景</div>
                    <div class="color-value">#1a1a1a</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--bg-secondary);"></div>
                    <div class="color-name">次背景</div>
                    <div class="color-value">#242424</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--bg-tertiary);"></div>
                    <div class="color-name">三级背景</div>
                    <div class="color-value">#2e2e2e</div>
                </div>
                <div class="color-card">
                    <div class="color-sample" style="background: var(--bg-card);"></div>
                    <div class="color-name">卡片背景</div>
                    <div class="color-value">rgba(42,42,42,0.9)</div>
                </div>
            </div>
        </div>

        <!-- 组件演示 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-cubes"></i>
                组件演示
            </h2>
            <div class="components-demo">
                <!-- 按钮演示 -->
                <div class="demo-card">
                    <div class="demo-title">按钮样式</div>
                    <button class="btn btn-primary">主要按钮</button>
                    <button class="btn btn-secondary">次要按钮</button>
                    <button class="btn btn-accent">强调按钮</button>
                    <button class="btn btn-outline">轮廓按钮</button>
                    <button class="btn btn-ghost">幽灵按钮</button>
                </div>

                <!-- 标签演示 -->
                <div class="demo-card">
                    <div class="demo-title">标签样式</div>
                    <span class="tag tag-primary">主要标签</span>
                    <span class="tag tag-success">成功标签</span>
                    <span class="tag tag-warning">警告标签</span>
                    <span class="tag tag-error">错误标签</span>
                    <span class="tag tag-info">信息标签</span>
                </div>

                <!-- 输入框演示 -->
                <div class="demo-card">
                    <div class="demo-title">输入框样式</div>
                    <input type="text" class="input" placeholder="请输入内容...">
                    <input type="email" class="input" placeholder="请输入邮箱地址">
                </div>

                <!-- 文字演示 -->
                <div class="demo-card text-demo">
                    <div class="demo-title">文字层级</div>
                    <h3>主要标题文字</h3>
                    <p>这是次要文字内容，用于正文段落显示。</p>
                    <small>这是三级文字，用于辅助信息显示。</small>
                    <p class="text-muted-demo">这是弱化文字，用于不重要的信息。</p>
                </div>

                <!-- 渐变演示 -->
                <div class="demo-card">
                    <div class="demo-title">渐变效果</div>
                    <div class="gradient-demo gradient-primary">主色渐变</div>
                    <div class="gradient-demo gradient-accent">辅助色渐变</div>
                    <div class="gradient-demo gradient-warm">温暖渐变</div>
                    <div class="gradient-demo gradient-hero">背景渐变</div>
                </div>

                <!-- 特殊效果演示 -->
                <div class="demo-card">
                    <div class="demo-title">特殊效果</div>
                    <div class="effect-demo glass-effect">毛玻璃效果</div>
                    <div class="effect-demo glow-effect">发光效果</div>
                    <div class="effect-demo shine-effect">光泽效果</div>
                </div>
            </div>
        </div>

        <!-- 配色说明 -->
        <div class="color-section">
            <h2 class="section-title">
                <i class="fas fa-info-circle"></i>
                配色方案说明
            </h2>
            <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; backdrop-filter: blur(10px);">
                <h3 style="color: var(--text-primary); margin-bottom: 16px;">设计理念</h3>
                <p style="margin-bottom: 12px;">
                    <strong>深色优雅：</strong>以深色背景为基础，营造专业、现代的视觉氛围。
                </p>
                <p style="margin-bottom: 12px;">
                    <strong>金黄奢华：</strong>主色选用优雅的金黄色调，彰显文化底蕴与高端品质。
                </p>
                <p style="margin-bottom: 12px;">
                    <strong>橙色活力：</strong>辅助色采用温暖橙色系，增添活力与亲和力。
                </p>
                <p style="margin-bottom: 12px;">
                    <strong>毛玻璃效果：</strong>大量使用backdrop-filter实现现代化的毛玻璃视觉效果。
                </p>
                <p>
                    <strong>动态光效：</strong>添加金色光泽扫过动画，提升交互体验的高级感。
                </p>
            </div>
        </div>
    </div>
</body>
</html> 