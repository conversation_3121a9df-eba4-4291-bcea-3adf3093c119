<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - 凌云数资</title>
    
    <style>
        /* 引用完整的色彩系统 */
        :root {
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            --gradient-tech: linear-gradient(45deg, rgba(124, 58, 237, 0.1) 0%, rgba(200, 134, 13, 0.1) 50%, rgba(124, 58, 237, 0.1) 100%);
            
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
            --shadow-glow: 0 0 20px rgba(124, 58, 237, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-tech);
            animation: float 8s ease-in-out infinite;
            z-index: -1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }
        
        .tech-particles {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--primary-gold);
            border-radius: 50%;
            animation: particle-float 6s linear infinite;
        }
        
        @keyframes particle-float {
            0% { transform: translateY(100vh) scale(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) scale(1); opacity: 0; }
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .header {
            padding: 20px 0;
            text-align: center;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: var(--gradient-card);
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            padding: 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .back-btn:hover {
            transform: translateY(-50%) scale(1.05);
            box-shadow: var(--shadow-gold);
        }
        
        .logo-section {
            text-align: center;
            padding: 30px 0 40px;
            position: relative;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin-bottom: 16px;
            box-shadow: var(--shadow-gold), var(--shadow-glow);
            position: relative;
            overflow: hidden;
            animation: logo-pulse 3s ease-in-out infinite;
        }
        
        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shine 2s linear infinite;
        }
        
        @keyframes logo-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .welcome-text {
            font-size: 22px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            color: var(--neutral-400);
        }
        
        .register-form {
            background: var(--gradient-card);
            border-radius: 24px;
            padding: 32px;
            margin-bottom: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }
        
        .register-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-secondary);
            opacity: 0.5;
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-200);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--neutral-100);
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 3px var(--primary-gold-alpha), var(--shadow-gold);
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }
        
        .form-input.error {
            border-color: var(--accent-red);
            box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }
        
        .form-input::placeholder {
            color: var(--neutral-400);
        }
        
        .input-wrapper {
            position: relative;
        }
        
        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--neutral-400);
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .password-toggle:hover {
            color: var(--primary-gold);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .sms-container {
            display: flex;
            gap: 12px;
            align-items: stretch;
        }
        
        .sms-container .form-input {
            flex: 1;
        }
        
        .sms-btn {
            background: var(--gradient-secondary);
            border: none;
            color: white;
            padding: 16px 20px;
            border-radius: 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-purple);
            position: relative;
            overflow: hidden;
        }
        
        .sms-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .sms-btn:hover::before {
            left: 100%;
        }
        
        .sms-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
        }
        
        .sms-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .password-strength {
            margin-top: 8px;
            display: flex;
            gap: 4px;
            align-items: center;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: rgba(255, 255, 255, 0.1);
            flex: 1;
            transition: all 0.3s ease;
        }
        
        .strength-bar.active {
            background: var(--accent-green);
        }
        
        .strength-bar.medium {
            background: var(--accent-orange);
        }
        
        .strength-bar.weak {
            background: var(--accent-red);
        }
        
        .strength-text {
            font-size: 12px;
            color: var(--neutral-400);
            margin-left: 8px;
        }
        
        .error-message {
            color: var(--accent-red);
            font-size: 12px;
            margin-top: 6px;
            display: none;
        }
        
        .error-message.show {
            display: block;
        }
        
        .btn {
            display: inline-block;
            padding: 16px 24px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            margin: 8px 0;
            width: 100%;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(200, 134, 13, 0.4);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .agreement {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin: 24px 0;
            font-size: 14px;
            color: var(--neutral-400);
        }
        
        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .checkbox.checked {
            background: var(--gradient-primary);
            border-color: var(--primary-gold);
        }
        
        .checkbox.checked::after {
            content: '✓';
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .agreement a {
            color: var(--secondary-purple-light);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .agreement a:hover {
            color: var(--secondary-purple);
            text-decoration: underline;
        }
        
        .login-prompt {
            text-align: center;
            padding: 24px 32px;
            background: var(--gradient-card);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
        }
        
        .login-prompt p {
            color: var(--neutral-400);
            font-size: 14px;
            margin-bottom: 12px;
        }
        
        .login-prompt a {
            color: var(--secondary-purple-light);
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .login-prompt a:hover {
            color: var(--secondary-purple);
            text-decoration: underline;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 16px;
            }
            
            .logo-section {
                padding: 20px 0 30px;
            }
            
            .logo {
                width: 70px;
                height: 70px;
                font-size: 28px;
            }
            
            .welcome-text {
                font-size: 20px;
            }
            
            .register-form {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="tech-particles" id="techParticles"></div>
    
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 style="font-size: 18px; font-weight: 600; color: var(--neutral-100);">注册</h1>
        </div>
        
        <div class="logo-section">
            <div class="logo">
                <i class="fas fa-sun"></i>
            </div>
            <div class="welcome-text">创建账户</div>
            <div class="subtitle">加入凌云数资</div>
        </div>
        
        <div class="register-form">
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-mobile-alt"></i>
                    手机号码
                </label>
                <div class="input-wrapper">
                    <input 
                        type="tel" 
                        class="form-input" 
                        placeholder="请输入手机号码"
                        maxlength="11"
                        id="phoneNumber"
                        required
                    >
                    <div class="error-message" id="phoneError">请输入正确的手机号码</div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-shield-alt"></i>
                    短信验证码
                </label>
                <div class="sms-container">
                    <input 
                        type="text" 
                        class="form-input" 
                        placeholder="请输入验证码"
                        maxlength="6"
                        id="smsCode"
                        required
                    >
                    <button type="button" class="sms-btn" id="sendSmsBtn" onclick="sendSMS()">
                        获取验证码
                    </button>
                </div>
                <div class="error-message" id="smsError">请输入6位验证码</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-lock"></i>
                    设置密码
                </label>
                <div class="input-wrapper">
                    <input 
                        type="password" 
                        class="form-input" 
                        placeholder="请输入密码（8-12位，包含大小写字母和数字）"
                        id="password"
                        required
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="passwordIcon"></i>
                    </button>
                </div>
                <div class="password-strength" id="passwordStrength">
                    <div class="strength-bar"></div>
                    <div class="strength-bar"></div>
                    <div class="strength-bar"></div>
                    <div class="strength-bar"></div>
                    <span class="strength-text">密码强度</span>
                </div>
                <div class="error-message" id="passwordError">密码长度为6-20位，建议包含数字和字母</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">
                    <i class="fas fa-lock"></i>
                    确认密码
                </label>
                <div class="input-wrapper">
                    <input 
                        type="password" 
                        class="form-input" 
                        placeholder="请再次输入密码"
                        id="confirmPassword"
                        required
                    >
                    <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                        <i class="fas fa-eye" id="confirmPasswordIcon"></i>
                    </button>
                </div>
                <div class="error-message" id="confirmPasswordError">两次密码输入不一致</div>
            </div>
            
            <div class="agreement">
                <div class="checkbox" id="agreementCheckbox" onclick="toggleAgreement()"></div>
                <div>
                    我已阅读并同意
                    <a href="#" onclick="showUserAgreement()">《用户服务协议》</a>
                    和
                    <a href="#" onclick="showPrivacyPolicy()">《隐私政策》</a>
                </div>
            </div>
            
            <button type="button" class="btn btn-primary" id="registerBtn" onclick="handleRegister()">
                <i class="fas fa-user-plus"></i> 立即注册
            </button>
        </div>
        
        <div class="login-prompt">
            <p>已有账户？</p>
            <a href="login.html">立即登录 <i class="fas fa-arrow-right"></i></a>
        </div>
    </div>

    <script>
        // 创建科技粒子效果
        function createTechParticles() {
            const container = document.getElementById('techParticles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                container.appendChild(particle);
            }
        }
        
        document.addEventListener('DOMContentLoaded', createTechParticles);
        
        let smsCountdown = 0;
        let isAgreed = false;
        
        function sendSMS() {
            const phone = document.getElementById('phoneNumber').value;
            
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                showFieldError('phoneNumber', 'phoneError');
                return;
            }
            
            if (smsCountdown > 0) return;
            
            showNotification('验证码已发送到您的手机', 'success');
            
            const smsBtn = document.getElementById('sendSmsBtn');
            smsCountdown = 60;
            smsBtn.disabled = true;
            
            const timer = setInterval(() => {
                if (smsCountdown <= 0) {
                    clearInterval(timer);
                    smsBtn.disabled = false;
                    smsBtn.innerHTML = '获取验证码';
                } else {
                    smsBtn.innerHTML = `${smsCountdown}s后重发`;
                    smsCountdown--;
                }
            }, 1000);
        }
        
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const passwordIcon = document.getElementById(inputId + 'Icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        function toggleAgreement() {
            const checkbox = document.getElementById('agreementCheckbox');
            isAgreed = !isAgreed;
            
            if (isAgreed) {
                checkbox.classList.add('checked');
            } else {
                checkbox.classList.remove('checked');
            }
        }
        
        function checkPasswordStrength(password) {
            const strengthBars = document.querySelectorAll('.strength-bar');
            const strengthText = document.querySelector('.strength-text');
            
            let strength = 0;
            
            if (password.length >= 6) strength++;
            if (password.length >= 8) strength++;
            if (/[a-zA-Z]/.test(password) && /\d/.test(password)) strength++;
            if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;
            
            strengthBars.forEach((bar, index) => {
                bar.classList.remove('active', 'medium', 'weak');
                if (index < strength) {
                    if (strength <= 1) bar.classList.add('weak');
                    else if (strength <= 2) bar.classList.add('medium');
                    else bar.classList.add('active');
                }
            });
            
            const strengthTexts = ['太弱', '较弱', '中等', '强'];
            strengthText.textContent = password ? strengthTexts[strength - 1] || '太弱' : '密码强度';
        }
        
        function showFieldError(inputId, errorId) {
            const input = document.getElementById(inputId);
            const error = document.getElementById(errorId);
            
            input.classList.add('error');
            error.classList.add('show');
            
            setTimeout(() => {
                input.classList.remove('error');
                error.classList.remove('show');
            }, 3000);
        }
        
        function validateForm() {
            const phone = document.getElementById('phoneNumber').value;
            const smsCode = document.getElementById('smsCode').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            let isValid = true;
            
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                showFieldError('phoneNumber', 'phoneError');
                isValid = false;
            }
            
            if (!smsCode || smsCode.length !== 6) {
                showFieldError('smsCode', 'smsError');
                isValid = false;
            }
            
            if (!password || password.length < 6 || password.length > 20) {
                showFieldError('password', 'passwordError');
                isValid = false;
            }
            
            if (password !== confirmPassword) {
                showFieldError('confirmPassword', 'confirmPasswordError');
                isValid = false;
            }
            
            if (!isAgreed) {
                showNotification('请阅读并同意用户协议', 'error');
                isValid = false;
            }
            
            return isValid;
        }
        
        function handleRegister() {
            if (!validateForm()) return;
            
            const btn = document.getElementById('registerBtn');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            btn.disabled = true;
            
            setTimeout(() => {
                showNotification('注册成功！即将跳转到登录页面', 'success');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
            }, 2000);
        }
        
        function showUserAgreement() {
            showNotification('正在加载用户服务协议...', 'info');
        }
        
        function showPrivacyPolicy() {
            showNotification('正在加载隐私政策...', 'info');
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                ${message}
            `;
            
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--accent-green)' : type === 'error' ? 'var(--accent-red)' : 'var(--secondary-purple)'};
                color: white;
                padding: 16px 20px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
                backdrop-filter: blur(20px);
                display: flex;
                align-items: center;
                gap: 8px;
                max-width: 300px;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        // 实时验证和交互效果
        document.getElementById('phoneNumber').addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });
        
        document.getElementById('smsCode').addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });
        
        document.getElementById('password').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
        
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.01)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html> 
