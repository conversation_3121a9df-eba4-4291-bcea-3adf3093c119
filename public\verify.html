<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>实名认证 - 四川省数字资产发行平�?/title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .text-large {
            font-size: 24px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 12px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 14px 24px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 8px 0;
            width: 100%;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 4px 12px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(218, 165, 32, 0.6);
            background: linear-gradient(135deg, #e6b422, #c99a10);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        /* 页面特定样式 */
        .header {
            padding: 20px 0;
            text-align: center;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #daa520;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: #f8f0e0;
        }
        
        .verify-header {
            text-align: center;
            padding: 30px 0 40px;
        }
        
        .verify-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #daa520, #b8860b);
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: #2a1616;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(218, 165, 32, 0.3);
        }
        
        .verify-title {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin-bottom: 8px;
        }
        
        .verify-subtitle {
            font-size: 14px;
            color: rgba(240, 224, 208, 0.7);
            line-height: 1.5;
        }
        
        .verify-content {
            max-width: 360px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 40px;
            gap: 12px;
        }
        
        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }
        
        .step-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(218, 165, 32, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: rgba(240, 224, 208, 0.5);
            transition: all 0.3s;
            margin-bottom: 8px;
        }
        
        .step.active .step-circle {
            background: #daa520;
            color: #2a1616;
            transform: scale(1.1);
        }
        
        .step.completed .step-circle {
            background: #8fbc8f;
            color: #f8f0e0;
        }
        
        .step-label {
            font-size: 11px;
            color: rgba(240, 224, 208, 0.6);
            text-align: center;
        }
        
        .step.active .step-label {
            color: #daa520;
            font-weight: 600;
        }
        
        .step.completed .step-label {
            color: #8fbc8f;
        }
        
        .step-line {
            position: absolute;
            top: 16px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: rgba(218, 165, 32, 0.2);
            z-index: -1;
        }
        
        .step:last-child .step-line {
            display: none;
        }
        
        .step-content {
            display: none;
        }
        
        .step-content.active {
            display: block;
            animation: fadeInUp 0.4s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .step-title {
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 16px;
        }
        
        .step-description {
            text-align: center;
            font-size: 14px;
            color: rgba(240, 224, 208, 0.7);
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #f0e0d0;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 20px;
            background: rgba(60, 40, 30, 0.5);
            border: 1px solid rgba(218, 165, 32, 0.2);
            border-radius: 12px;
            color: #f0e0d0;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
            box-shadow: 0 0 0 3px rgba(218, 165, 32, 0.2);
        }
        
        .form-input::placeholder {
            color: rgba(240, 224, 208, 0.5);
        }
        
        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(218, 165, 32, 0.6);
            font-size: 18px;
        }
        
        .form-input.with-icon {
            padding-left: 56px;
        }
        
        .camera-section {
            text-align: center;
            margin: 32px 0;
        }
        
        .camera-frame {
            width: 280px;
            height: 200px;
            background: rgba(60, 40, 30, 0.5);
            border: 2px dashed rgba(218, 165, 32, 0.4);
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .camera-frame:hover {
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
        }
        
        .camera-icon {
            font-size: 48px;
            color: rgba(218, 165, 32, 0.6);
            margin-bottom: 12px;
        }
        
        .camera-text {
            color: rgba(240, 224, 208, 0.7);
            font-size: 14px;
            line-height: 1.4;
        }
        
        .id-preview {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 10px;
        }
        
        .upload-progress {
            display: none;
            margin-top: 16px;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #daa520, #b8860b);
            width: 0%;
            transition: width 0.3s;
        }
        
        .progress-text {
            text-align: center;
            font-size: 12px;
            color: #daa520;
        }
        
        .verify-tips {
            background: rgba(218, 165, 32, 0.1);
            border: 1px solid rgba(218, 165, 32, 0.3);
            border-radius: 12px;
            padding: 16px;
            margin: 24px 0;
        }
        
        .tips-title {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
            color: #daa520;
        }
        
        .tips-title i {
            margin-right: 8px;
        }
        
        .tips-list {
            font-size: 13px;
            color: rgba(240, 224, 208, 0.8);
            line-height: 1.5;
        }
        
        .tips-list li {
            margin-bottom: 6px;
            padding-left: 16px;
            position: relative;
        }
        
        .tips-list li:before {
            content: "�?;
            color: #daa520;
            position: absolute;
            left: 0;
        }
        
        .success-section {
            text-align: center;
            padding: 40px 20px;
        }
        
        .success-icon {
            font-size: 80px;
            color: #8fbc8f;
            margin-bottom: 24px;
            animation: successPulse 2s infinite;
        }
        
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        .success-title {
            font-size: 22px;
            font-weight: 700;
            color: #8fbc8f;
            margin-bottom: 12px;
        }
        
        .success-description {
            font-size: 14px;
            color: rgba(240, 224, 208, 0.7);
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .verify-result {
            background: rgba(143, 188, 143, 0.1);
            border: 1px solid rgba(143, 188, 143, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
        }
        
        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
        }
        
        .result-item:last-child {
            margin-bottom: 0;
        }
        
        .result-label {
            color: rgba(240, 224, 208, 0.8);
        }
        
        .result-value {
            color: #8fbc8f;
            font-weight: 600;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 32px;
        }
        
        .btn-group .btn {
            flex: 1;
        }
        
        .hidden-input {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <button class="back-btn" onclick="history.back()">
            <i class="fas fa-arrow-left"></i>
        </button>
        <h1 class="text-medium text-primary">实名认证</h1>
    </header>

    <main>
        <div class="container">
            <!-- 认证头部 -->
            <div class="verify-header">
                <div class="verify-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="verify-title">身份认证</div>
                <div class="verify-subtitle">
                    为了您的账户安全和交易保�?br>
                    请完成身份认证流�?
                </div>
            </div>

            <!-- 认证内容 -->
            <div class="verify-content">
                <!-- 步骤指示�?-->
                <div class="step-indicator">
                    <div class="step active">
                        <div class="step-circle">1</div>
                        <div class="step-label">填写信息</div>
                        <div class="step-line"></div>
                    </div>
                    <div class="step">
                        <div class="step-circle">2</div>
                        <div class="step-label">身份证认�?/div>
                        <div class="step-line"></div>
                    </div>
                    <div class="step">
                        <div class="step-circle">3</div>
                        <div class="step-label">人脸识别</div>
                        <div class="step-line"></div>
                    </div>
                    <div class="step">
                        <div class="step-circle">4</div>
                        <div class="step-label">认证完成</div>
                    </div>
                </div>

                <!-- 步骤1: 基本信息 -->
                <div class="step-content active" id="step-1">
                    <div class="step-title">填写基本信息</div>
                    <div class="step-description">请如实填写您的身份信息，确保与身份证一�?/div>
                    
                    <div class="form-group">
                        <label class="form-label">真实姓名</label>
                        <div style="position: relative;">
                            <i class="input-icon fas fa-user"></i>
                            <input type="text" class="form-input with-icon" placeholder="请输入真实姓�? id="real-name">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">身份证号�?/label>
                        <div style="position: relative;">
                            <i class="input-icon fas fa-id-card"></i>
                            <input type="text" class="form-input with-icon" placeholder="请输�?8位身份证号码" id="id-number" maxlength="18">
                        </div>
                    </div>
                    
                    <div class="verify-tips">
                        <div class="tips-title">
                            <i class="fas fa-info-circle"></i>
                            温馨提示
                        </div>
                        <ul class="tips-list">
                            <li>请确保信息真实有效，一旦提交无法修�?/li>
                            <li>身份证号码仅用于实名认证，平台将严格保护您的隐私</li>
                            <li>认证完成后可享受更多平台功能和权�?/li>
                        </ul>
                    </div>
                    
                    <button class="btn btn-primary" onclick="nextStep(2)">下一�?/button>
                </div>

                <!-- 步骤2: 身份证认�?-->
                <div class="step-content" id="step-2">
                    <div class="step-title">身份证认�?/div>
                    <div class="step-description">请拍摄身份证正反面照片，确保信息清晰可见</div>
                    
                    <div class="form-group">
                        <label class="form-label">身份证正�?/label>
                        <div class="camera-frame" onclick="uploadIdFront()">
                            <div class="camera-content" id="id-front-content">
                                <i class="camera-icon fas fa-camera"></i>
                                <div class="camera-text">点击拍摄身份证正�?br>确保头像、姓名清�?/div>
                            </div>
                        </div>
                        <input type="file" class="hidden-input" id="id-front-input" accept="image/*" onchange="previewIdCard(this, 'id-front-content')">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">身份证反�?/label>
                        <div class="camera-frame" onclick="uploadIdBack()">
                            <div class="camera-content" id="id-back-content">
                                <i class="camera-icon fas fa-camera"></i>
                                <div class="camera-text">点击拍摄身份证反�?br>确保有效期清�?/div>
                            </div>
                        </div>
                        <input type="file" class="hidden-input" id="id-back-input" accept="image/*" onchange="previewIdCard(this, 'id-back-content')">
                    </div>
                    
                    <div class="upload-progress" id="upload-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text">上传�?.. 0%</div>
                    </div>
                    
                    <div class="verify-tips">
                        <div class="tips-title">
                            <i class="fas fa-camera"></i>
                            拍摄要求
                        </div>
                        <ul class="tips-list">
                            <li>光线充足，避免反光和阴影</li>
                            <li>身份证四角完整，信息清晰可见</li>
                            <li>支持JPG、PNG格式，文件大小不超过5MB</li>
                        </ul>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="prevStep(1)">上一�?/button>
                        <button class="btn btn-primary" onclick="nextStep(3)" id="id-next-btn" disabled>下一�?/button>
                    </div>
                </div>

                <!-- 步骤3: 人脸识别 -->
                <div class="step-content" id="step-3">
                    <div class="step-title">人脸识别</div>
                    <div class="step-description">请正对摄像头完成人脸识别验证</div>
                    
                    <div class="camera-section">
                        <div class="camera-frame" style="border-radius: 50%; width: 200px; height: 200px;" onclick="startFaceRecognition()">
                            <div class="camera-content" id="face-content">
                                <i class="camera-icon fas fa-user-circle"></i>
                                <div class="camera-text">点击开始人脸识�?/div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="verify-tips">
                        <div class="tips-title">
                            <i class="fas fa-eye"></i>
                            识别要求
                        </div>
                        <ul class="tips-list">
                            <li>请正对摄像头，保持面部居�?/li>
                            <li>光线充足，避免强光直�?/li>
                            <li>摘下口罩、帽子等遮挡�?/li>
                            <li>按照系统提示完成动作指令</li>
                        </ul>
                    </div>
                    
                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="prevStep(2)">上一�?/button>
                        <button class="btn btn-primary" onclick="submitVerification()" id="face-next-btn" disabled>提交认证</button>
                    </div>
                </div>

                <!-- 步骤4: 认证完成 -->
                <div class="step-content" id="step-4">
                    <div class="success-section">
                        <div class="success-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="success-title">认证成功�?/div>
                        <div class="success-description">
                            恭喜您完成实名认证，现在可以享受平台的全部功能和服务
                        </div>
                        
                        <div class="verify-result">
                            <div class="result-item">
                                <span class="result-label">认证姓名</span>
                                <span class="result-value" id="result-name">张三</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">身份证号</span>
                                <span class="result-value" id="result-id">51************1234</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">认证时间</span>
                                <span class="result-value" id="result-time">2024-01-15 14:30</span>
                            </div>
                            <div class="result-item">
                                <span class="result-label">认证状�?/span>
                                <span class="result-value">已认�?/span>
                            </div>
                        </div>
                        
                        <button class="btn btn-primary" onclick="goToProfile()">返回个人中心</button>
                        <button class="btn btn-secondary" onclick="goToHome()">开始购�?/button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentStep = 1;
        let idFrontUploaded = false;
        let idBackUploaded = false;
        let faceVerified = false;

        // 下一�?
        function nextStep(step) {
            if (step === 2) {
                // 验证基本信息
                const realName = document.getElementById('real-name').value.trim();
                const idNumber = document.getElementById('id-number').value.trim();
                
                if (!realName) {
                    alert('请输入真实姓�?);
                    return;
                }
                
                if (!idNumber || !/^\d{17}[\dXx]$/.test(idNumber)) {
                    alert('请输入正确的身份证号�?);
                    return;
                }
            } else if (step === 3) {
                // 验证身份证上�?
                if (!idFrontUploaded || !idBackUploaded) {
                    alert('请上传身份证正反面照�?);
                    return;
                }
            }
            
            showStep(step);
        }

        // 上一�?
        function prevStep(step) {
            showStep(step);
        }

        // 显示步骤
        function showStep(step) {
            // 隐藏所有步骤内�?
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 显示当前步骤
            document.getElementById(`step-${step}`).classList.add('active');
            
            // 更新步骤指示�?
            document.querySelectorAll('.step').forEach((stepElement, index) => {
                stepElement.classList.remove('active', 'completed');
                if (index + 1 < step) {
                    stepElement.classList.add('completed');
                } else if (index + 1 === step) {
                    stepElement.classList.add('active');
                }
            });
            
            currentStep = step;
        }

        // 身份证上�?
        function uploadIdFront() {
            document.getElementById('id-front-input').click();
        }

        function uploadIdBack() {
            document.getElementById('id-back-input').click();
        }

        function previewIdCard(input, contentId) {
            const file = input.files[0];
            if (!file) return;
            
            // 显示上传进度
            const progressSection = document.getElementById('upload-progress');
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');
            
            progressSection.style.display = 'block';
            
            // 模拟上传进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 20;
                if (progress >= 100) {
                    progress = 100;
                    clearInterval(interval);
                    
                    // 上传完成，显示预�?
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const content = document.getElementById(contentId);
                        content.innerHTML = `<img class="id-preview" src="${e.target.result}" alt="身份�?>`;
                        
                        // 标记为已上传
                        if (contentId === 'id-front-content') {
                            idFrontUploaded = true;
                        } else {
                            idBackUploaded = true;
                        }
                        
                        // 检查是否可以进入下一�?
                        checkIdUploadComplete();
                        
                        // 隐藏进度�?
                        setTimeout(() => {
                            progressSection.style.display = 'none';
                        }, 500);
                    };
                    reader.readAsDataURL(file);
                } else {
                    progressFill.style.width = progress + '%';
                    progressText.textContent = `上传�?.. ${Math.floor(progress)}%`;
                }
            }, 200);
        }

        function checkIdUploadComplete() {
            const nextBtn = document.getElementById('id-next-btn');
            if (idFrontUploaded && idBackUploaded) {
                nextBtn.disabled = false;
                nextBtn.style.opacity = '1';
            }
        }

        // 人脸识别
        function startFaceRecognition() {
            const faceContent = document.getElementById('face-content');
            
            // 模拟人脸识别过程
            faceContent.innerHTML = `
                <i class="camera-icon fas fa-spinner fa-spin"></i>
                <div class="camera-text">正在进行人脸识别...</div>
            `;
            
            setTimeout(() => {
                faceContent.innerHTML = `
                    <i class="camera-icon fas fa-check-circle" style="color: #8fbc8f;"></i>
                    <div class="camera-text" style="color: #8fbc8f;">人脸识别成功</div>
                `;
                
                faceVerified = true;
                const nextBtn = document.getElementById('face-next-btn');
                nextBtn.disabled = false;
                nextBtn.style.opacity = '1';
            }, 3000);
        }

        // 提交认证
        function submitVerification() {
            if (!faceVerified) {
                alert('请完成人脸识�?);
                return;
            }
            
            // 模拟提交认证
            const realName = document.getElementById('real-name').value;
            const idNumber = document.getElementById('id-number').value;
            
            // 更新结果显示
            document.getElementById('result-name').textContent = realName;
            document.getElementById('result-id').textContent = idNumber.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
            
            const now = new Date();
            document.getElementById('result-time').textContent = 
                `${now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} ${String(now.getHours()).padStart(2,'0')}:${String(now.getMinutes()).padStart(2,'0')}`;
            
            // 存储认证状�?
            localStorage.setItem('user_verified', 'true');
            localStorage.setItem('user_real_name', realName);
            localStorage.setItem('verify_date', now.toISOString());
            
            showStep(4);
        }

        // 页面跳转
        function goToProfile() {
            window.location.href = 'profile.html';
        }

        function goToHome() {
            window.location.href = 'index.html';
        }

        // 身份证号码格式化
        document.getElementById('id-number').addEventListener('input', function() {
            this.value = this.value.replace(/[^\dXx]/g, '').toUpperCase();
        });

        // 姓名输入限制
        document.getElementById('real-name').addEventListener('input', function() {
            this.value = this.value.replace(/[^\u4e00-\u9fa5·]/g, '');
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .camera-frame').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 检查是否已认证
        if (localStorage.getItem('user_verified') === 'true') {
            if (confirm('您已完成实名认证，是否查看认证信息？')) {
                showStep(4);
                // 显示已保存的认证信息
                const realName = localStorage.getItem('user_real_name') || '已认证用�?;
                const verifyDate = localStorage.getItem('verify_date');
                
                document.getElementById('result-name').textContent = realName;
                if (verifyDate) {
                    const date = new Date(verifyDate);
                    document.getElementById('result-time').textContent = 
                        `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,'0')}-${String(date.getDate()).padStart(2,'0')} ${String(date.getHours()).padStart(2,'0')}:${String(date.getMinutes()).padStart(2,'0')}`;
                }
            }
        }
    </script>
</body>
</html> 
