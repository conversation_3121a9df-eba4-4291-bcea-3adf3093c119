<template>
  <div class="countdown-card">
    <div class="countdown-title">{{ title }}</div>
    <div class="countdown-timer">
      <span>{{ countdown.hours }}</span>:<span>{{ countdown.minutes }}</span>:<span>{{ countdown.seconds }}</span>
    </div>
    <div class="countdown-label">{{ label }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface Props {
  title: string
  label?: string
  endTime?: Date | number
  duration?: number // 持续时间（毫秒）
}

const props = withDefaults(defineProps<Props>(), {
  label: '即将开启',
  duration: 19 * 60 * 60 * 1000 + 57 * 60 * 1000 + 19 * 1000 // 默认19小时57分19秒
})

const countdown = ref({
  hours: '00',
  minutes: '00',
  seconds: '00'
})

let countdownInterval: number | null = null

const updateCountdown = () => {
  const now = new Date().getTime()
  let targetTime: number
  
  if (props.endTime) {
    targetTime = typeof props.endTime === 'number' ? props.endTime : props.endTime.getTime()
  } else {
    targetTime = now + props.duration
  }
  
  const distance = targetTime - now
  
  if (distance > 0) {
    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)
    
    countdown.value.hours = hours.toString().padStart(2, '0')
    countdown.value.minutes = minutes.toString().padStart(2, '0')
    countdown.value.seconds = seconds.toString().padStart(2, '0')
  } else {
    countdown.value.hours = '00'
    countdown.value.minutes = '00'
    countdown.value.seconds = '00'
  }
}

onMounted(() => {
  updateCountdown()
  countdownInterval = setInterval(updateCountdown, 1000)
})

onUnmounted(() => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
  }
})
</script>

<style scoped>
.countdown-card {
  background: linear-gradient(135deg, #2a1b0c, #1a1006);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  text-align: center;
  border: 1px solid rgba(218, 165, 32, 0.25);
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.countdown-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  position: relative;
  z-index: 1;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: var(--text-primary);
  position: relative;
  z-index: 1;
  letter-spacing: 1px;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 10px rgba(218, 165, 32, 0.7);
  margin: var(--spacing-sm) 0;
}

.countdown-timer span {
  display: inline-block;
  min-width: 36px;
  text-align: center;
  background: rgba(60, 30, 0, 0.7);
  padding: var(--spacing-xs);
  border-radius: var(--spacing-xs);
  margin: 0 2px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.countdown-label {
  font-size: 13px;
  color: var(--primary-color);
  margin-top: var(--spacing-xs);
  position: relative;
  z-index: 1;
}
</style> 