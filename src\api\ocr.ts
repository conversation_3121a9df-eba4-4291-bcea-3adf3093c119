import { request } from './request'
import type { ApiResponse } from './types'

/**
 * OCR识别请求参数
 */
export interface OcrRequest {
  /** 证件类型，来自字典表 */
  idType: string
  /** 证件面（身份证：face=正面，back=背面；营业执照等单面证件不传此参数） */
  side?: 'face' | 'back'
  /** 证件照片的base64字符串（完整格式，包含data:image/xxx;base64,前缀） */
  base64: string
}

/**
 * OCR识别结果 - 身份证正面信息
 */
export interface IdCardFrontResult {
  /** 姓名 */
  name: string
  /** 性别 */
  gender: string
  /** 民族 */
  ethnicity: string
  /** 出生日期 */
  birthday: string
  /** 身份证号码 */
  idNumber: string
  /** 住址 */
  address: string
}

/**
 * OCR识别结果 - 身份证背面信息
 */
export interface IdCardBackResult {
  /** 签发机关 */
  issuingAuthority: string
  /** 有效期限起始日期 */
  validFrom: string
  /** 有效期限结束日期 */
  validTo: string
}

/**
 * OCR识别结果 - 营业执照信息
 */
export interface BusinessLicenseResult {
  /** 企业名称 */
  companyName: string
  /** 统一社会信用代码 */
  creditCode: string
  /** 法定代表人 */
  legalRepresentative: string
  /** 注册资本 */
  registeredCapital: string
  /** 成立日期 */
  establishmentDate: string
  /** 营业期限 */
  businessTerm: string
  /** 经营范围 */
  businessScope: string
  /** 住所 */
  address: string
}

/**
 * OCR API类
 */
export class OcrAPI {
  /**
   * 证件OCR识别
   * @param data OCR识别请求参数
   * @returns 识别结果，data字段为JSON字符串，需要解析
   */
  static async recognizeDocument(data: OcrRequest): Promise<ApiResponse<string>> {
    return request.post('/ali/cor', data)
  }

  /**
   * 文件转Base64（包含完整的data:image格式）
   * @param file 文件对象
   * @returns 完整的Base64字符串（包含data:image/xxx;base64,前缀）
   */
  static fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      reader.readAsDataURL(file)
    })
  }

  /**
   * 验证文件类型
   * @param file 文件对象
   * @returns 是否为有效的图片文件
   */
  static validateImageFile(file: File): { valid: boolean; error?: string } {
    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: '请上传JPG或PNG格式的图片' }
    }

    // 检查文件大小（5MB限制）
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      return { valid: false, error: '图片大小不能超过5MB' }
    }

    return { valid: true }
  }

  /**
   * 解析身份证正面OCR结果
   * @param ocrData OCR返回的JSON字符串
   * @returns 解析后的身份证正面信息
   */
  static parseIdCardFront(ocrData: string): IdCardFrontResult | null {
    try {
      const data = JSON.parse(ocrData)
      return {
        name: data.name || '',
        gender: data.gender || '',
        ethnicity: data.ethnicity || '',
        birthday: data.birthday || '',
        idNumber: data.num || data.idNumber || data.id_number || '',
        address: data.address || ''
      }
    } catch (error) {
      console.error('解析身份证正面OCR结果失败:', error)
      return null
    }
  }

  /**
   * 格式化身份证日期
   * @param dateStr 原始日期字符串，可能是"20290403"格式或"长期"
   * @returns 格式化后的日期字符串（YYYY-MM-DD格式）或特殊值
   */
  static formatIdCardDate(dateStr: string): string {
    if (!dateStr) return ''
    
    // 处理"长期"情况
    if (dateStr === '长期' || dateStr.toLowerCase() === 'permanent') {
      return 'permanent'
    }
    
    // 处理8位数字日期格式：20290403 -> 2029-04-03
    if (/^\d{8}$/.test(dateStr)) {
      const year = dateStr.substring(0, 4)
      const month = dateStr.substring(4, 6)
      const day = dateStr.substring(6, 8)
      
      // 验证日期有效性
      const date = new Date(`${year}-${month}-${day}`)
      if (date.getFullYear().toString() === year && 
          (date.getMonth() + 1).toString().padStart(2, '0') === month &&
          date.getDate().toString().padStart(2, '0') === day) {
        return `${year}-${month}-${day}`
      }
    }
    
    // 其他格式直接返回
    return dateStr
  }

  /**
   * 解析身份证背面OCR结果
   * @param ocrData OCR返回的JSON字符串
   * @returns 解析后的身份证背面信息
   */
  static parseIdCardBack(ocrData: string): IdCardBackResult | null {
    try {
      const data = JSON.parse(ocrData)
      const rawValidTo = data.validTo || data.valid_to || data.end_date || ''
      
      return {
        issuingAuthority: data.issuingAuthority || data.issue_date || '',
        validFrom: this.formatIdCardDate(data.validFrom || data.valid_from || data.start_date || ''),
        validTo: this.formatIdCardDate(rawValidTo)
      }
    } catch (error) {
      console.error('解析身份证背面OCR结果失败:', error)
      return null
    }
  }

  /**
   * 解析营业执照OCR结果
   * @param ocrData OCR返回的JSON字符串
   * @returns 解析后的营业执照信息
   */
  static parseBusinessLicense(ocrData: string): BusinessLicenseResult | null {
    try {
      const data = JSON.parse(ocrData)
      return {
        companyName: data.name || data.companyName || '',
        creditCode: data.num || data.reg_num || data.creditCode || '',
        legalRepresentative: data.person || data.legalRepresentative || '',
        registeredCapital: data.capital || data.registeredCapital || '',
        establishmentDate: data.establish_date || data.establishmentDate || '',
        businessTerm: data.valid_period || data.businessTerm || '',
        businessScope: data.business || data.businessScope || '',
        address: data.address || ''
      }
    } catch (error) {
      console.error('解析营业执照OCR结果失败:', error)
      return null
    }
  }
} 