<template>
  <AppPageHeader :title="'登录'" @back="$router.back()" />
  <div class="auth-page">
    <div class="auth-container">
      <!-- Logo区域 -->
      <div class="logo-section" @click="goBack">
        <!-- <div class="logo-container">
          <div class="logo">
            <i class="fas fa-sun" style="font-size: 36px;"></i>
          </div>
        </div> -->
        <div class="welcome-text">凌云数资</div>
        <div class="subtitle">欢迎登录</div>
      </div>

      <!-- 登录表单 -->
      <div class="auth-form">
        <!-- 标签切换器 -->
        <div class="tab-switcher">
          <button
            class="tab-btn"
            :class="{ active: activeTab === 'sms' }"
            @click="switchTab('sms')"
          >
            <i class="fas fa-mobile"></i> 验证码登录
          </button>
          <button
            class="tab-btn"
            :class="{ active: activeTab === 'password' }"
            @click="switchTab('password')"
          >
            <i class="fas fa-lock"></i> 密码登录
          </button>
        </div>

        <!-- 验证码登录表单 -->
        <div
          class="form-content"
          :class="{ active: activeTab === 'sms' }"
          v-show="activeTab === 'sms'"
        >
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-mobile"></i>
              手机号码
            </label>
            <div class="input-wrapper">
              <input
                type="tel"
                class="form-input"
                placeholder="请输入手机号码"
                maxlength="11"
                v-model="smsForm.phoneNumber"
                @input="formatPhoneNumber"
                required
              >
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-shield"></i>
              短信验证码
            </label>
            <div class="sms-container">
              <input
                type="text"
                class="form-input"
                placeholder="请输入验证码"
                maxlength="6"
                v-model="smsForm.smsCode"
                @input="formatSmsCode"
                required
              >
              <button
                type="button"
                class="sms-btn"
                :disabled="smsCountdown > 0"
                @click="sendSMS"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
              </button>
            </div>
          </div>

          <!-- 协议勾选 -->
          <div class="agreement">
            <div
              class="checkbox"
              :class="{ checked: agreedToTerms }"
              @click="agreedToTerms = !agreedToTerms"
            ></div>
            <div>
              我已阅读并同意
              <a href="#" @click.prevent="showUserAgreement">《用户服务协议》</a>
              和
              <a href="#" @click.prevent="showPrivacyPolicy">《隐私政策》</a>
            </div>
          </div>

          <button
            type="button"
            class="btn btn-primary"
            :disabled="isLoggingIn"
            @click="loginWithSMS"
          >
            <span v-if="isLoggingIn"><i class="fas fa-spinner fa-spin"></i></span><span v-else><i class="fas fa-sign-in"></i></span>
            {{ isLoggingIn ? '登录中...' : '登录' }}
          </button>
        </div>

        <!-- 密码登录表单 -->
        <div
          class="form-content"
          :class="{ active: activeTab === 'password' }"
          v-show="activeTab === 'password'"
        >
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-user"></i>
              手机号/用户名
            </label>
            <div class="input-wrapper">
              <input
                type="text"
                class="form-input"
                placeholder="请输入手机号或用户名"
                v-model="passwordForm.username"
                required
              >
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-lock"></i>
              登录密码
            </label>
            <div class="input-wrapper">
              <input
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                :class="{
                  'input-error': passwordForm.password && !passwordValidationValid,
                  'input-success': passwordForm.password && passwordValidationValid
                }"
                placeholder="8-16位，包含大小写字母和数字"
                v-model="passwordForm.password"
                @input="onPasswordInput"
                required
              >
              <button
                type="button"
                class="password-toggle"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>

            <!-- 密码校验提示 -->
            <div
              v-if="passwordForm.password && passwordValidationMessage"
              class="validation-message"
              :class="{
                'validation-success': passwordValidationValid,
                'validation-error': !passwordValidationValid
              }"
            >
              <span v-if="passwordValidationValid">✅</span>
              <span v-else>❌</span>
              {{ passwordValidationMessage }}
            </div>

            <div class="forgot-password">
              <!-- <router-link to="/forgot-password">忘记密码？</router-link> -->
            </div>
          </div>

          <!-- 协议勾选 -->
          <div class="agreement">
            <div
              class="checkbox"
              :class="{ checked: agreedToTerms }"
              @click="agreedToTerms = !agreedToTerms"
            ></div>
            <div>
              我已阅读并同意
              <a href="#" @click.prevent="showUserAgreement">《用户服务协议》</a>
              和
              <a href="#" @click.prevent="showPrivacyPolicy">《隐私政策》</a>
            </div>
          </div>

          <button
            type="button"
            class="btn btn-primary"
            :disabled="isLoggingIn"
            @click="loginWithPassword"
          >
            <span v-if="isLoggingIn"><i class="fas fa-spinner fa-spin"></i></span><span v-else><i class="fas fa-sign-in"></i></span>
            {{ isLoggingIn ? '登录中...' : '登录' }}
          </button>
        </div>
      </div>

      <!-- 注册提示 -->
      <div class="prompt-card">
          <p>还没有账户？</p>
          <router-link to="/register">立即注册 →</router-link>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useForceScroll } from '@/composables/useForceScroll'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import { RequestError } from '@/api/request'
import type { LoginRequest, PhoneLoginRequest } from '@/api/types'
import AppPageHeader from '@/components/AppPageHeader.vue'
import { validatePhoneNumber as validatePhone, detectVirtualOperator, getVirtualOperatorInfo } from '@/utils/phoneValidator'

// 响应式数据
const router = useRouter()

// 启用强制滚动
const { enableScrolling } = useForceScroll()

// 通知系统
const { success, error, info } = useNotification()
const activeTab = ref<'sms' | 'password'>('sms')
const smsCountdown = ref(0)
const isLoggingIn = ref(false)
const showPassword = ref(false)

// 密码校验状态
const passwordValidationMessage = ref('')
const passwordValidationValid = ref(false)

// 新增：协议勾选状态
const agreedToTerms = ref(false)

// 表单数据
const smsForm = reactive({
  phoneNumber: '',
  smsCode: ''
})

const passwordForm = reactive({
  username: '',
  password: ''
})

// 倒计时定时器
let countdownTimer: number | null = null

/**
 * 切换标签
 */
const switchTab = (tabType: 'sms' | 'password') => {
  activeTab.value = tabType
}

/**
 * 格式化手机号输入
 */
const formatPhoneNumber = () => {
  smsForm.phoneNumber = smsForm.phoneNumber.replace(/\D/g, '')
}

/**
 * 格式化验证码输入
 */
const formatSmsCode = () => {
  smsForm.smsCode = smsForm.smsCode.replace(/\D/g, '')
}

/**
 * 验证密码强度
 * 规则：8-12字符，需包含大小写字母、数字
 */
const validatePasswordStrength = (password: string): { isValid: boolean; message: string } => {
  if (!password) {
    return { isValid: false, message: '请输入密码' }
  }

  if (password.length < 8 || password.length > 16) {
    return { isValid: false, message: '密码长度必须为8-16位字符' }
  }

  // 检查是否包含大写字母
  if (!/[A-Z]/.test(password)) {
    return { isValid: false, message: '密码必须包含大写字母' }
  }

  // 检查是否包含小写字母
  if (!/[a-z]/.test(password)) {
    return { isValid: false, message: '密码必须包含小写字母' }
  }

  // 检查是否包含数字
  if (!/[0-9]/.test(password)) {
    return { isValid: false, message: '密码必须包含数字' }
  }

  return { isValid: true, message: '密码强度符合要求' }
}

/**
 * 密码输入处理
 */
const onPasswordInput = () => {
  const validation = validatePasswordStrength(passwordForm.password)
  passwordValidationMessage.value = validation.message
  passwordValidationValid.value = validation.isValid
}

/**
 * 发送短信验证码（包含虚拟运营商检测）
 */
const sendSMS = async () => {
  if (!smsForm.phoneNumber) {
    error('请先输入手机号码')
    return
  }

  // 验证手机号（包含虚拟运营商检测）
  const result = validatePhone(smsForm.phoneNumber, false) // 不允许虚拟运营商
  if (!result.isValid) {
    // 如果是虚拟运营商，显示详细提示
    if (result.virtualInfo?.isVirtual) {
      const detailInfo = getVirtualOperatorInfo(smsForm.phoneNumber)
      error(`${result.error}\n\n${detailInfo}`, 8000)
    } else {
      error(result.error || '手机号码验证失败')
    }
    return
  }

  if (smsCountdown.value > 0) return

  try {
    // 调用真实API发送短信
    await AuthAPI.sendSmsCode({
      phone: smsForm.phoneNumber,
      scene: 'H5login'
    })

    success(`验证码已发送到手机号 ${smsForm.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}，请注意查收！`, 5000)

    // 额外提示验证码有效期
    setTimeout(() => {
      info('验证码有效期为5分钟，请尽快输入', 3000)
    }, 2000)

    // 开始倒计时
    smsCountdown.value = 60
    countdownTimer = setInterval(() => {
      if (smsCountdown.value <= 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      } else {
        smsCountdown.value--
      }
    }, 1000)
  } catch (err) {
    console.error('发送验证码失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('发送验证码失败，请稍后重试')
    }
  }
}

/**
 * 切换密码显示状态
 */
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

/**
 * 显示用户协议
 */
const showUserAgreement = () => {
  // 保存当前表单数据到sessionStorage
  saveFormToSession()
  router.push('/user-agreement')
}

/**
 * 显示隐私政策
 */
const showPrivacyPolicy = () => {
  // 保存当前表单数据到sessionStorage
  saveFormToSession()
  router.push('/privacy-policy')
}

/**
 * 保存表单数据到sessionStorage
 */
const saveFormToSession = () => {
  const formData = {
    activeTab: activeTab.value,
    smsForm: { ...smsForm },
    passwordForm: { ...passwordForm },
    agreedToTerms: agreedToTerms.value
  }
  sessionStorage.setItem('loginFormData', JSON.stringify(formData))
}

/**
 * 从sessionStorage恢复表单数据
 */
const restoreFormFromSession = () => {
  const savedData = sessionStorage.getItem('loginFormData')
  if (savedData) {
    try {
      const formData = JSON.parse(savedData)
      activeTab.value = formData.activeTab || 'sms'
      Object.assign(smsForm, formData.smsForm || {})
      Object.assign(passwordForm, formData.passwordForm || {})
      agreedToTerms.value = formData.agreedToTerms || false
      // 清除保存的数据
      sessionStorage.removeItem('loginFormData')
    } catch (err) {
      console.warn('恢复表单数据失败:', err)
    }
  }
}

/**
 * 使用短信验证码登录
 */
const loginWithSMS = async () => {
  if (!smsForm.phoneNumber || !/^1[3-9]\d{9}$/.test(smsForm.phoneNumber)) {
    error('请输入正确的手机号码')
    return
  }

  if (!smsForm.smsCode || smsForm.smsCode.length !== 6) {
    error('请输入6位验证码')
    return
  }

  // 新增：检查是否同意协议
  if (!agreedToTerms.value) {
    error('请先阅读并同意用户协议和隐私政策')
    return
  }

  isLoggingIn.value = true

  try {
    // 调用真实API进行手机号登录
    const response = await AuthAPI.phoneLogin({
      phone: smsForm.phoneNumber,
      smsCode: smsForm.smsCode
    })

    // 保存登录信息
    AuthAPI.saveLoginInfo(response.token)

    success('登录成功！欢迎回来')

    // 获取用户信息
    try {
      const userInfo = await AuthAPI.getUserInfo()
      AuthAPI.saveLoginInfo(response.token, userInfo.user)
    } catch (err) {
      console.warn('获取用户信息失败:', err)
    }

    // 登录成功后跳转 - 检查是否有重定向路径
    setTimeout(() => {
      const redirectPath = localStorage.getItem('redirectPath')
      if (redirectPath) {
        localStorage.removeItem('redirectPath')
        router.push(redirectPath)
      } else {
        router.push('/')
      }
    }, 1000)
  } catch (err) {
    console.error('登录失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('登录失败，请检查验证码')
    }
  } finally {
    isLoggingIn.value = false
  }
}

/**
 * 使用密码登录
 */
const loginWithPassword = async () => {
  if (!passwordForm.username.trim()) {
    error('请输入手机号或用户名')
    return
  }

  // 使用新的密码校验规则
  const passwordValidation = validatePasswordStrength(passwordForm.password)
  if (!passwordValidation.isValid) {
    error(passwordValidation.message)
    return
  }

  // 新增：检查是否同意协议
  if (!agreedToTerms.value) {
    error('请先阅读并同意用户协议和隐私政策')
    return
  }

  isLoggingIn.value = true

  try {
    // 调用真实API进行密码登录
    const response = await AuthAPI.login({
      username: passwordForm.username,
      password: passwordForm.password
    })

    // 保存登录信息
    AuthAPI.saveLoginInfo(response.token)

    success('登录成功！欢迎回来')

    // 获取用户信息
    try {
      const userInfo = await AuthAPI.getUserInfo()
      AuthAPI.saveLoginInfo(response.token, userInfo.user)
    } catch (err) {
      console.warn('获取用户信息失败:', err)
    }

    // 登录成功后跳转 - 检查是否有重定向路径
    setTimeout(() => {
      const redirectPath = localStorage.getItem('redirectPath')
      if (redirectPath) {
        localStorage.removeItem('redirectPath')
        router.push(redirectPath)
      } else {
        router.push('/')
      }
    }, 1000)
  } catch (err) {
    console.error('登录失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('登录失败，请检查用户名和密码')
    }
  } finally {
    isLoggingIn.value = false
  }
}





/**
 * 返回首页
 */
const goBack = () => {
  router.push('/')
}



// 生命周期钩子
onMounted(() => {
  enableScrolling()
  // 显示登录提示
  info('请登录', 3000)

  // 恢复表单数据
  restoreFormFromSession()

  // 欢迎提示
  // setTimeout(() => {
  //   info('欢迎使用凌云数资！')
  // }, 500)
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
@import '@/assets/styles/auth.css';
</style>
