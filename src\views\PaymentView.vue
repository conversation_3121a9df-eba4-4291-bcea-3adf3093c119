<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import { PayAPI } from '@/api/pay'
import type { ApiResponse } from '@/api/types'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const route = useRoute()
const router = useRouter()
const notification = useNotification()

// 数据状态
const loading = ref(false)
const orderStatus = ref('')
const orderInfo = ref<any>(null)
const selectedPayMethod = ref('alipayH5')
const isProcessing = ref(false)

// 从路由参数获取数据
const orderNo = computed(() => route.params.orderNo as string)
const assetName = computed(() => route.query.assetName as string)
const assetPrice = computed(() => route.query.assetPrice as string)
const assetCover = computed(() => route.query.assetCover as string)
const purchaseQuantity = computed(() => parseInt(route.query.purchaseQuantity as string) || 1)
const totalAmount = computed(() => route.query.totalAmount as string)

// 支付方式选项
const paymentMethods = [
  {
    id: 'alipayH5',
    name: '支付宝支付',
    icon: 'fab fa-alipay',
    color: '#1677ff'
  },
  // {
  //   id: 'unionGatewayPay',
  //   name: '银联支付',
  //   icon: '/images/unionpay-logo.png', // 使用本地图片
  //   color: '#e60012'
  // }
  // {
  //   id: 'wechatH5',
  //   name: '微信支付',
  //   icon: 'fab fa-weixin',
  //   color: '#07c160'
  // }
]

// 格式化价格
const formatPrice = (price: any): string => {
  if (typeof price === 'number') {
    return price.toFixed(2)
  }
  return String(price || '0.00').replace(/[¥]/g, '')
}

// 获取显示的支付金额（优先使用总金额，否则使用单价）
const getDisplayAmount = (): string => {
  if (totalAmount.value && totalAmount.value !== '0') {
    return formatPrice(totalAmount.value)
  }
  // 如果没有总金额，使用单价 × 数量计算
  const unitPrice = parseFloat(assetPrice.value || '0')
  const quantity = purchaseQuantity.value
  return formatPrice(unitPrice * quantity)
}

// 检查订单状态
const checkOrderStatus = async () => {
  try {
    loading.value = true
    console.log('📡 检查订单状态:', orderNo.value)

    const response = await PayAPI.getOrderStatus(orderNo.value)

    console.log('📥 订单状态响应:', response)

    if (response.code === 200 && response.data) {
      orderInfo.value = response.data
      orderStatus.value = response.data.status

      if (response.data.status === 'processing') {
        notification.warning('订单正在处理中，请稍后再试')
      } else if (response.data.status === 'success') {
        console.log('✅ 订单状态正常，可以进行支付')
      } else {
        console.warn('⚠️ 订单状态异常:', response.data.status)
      }
    } else {
      throw new Error(response.msg || '获取订单状态失败')
    }
  } catch (error) {
    console.error('❌ 检查订单状态失败:', error)
    const errorMessage = error instanceof Error ? error.message : '获取订单状态失败'
    notification.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 发起支付
const confirmPayment = async () => {
  if (!orderNo.value) {
    notification.error('订单号缺失')
    return
  }

  if (orderStatus.value === 'processing') {
    notification.warning('订单正在处理中，请稍后再试')
    return
  }

  try {
    isProcessing.value = true
    console.log('💳 发起支付请求:', {
      orderNo: orderNo.value,
      payChannel: selectedPayMethod.value
    })

    const response = await PayAPI.pay(orderNo.value, selectedPayMethod.value)

    console.log('📥 支付接口响应:', response)

    if (response.code === 200) {
      console.log('✅ 支付请求成功')

      // 处理支付返回的数据
      if (response.data) {
        await handlePaymentResponse(response.data,selectedPayMethod.value)
      } else {
        notification.success('支付请求成功！')
      }
    } else {
      throw new Error(response.msg || '支付请求失败')
    }
  } catch (error) {
    console.error('❌ 支付请求失败:', error)
    const errorMessage = error instanceof Error ? error.message : '支付请求失败'
    notification.error(errorMessage)
  } finally {
    isProcessing.value = false
  }
}

// 处理支付响应
const handlePaymentResponse = async (paymentData: any, payChannel: string) => {
  try {
    if (payChannel === 'alipayH5' || payChannel === 'unionGatewayPay') {
      // 如果返回了支付URL，直接跳转
      console.log('🔗 检测到支付URL，准备跳转:', paymentData)
      notification.success('正在跳转到支付页面...', 2000)
      setTimeout(() => {
        window.location.href = paymentData
      }, 1000)
    } else if (payChannel === 'wechatH5') {
      // 微信支付暂时隐藏，不处理
    }
  } catch (error) {
    console.error('❌ 处理支付响应失败:', error)
    notification.error('支付处理失败，请联系客服')
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 选择支付方式
const selectPayMethod = (methodId: string) => {
  selectedPayMethod.value = methodId
}

// 生命周期
onMounted(() => {
  if (!orderNo.value) {
    notification.error('订单号缺失，请重新下单')
    router.back()
    return
  }

  checkOrderStatus()
})
</script>

<template>
  <AppPageHeader :title="'确认支付'" @back="$router.back()" />
  <div class="payment-container" :style="{ paddingTop: '40px' }">
    <!-- 头部导航 -->


    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在加载订单信息...</p>
    </div>

    <!-- 支付内容 -->
    <div v-else class="payment-content">
      <!-- 资产信息 -->
      <section class="asset-info">
        <div class="asset-cover">
          <img
            v-if="assetCover"
            :src="assetCover"
            :alt="assetName"
            class="cover-image"
          >
          <div v-else class="cover-placeholder">
            <i class="fas fa-image"></i>
          </div>
        </div>

        <div class="asset-details">
          <h2 class="asset-name">{{ assetName || '数字资产' }}</h2>
          <div class="asset-info-row">
            <div class="asset-quantity">
              <span class="quantity-label">购买数量</span>
              <span class="quantity-value">{{ purchaseQuantity }}份</span>
            </div>
            <div class="asset-unit-price">
              <span class="unit-price-label">单价</span>
              <span class="unit-price-value">¥{{ formatPrice(assetPrice) }}</span>
            </div>
          </div>
          <div class="asset-price">
            <span class="price-label">支付金额</span>
            <span class="price-value">¥{{ getDisplayAmount() }}</span>
          </div>
        </div>
      </section>

      <!-- 订单信息 -->
      <section v-if="orderInfo" class="order-info">
        <h3 class="section-title">订单信息</h3>
        <div class="order-details">
          <div class="order-item">
            <span class="item-label">订单号</span>
            <span class="item-value">{{ orderNo }}</span>
          </div>
          <!-- <div class="order-item">
            <span class="item-label">订单状态</span>
            <span class="item-value" :class="orderStatus">{{ orderStatus }}</span>
          </div> -->
        </div>
      </section>

      <!-- 支付方式 -->
      <section class="payment-methods">
        <h3 class="section-title">选择支付方式</h3>
        <div class="methods-list">
          <div
            v-for="method in paymentMethods"
            :key="method.id"
            class="method-item"
            :class="{ active: selectedPayMethod === method.id }"
            @click="selectPayMethod(method.id)"
          >
            <div class="method-icon">
              <i v-if="!method.icon.startsWith('/images/')" :class="method.icon" :style="{ color: method.color, fontSize: '48px' }"></i>
              <img v-else :src="method.icon" alt="银联LOGO" style="width:48px;height:48px;object-fit:contain;vertical-align:middle;" />
            </div>
            <div class="method-info">
              <span class="method-name">{{ method.name }}</span>
            </div>
            <div class="method-radio">
              <div class="radio-button" :class="{ checked: selectedPayMethod === method.id }">
                <i v-if="selectedPayMethod === method.id" class="fas fa-check"></i>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 底部固定支付按钮 -->
      <section class="payment-footer">
        <div class="footer-content">
          <div class="payment-amount">
            <span class="amount-label">支付金额</span>
            <span class="amount-value">¥{{ getDisplayAmount() }}</span>
          </div>
          <button
            class="confirm-payment-btn"
            :disabled="isProcessing || orderStatus === 'processing'"
            @click="confirmPayment"
          >
            <i v-if="isProcessing" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-credit-card"></i>
            {{ isProcessing ? '处理中...' : '确认支付' }}
          </button>
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;

  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);

  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);

  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
}

.payment-container {
  background: var(--gradient-bg);
  color: var(--text-primary);
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 头部导航 */
.payment-header {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.back-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  grid-column: 1;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  text-align: center;
  grid-column: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-placeholder {
  width: 40px;
  grid-column: 3;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

/* 支付内容 */
.payment-content {
  padding: 0 0 120px 0;
}

/* 资产信息 */
.asset-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: var(--gradient-card);
  margin: 16px;
  border-radius: 16px;
  border: 1px solid var(--border-light);
  min-height: 100px;
}

.asset-cover {
  flex-shrink: 0;
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  background: var(--bg-secondary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.cover-image:hover {
  transform: scale(1.05);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 24px;
}

.asset-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 80px;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 12px 0;
  line-height: 1.3;
  word-break: break-word;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.asset-info-row {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  margin-bottom: 8px;
}

.asset-quantity,
.asset-unit-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.quantity-label,
.unit-price-label {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.quantity-value,
.unit-price-value {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 600;
}

.asset-price {
  display: flex;
  flex-direction: column;
  gap: 2px;
  margin-top: auto;
}

.price-label {
  font-size: 12px;
  color: var(--text-muted);
  font-weight: 500;
}

.price-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

/* 订单信息 */
.order-info {
  margin: 16px;
  background: var(--gradient-card);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-label {
  font-size: 14px;
  color: var(--text-muted);
}

.item-value {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.item-value.processing {
  color: #f9844a;
}

.item-value.success {
  color: #90a955;
}

/* 支付方式 */
.payment-methods {
  margin: 16px;
  background: var(--gradient-card);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  padding: 20px;
}

.methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.method-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--border-hover);
}

.method-item.active {
  background: var(--primary-alpha);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
}

.method-icon {
  font-size: 24px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.method-radio {
  width: 24px;
  height: 24px;
}

.radio-button {
  width: 100%;
  height: 100%;
  border: 2px solid var(--border-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-button.checked {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.radio-button i {
  font-size: 12px;
}

/* 底部支付按钮 */
.payment-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(26, 26, 26, 0.98), rgba(36, 36, 36, 0.95));
  border-top: 1px solid var(--border-light);
  backdrop-filter: blur(20px);
  padding: 16px;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.4);
}

.footer-content {
  display: flex;
  align-items: center;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.payment-amount {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.amount-label {
  font-size: 12px;
  color: var(--text-muted);
}

.amount-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
}

.confirm-payment-btn {
  flex: 1;
  padding: 16px 24px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 52px;
}

.confirm-payment-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.4);
}

.confirm-payment-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .asset-info {
    padding: 16px;
    gap: 12px;
    margin: 12px;
  }

  .asset-cover {
    width: 70px;
    height: 70px;
  }

  .asset-details {
    height: 70px;
  }

  .asset-name {
    font-size: 14px;
    margin-bottom: 10px;
  }

  .asset-info-row {
    gap: 12px;
    margin-bottom: 6px;
  }

  .quantity-value,
  .unit-price-value {
    font-size: 13px;
  }

  .price-value {
    font-size: 18px;
  }

  .footer-content {
    flex-direction: column;
    gap: 12px;
  }

  .payment-amount {
    text-align: center;
  }

  .confirm-payment-btn {
    width: 100%;
  }
}

/* 超小屏幕优化 */
@media (max-width: 360px) {
  .asset-info {
    padding: 12px;
    gap: 10px;
    margin: 8px;
  }

  .asset-cover {
    width: 60px;
    height: 60px;
  }

  .asset-details {
    height: 60px;
  }

  .asset-name {
    font-size: 13px;
    -webkit-line-clamp: 3;
    margin-bottom: 8px;
  }

  .asset-info-row {
    flex-direction: column;
    gap: 8px;
    margin-bottom: 8px;
  }

  .quantity-label,
  .unit-price-label,
  .price-label {
    font-size: 11px;
  }

  .quantity-value,
  .unit-price-value {
    font-size: 12px;
  }

  .price-value {
    font-size: 16px;
  }
}
</style>
