<template>
  <AppPageHeader :title="'我的盲盒'" @back="$router.back()" />
  <div class="blind-box-list-page" style="padding-top: 40px;">

    <main class="main-content">
      <div class="box-grid">
        <div v-for="box in boxList" :key="box.userBoxId" class="box-card" @click="handleBoxClick(box)">
          <div class="box-cover-wrapper">
            <img :src="box.boxCover" :alt="box.boxName" class="box-cover" />
            <div class="box-info-overlay">
              <div class="box-name">{{ box.boxName }}</div>
              <div class="box-code">编号：{{ box.boxCode }}</div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="loading" class="loading">加载中...</div>
      <div v-if="!loading && boxList.length === 0" class="empty">暂无盲盒</div>
      <div v-if="!hasMore && boxList.length > 0" class="no-more">没有更多盲盒了</div>
    </main>
  </div>

  <!-- 新增弹窗样式和结构 -->
  <template v-if="showConfirm">
    <div class="modal-mask">
      <div class="modal-wrapper">
        <div class="modal-container">
          <div class="modal-title">确认开启盲盒？</div>
          <div class="modal-content">盲盒：{{ selectedBox?.boxName }}<br>编号：{{ selectedBox?.boxCode }}</div>
          <div class="modal-actions">
            <button class="modal-btn confirm" @click="confirmOpenBox" :disabled="opening">
              <span v-if="!opening">确认</span>
              <span v-else class="open-loading"><i class="fas fa-spinner fa-spin"></i> 开启中...</span>
            </button>
            <button class="modal-btn cancel" @click="cancelOpenBox">取消</button>
          </div>
        </div>
      </div>
    </div>
  </template>

  <!-- 新增中奖奖品弹窗 -->
  <template v-if="showPrize">
    <div class="modal-mask">
      <div class="modal-wrapper">
        <div class="modal-container">
          <div class="modal-title">恭喜你获得奖品！</div>
          <div class="prize-content">
            <img v-if="prizeInfo?.cover" :src="prizeInfo.cover" alt="奖品封面" class="prize-cover" />
            <div class="prize-name">{{ prizeInfo?.name }}</div>
          </div>
          <div class="modal-actions">
            <button class="modal-btn confirm" @click="showPrize = false">我知道了</button>
          </div>
        </div>
      </div>
    </div>
  </template>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { getUserBoxList, openBox as openBoxApi, type BlindBox } from '@/api/box'
import AppPageHeader from '@/components/AppPageHeader.vue'
import { useNotification } from '@/composables/useNotification'

const router = useRouter()
const boxList = ref<BlindBox[]>([])
const pageNum = ref(1)
const pageSize = 6
const total = ref(0)
const loading = ref(false)
const hasMore = ref(true)

// 新增：弹窗状态
const showConfirm = ref(false)
const selectedBox = ref<BlindBox | null>(null)

// 新增：中奖奖品弹窗状态
const showPrize = ref(false)
const prizeInfo = ref<{ cover: string; name: string } | null>(null)

// 开启动画loading状态
const opening = ref(false)

const { success, error } = useNotification()

const fetchBoxList = async (append = false) => {
  if (loading.value || !hasMore.value) return
  loading.value = true
  try {
    const res: any = await getUserBoxList({ pageNum: pageNum.value, pageSize })
    if (res.code === 200 && Array.isArray(res.rows)) {
      if (append) {
        boxList.value = boxList.value.concat(res.rows)
      } else {
        boxList.value = res.rows
      }
      total.value = res.total || 0
      hasMore.value = boxList.value.length < total.value
    } else {
      if (!append) boxList.value = []
      hasMore.value = false
    }
  } catch (err) {
    if (!append) boxList.value = []
    hasMore.value = false
  } finally {
    loading.value = false
  }
}

const handleScroll = () => {
  const scrollTop = window.scrollY || document.documentElement.scrollTop
  const clientHeight = window.innerHeight || document.documentElement.clientHeight
  const scrollHeight = document.documentElement.scrollHeight
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    if (hasMore.value && !loading.value) {
      pageNum.value++
      fetchBoxList(true)
    }
  }
}

const bindScroll = () => window.addEventListener('scroll', handleScroll)
const unbindScroll = () => window.removeEventListener('scroll', handleScroll)

// 新增：点击卡片弹窗
const handleBoxClick = (box: BlindBox) => {
  if (box.isOpened === '1') return // 已开启不弹窗
  selectedBox.value = box
  showConfirm.value = true
}

const confirmOpenBox = async () => {
  if (selectedBox.value) {
    opening.value = true // 显示loading动画
    try {
      const res: any = await openBoxApi(selectedBox.value.boxCode)
      if (res.code === 200) {
        // 处理中奖奖品弹窗
        let cover = ''
        let name = ''
        if (res.data) {
          if (res.data.assetName) {
            cover = res.data.assetCover
            name = res.data.assetName
          } else if (res.data.couponName) {
            cover = res.data.couponCover
            name = res.data.couponName
          }
        }
        if (name) {
          prizeInfo.value = { cover, name }
          showPrize.value = true
        } else {
          success('盲盒开启成功！')
        }
        // 刷新列表
        pageNum.value = 1
        boxList.value = [] // 清空旧数据，防止append
        await fetchBoxList(false)
      } else {
        error(res.msg || '盲盒开启失败')
      }
    } catch (e) {
      error('盲盒开启失败')
    } finally {
      opening.value = false // 关闭loading动画
      showConfirm.value = false
      selectedBox.value = null
    }
  }
}
const cancelOpenBox = () => {
  showConfirm.value = false
  selectedBox.value = null
}

const goBack = () => {
  // 获取历史记录
  const from = router.options.history.state.back as string | undefined
  if (from && (from.includes('/login') || from.includes('/register'))) {
    router.replace('/')
  } else {
    router.back()
  }
}
const openBox = (box: BlindBox) => {
  // TODO: 实现盲盒开启逻辑
  alert('盲盒开启功能待开发')
}

onMounted(() => {
  fetchBoxList()
  bindScroll()
})
onUnmounted(() => {
  unbindScroll()
})
</script>

<style scoped>
.blind-box-list-page {
  min-height: 100vh;
  background: var(--gradient-bg, #181818);
  color: #fff;
}
.page-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: rgba(42,42,42,0.95);
  border-bottom: 1px solid #222;
}
.back-btn {
  background: none;
  border: none;
  color: #d4a574;
  font-size: 20px;
  margin-right: 12px;
  cursor: pointer;
}
.page-title {
  font-size: 18px;
  font-weight: 600;
}
.main-content {
  padding: 20px 16px;
}
.box-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 18px;
}
.box-card {
  background: #232323;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.12);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1.5px solid rgba(255,255,255,0.08);
  transition: box-shadow 0.2s, border-color 0.2s;
  cursor: pointer;
}
.box-card:hover {
  box-shadow: 0 6px 18px rgba(212,165,116,0.18);
  border-color: #d4a574;
}
/* 盲盒卡片底部浮层样式 */
.box-cover-wrapper {
  width: 100%;
  height: 225px;
  background: #181818;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}
.box-info-overlay {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px 14px 12px 14px;
  background: linear-gradient(0deg, rgba(34,34,34,0.98) 85%, rgba(34,34,34,0.65) 60%, rgba(34,34,34,0.0) 20%);
  color: #fff;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 6px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}
.box-info-overlay .box-name {
  font-size: 15px;
  font-weight: 700;
  color: #fff;
  margin-bottom: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: 0 2px 8px rgba(0,0,0,0.28), 0 1px 0 #222;
  letter-spacing: 0.5px;
}
.box-info-overlay .box-code {
  font-size: 12px;
  color: #e8c49a;
  opacity: 0.85;
  margin-bottom: 0;
  letter-spacing: 1.2px;
  font-weight: 500;
  text-shadow: 0 1px 4px rgba(0,0,0,0.18);
}
.box-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.box-info {
  padding: 12px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.box-name {
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.box-code {
  font-size: 12px;
  color: #d4a574;
  margin-bottom: 6px;
}
.open-btn {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 8px;
  background: linear-gradient(90deg,#d4a574,#e8c49a);
  color: #fff;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  align-self: flex-start;
}
.open-btn:disabled {
  background: #aaa;
  color: #eee;
  cursor: not-allowed;
}
.loading, .empty, .no-more {
  text-align: center;
  color: #a39d8e;
  margin: 18px 0;
  font-size: 14px;
}

/* 新增弹窗样式 */
.modal-mask {
  position: fixed;
  z-index: 9999;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.45);
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-container {
  background: #232323;
  border-radius: 12px;
  padding: 28px 24px 18px 24px;
  min-width: 260px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.25);
  text-align: center;
}
.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #d4a574;
}
.modal-content {
  font-size: 15px;
  color: #fff;
  margin-bottom: 18px;
}
.modal-actions {
  display: flex;
  justify-content: center;
  gap: 18px;
}
.modal-btn {
  font-size: 14px;
  padding: 6px 22px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}
.modal-btn.confirm {
  background: linear-gradient(90deg,#d4a574,#e8c49a);
  color: #fff;
}
.modal-btn.cancel {
  background: #aaa;
  color: #fff;
}

/* 开启loading动画样式 */
.open-loading {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
}
.open-loading i {
  font-size: 15px;
  color: #fff;
}

/* 中奖奖品弹窗样式 */
.prize-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin: 18px 0 10px 0;
}
.prize-cover {
  width: 90px;
  height: 90px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(212,165,116,0.18);
  background: #222;
}
.prize-name {
  font-size: 16px;
  font-weight: 700;
  color: #d4a574;
  text-align: center;
  text-shadow: 0 2px 8px rgba(0,0,0,0.12);
}
</style> 