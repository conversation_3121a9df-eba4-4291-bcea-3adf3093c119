<template>
  <div class="design-system-container">
    <div class="design-system-inner">
      <div class="header">
        <h1>太阳神鸟设计系统</h1>
        <p>数字资产平台 - 金沙文化主题设计语言与组件规范</p>
      </div>
      
      <!-- 文字样式 -->
      <div class="section">
        <h2 class="section-title"><i class="fas fa-text-height"></i>文字样式</h2>
        <div class="grid">
          <div class="card">
            <h3 class="card-title"><i class="fas fa-heading"></i>标题文字</h3>
            <div class="example-area">
              <div class="text-large">大标题文字 (22px)</div>
              <div class="text-medium">中标题文字 (18px)</div>
            </div>
            <div class="code">
.text-large {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}
            </div>
          </div>
          
          <div class="card">
            <h3 class="card-title"><i class="fas fa-paragraph"></i>正文文字</h3>
            <div class="example-area">
              <div class="text-normal">普通正文文字 (15px)</div>
              <div class="text-small">辅助说明文字 (12px)</div>
            </div>
            <div class="code">
.text-normal {
  font-size: 15px;
  color: var(--text-secondary);
}

.text-small {
  font-size: 12px;
  color: var(--text-tertiary);
}
            </div>
          </div>
          
          <div class="card">
            <h3 class="card-title"><i class="fas fa-palette"></i>强调文字</h3>
            <div class="example-area">
              <div class="text-primary">主色调强调文字(金色)</div>
              <div class="text-accent">强调色文字(红色)</div>
              <div class="text-success">成功状态文字</div>
              <div class="text-warning">警告状态文字</div>
            </div>
            <div class="code">
.text-primary { color: var(--primary-color); }
.text-accent { color: var(--accent-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
            </div>
          </div>
        </div>
      </div>
      
      <!-- 按钮样式 -->
      <div class="section">
        <h2 class="section-title"><i class="fas fa-hand-pointer"></i>按钮样式</h2>
        <div class="grid">
          <div class="card">
            <h3 class="card-title"><i class="fas fa-button"></i>主要按钮</h3>
            <div class="example-area">
              <div class="btn-group">
                <DSButton type="primary">金色主题按钮</DSButton>
                <DSButton type="primary" size="small">小按钮</DSButton>
                <DSButton type="primary" size="large">大按钮</DSButton>
              </div>
              <DSButton type="primary" icon="fas fa-sun">太阳神鸟</DSButton>
            </div>
            <div class="code">
&lt;DSButton type="primary"&gt;金色主题按钮&lt;/DSButton&gt;
&lt;DSButton type="primary" size="small"&gt;小按钮&lt;/DSButton&gt;
&lt;DSButton type="primary" icon="fas fa-sun"&gt;太阳神鸟&lt;/DSButton&gt;
            </div>
          </div>
          
          <div class="card">
            <h3 class="card-title"><i class="fas fa-square"></i>次要按钮</h3>
            <div class="example-area">
              <div class="btn-group">
                <DSButton type="secondary">次要按钮</DSButton>
                <DSButton type="accent">强调按钮</DSButton>
              </div>
              <DSButton type="secondary" icon="fas fa-feather-alt">神鸟羽毛</DSButton>
            </div>
            <div class="code">
&lt;DSButton type="secondary"&gt;次要按钮&lt;/DSButton&gt;
&lt;DSButton type="accent"&gt;强调按钮&lt;/DSButton&gt;
&lt;DSButton type="secondary" icon="fas fa-feather-alt"&gt;神鸟羽毛&lt;/DSButton&gt;
            </div>
          </div>
        </div>
      </div>
      
      <!-- 标签样式 -->
      <div class="section">
        <h2 class="section-title"><i class="fas fa-tags"></i>标签样式</h2>
        <div class="grid">
          <div class="card">
            <h3 class="card-title"><i class="fas fa-tag"></i>状态标签</h3>
            <div class="example-area">
              <div style="text-align: center;">
                <DSTag type="primary">金沙文化</DSTag>
                <DSTag type="success">精选藏品</DSTag>
                <DSTag type="warning">优先购</DSTag>
                <DSTag type="accent">限量发行</DSTag>
                <DSTag type="primary" size="large">太阳神鸟</DSTag>
              </div>
            </div>
            <div class="code">
&lt;DSTag type="primary"&gt;金沙文化&lt;/DSTag&gt;
&lt;DSTag type="success"&gt;精选藏品&lt;/DSTag&gt;
&lt;DSTag type="warning"&gt;优先购&lt;/DSTag&gt;
&lt;DSTag type="accent"&gt;限量发行&lt;/DSTag&gt;
&lt;DSTag type="primary" size="large"&gt;太阳神鸟&lt;/DSTag&gt;
            </div>
          </div>
        </div>
      </div>
      
      <!-- 卡片组件 -->
      <div class="section">
        <h2 class="section-title"><i class="fas fa-layer-group"></i>卡片组件</h2>
        <div class="grid">
          <div class="card">
            <h3 class="card-title"><i class="fas fa-box"></i>藏品卡片</h3>
            <div class="example-area">
              <DSProductCard
                name="金沙太阳神鸟金饰"
                price="￥99.00"
                tag="限量"
                sale-time="12:00开售"
                image-placeholder="太阳神鸟图案"
                :tags="[
                  { text: '文化遗产', type: 'primary' },
                  { text: '优先购', type: 'warning' }
                ]"
              />
            </div>
            <div class="code">
&lt;DSProductCard
  name="金沙太阳神鸟金饰"
  price="￥99.00"
  tag="限量"
  sale-time="12:00开售"
  :tags="[
    { text: '文化遗产', type: 'primary' },
    { text: '优先购', type: 'warning' }
  ]"
/&gt;
            </div>
          </div>
          
          <div class="card">
            <h3 class="card-title"><i class="fas fa-stopwatch"></i>倒计时卡片</h3>
            <div class="example-area">
              <DSCountdownCard
                title="太阳神鸟藏品发售倒计时"
                label="即将开启"
              />
            </div>
            <div class="code">
&lt;DSCountdownCard
  title="太阳神鸟藏品发售倒计时"
  label="即将开启"
/&gt;
            </div>
          </div>
          
          <div class="card">
            <h3 class="card-title"><i class="fas fa-chart-bar"></i>统计卡片</h3>
            <div class="example-area">
              <DSStatsCard :stats="statsData" />
            </div>
            <div class="code">
&lt;DSStatsCard :stats="[
  { value: '3000份', label: '限量发行' },
  { value: '￥99.00', label: '价格' },
  { value: '8000+', label: '关注' }
]" /&gt;
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { DSButton, DSTag, DSProductCard, DSCountdownCard, DSStatsCard } from '../components/design-system'

const statsData = [
  { value: '3000份', label: '限量发行' },
  { value: '￥99.00', label: '价格' },
  { value: '8000+', label: '关注' }
]
</script>

<style scoped>
.header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-xl) 0;
  border-bottom: 1px solid var(--border-primary);
  background: linear-gradient(135deg, rgba(139, 0, 0, 0.1), rgba(184, 134, 11, 0.1));
  border-radius: var(--radius-xl);
}

.header h1 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 10px;
  text-shadow: 0 2px 5px rgba(218, 165, 32, 0.3);
  padding: 0 var(--spacing-md);
}

.header p {
  font-size: 16px;
  color: var(--text-tertiary);
  max-width: 100%;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.section {
  background: var(--bg-section);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xxl);
  border: 1px solid var(--border-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background-image: radial-gradient(circle at 10% 20%, rgba(218, 165, 32, 0.05) 0%, rgba(139, 0, 0, 0.05) 90%);
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: 18px;
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--border-primary);
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 10px;
  font-size: 22px;
  color: var(--text-tertiary);
}

.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.card {
  background: linear-gradient(145deg, #2a201e, #352520);
  border-radius: 12px;
  padding: 18px;
  border: 1px solid rgba(218, 165, 32, 0.3);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(218, 165, 32, 0.2);
  border-color: rgba(218, 165, 32, 0.5);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #f0e0d0;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.card-title i {
  margin-right: 8px;
  color: #d4af37;
  font-size: 18px;
}

.example-area {
  padding: 16px;
  background: rgba(60, 40, 30, 0.5);
  border-radius: 8px;
  margin: 12px 0;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px dashed rgba(218, 165, 32, 0.3);
}

.code {
  background: rgba(20, 10, 5, 0.4);
  padding: 12px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #d4af37;
  margin-top: 12px;
  width: 100%;
  overflow-x: auto;
  border: 1px solid rgba(218, 165, 32, 0.2);
  white-space: pre-wrap;
}

/* 文字样式 */
.text-large {
  font-size: 22px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 8px 0;
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 8px 0;
}

.text-normal {
  font-size: 15px;
  color: var(--text-secondary);
  margin: 6px 0;
}

.text-small {
  font-size: 12px;
  color: var(--text-tertiary);
  margin: 6px 0;
}

.text-primary {
  color: var(--primary-color);
}

.text-accent {
  color: var(--accent-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 10px 24px;
  border-radius: 24px;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  margin: 4px;
  min-width: 120px;
}

.btn-primary {
  background: linear-gradient(135deg, #daa520, #b8860b);
  color: #2a1616;
  box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
}

.btn-primary:hover,
.btn-primary:active {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
  background: linear-gradient(135deg, #e6b422, #c99a10);
}

.btn-secondary {
  background: rgba(60, 40, 30, 0.7);
  color: #f0e0d0;
  border: 1px solid rgba(218, 165, 32, 0.3);
}

.btn-secondary:hover,
.btn-secondary:active {
  background: rgba(80, 60, 40, 0.7);
  border-color: #daa520;
}

.btn-accent {
  background: linear-gradient(135deg, #b22222, #8b0000);
  color: #f8f0e0;
  box-shadow: 0 3px 10px rgba(178, 34, 34, 0.4);
}

.btn-accent:hover,
.btn-accent:active {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(178, 34, 34, 0.6);
}

.btn-small {
  padding: 6px 16px;
  font-size: 13px;
  min-width: auto;
}

.btn-large {
  padding: 14px 32px;
  font-size: 16px;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 3px 10px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
  margin: 2px;
}

.tag-primary {
  background: rgba(218, 165, 32, 0.2);
  color: #daa520;
  border: 1px solid rgba(218, 165, 32, 0.4);
}

.tag-accent {
  background: rgba(178, 34, 34, 0.2);
  color: #b22222;
  border: 1px solid rgba(178, 34, 34, 0.4);
}

.tag-success {
  background: rgba(143, 188, 143, 0.2);
  color: #8fbc8f;
  border: 1px solid rgba(143, 188, 143, 0.4);
}

.tag-warning {
  background: rgba(205, 133, 63, 0.2);
  color: #cd853f;
  border: 1px solid rgba(205, 133, 63, 0.4);
}

.tag-large {
  padding: 5px 12px;
  font-size: 13px;
  border-radius: 16px;
}

/* 卡片样式 */
.product-card {
  background: linear-gradient(145deg, #2a201e, #352520);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
  border: 1px solid rgba(218, 165, 32, 0.3);
  max-width: 100%;
  margin: 0 auto;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 18px rgba(218, 165, 32, 0.4);
  border-color: rgba(218, 165, 32, 0.5);
}

.product-image {
  width: 100%;
  height: 140px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #daa520;
  font-size: 13px;
}

.product-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #b22222;
  color: #f8f0e0;
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 16px;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.product-info {
  padding: 12px;
}

.product-name {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 6px;
  color: #f8f0e0;
  line-height: 1.4;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.price {
  font-size: 16px;
  font-weight: bold;
  color: #b22222;
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.sale-time {
  font-size: 11px;
  color: #d4af37;
  background: rgba(100, 80, 60, 0.3);
  padding: 3px 8px;
  border-radius: 10px;
  margin-left: auto;
}

/* 倒计时组件 */
.countdown-card {
  background: linear-gradient(135deg, #2a1b0c, #1a1006);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(218, 165, 32, 0.25);
  position: relative;
  overflow: hidden;
  max-width: 100%;
  margin: 0 auto;
}

.countdown-title {
  font-size: 16px;
  font-weight: bold;
  color: #daa520;
  margin-bottom: 12px;
  position: relative;
  z-index: 1;
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #f8f0e0;
  position: relative;
  z-index: 1;
  letter-spacing: 1px;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 10px rgba(218, 165, 32, 0.7);
  margin: 8px 0;
}

.countdown-timer span {
  display: inline-block;
  min-width: 36px;
  text-align: center;
  background: rgba(60, 30, 0, 0.7);
  padding: 4px;
  border-radius: 6px;
  margin: 0 2px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.countdown-label {
  font-size: 13px;
  color: #daa520;
  margin-top: 4px;
  position: relative;
  z-index: 1;
}

/* 统计卡片 */
.stats-card {
  display: flex;
  justify-content: space-between;
  background: rgba(60, 40, 30, 0.7);
  border-radius: 10px;
  padding: 12px;
  border: 1px solid rgba(218, 165, 32, 0.3);
  max-width: 100%;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #f8f0e0;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #d4af37;
}

.divider {
  height: 32px;
  width: 1px;
  background: rgba(218, 165, 32, 0.3);
  margin: 0 8px;
}

/* 按钮组样式 */
.btn-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin: 8px 0;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
  
  .header h1 {
    font-size: 28px;
  }
  
  .section {
    padding: var(--spacing-xxl);
  }
}
</style> 