
甲方制定的方案的评分标准如下：
根据比选申请人提交的服务方案综合评分，包含： (1)功能开发服务方案；(2)项目实施计划；(3) 服务承诺方案 (网络安全应急、平台信息数据安全、售后服务及响应) ；
1.对本项目需求分析深入透彻；技术方案设计完善、逻辑清晰，且可执行性强；服务细节考虑周到、分工明确；满足项目时限要求，完全满足采购人项目特点和采购文件要求的，得（35，45]分；
2.对本项目需求分析深入透彻；技术方案设计完善、逻辑清晰，且可执行性强；服务细节考虑周到、分工明确；满足项目时限要求，比较满足采购人项目特点和采购文件要求的，得（20，35]分；
3.对本项目需求分析深入透彻；技术方案设计完善、逻辑清晰，且可执行性强；服务细节考虑周到、分工明确；满足项目时限要求，基本满足采购人项目特点和采购文件要求的，得（0，20]分；
4.技术方案完全不满足采购文件要求或未提供技术方案，得0分。


甲方制定的技术服务要求：
（一）负责开发建设“凌云数资”（暂定名）系统（以下简称“系统”）基础功能，建设内容如下：
1. 用户管理。开发用户注册、登录、密码管理、实名认证账号注销等功能，确保用户身份的真实性和安全性。提供用户管理权限管理、角色管理及多渠道用户登录等功能，方便平台管理员进行高效的用户数据管理和维护。
2. H5移动端。包括首页、资产和我的相关页面功能。“首页”集成banner区展示资产展示和检索、即将发售和热门推荐功能。“资产”包括资产分类展示、资产详情浏览、资产收藏与分享、资产购买（含支付流程）。“我的”包括我的资料、我的资产、实名认证、消息中心、我的订单、我的预约、我的权益、帮助中心、邀请好友和关于我们的相关功能。为终端用户提供便捷的数字资产服务入口与交互体验。
3. BI大屏。实现包括平台交易数据、平台健康度、用户行为分析、发行方数据统计等相关业务数据的可视化展示。为平台运营与决策提供实时、多维度的数据洞察。
4. 数字资产管理。实现数字资产的全生命周期管理。包括资产系列管理、资产发行管理、资产发行审核的数字资产管理相关能力。
5. 交易订单管理。实现数字资产交易和交易订单信息的全流程管理、状态跟踪与信息查询。
6. 发行方信息管理。实现发行方基本信息、发行方关联数字资产的管理和查看。
7. 发售规则和空投管理。发售规则管理要实现通过可视化交互方式配置规则和管理。提供灵活、可视化的规则配置引擎，发售模式支持优先购、限时购方式。空投管理要实现策划、配置、审核、执行并管理面向用户的空投活动。
8. 内容运营管理。管理平台核心展示位的运营内容，提升用户关注度与转化。包括banner区运营和热门资产推荐运营的管理和查看。
9. 权益管理。建立并运营用户权益体系，管理权益的发放、流转与核销。包括对权益库的管理、权益的发放、库存和核销记录管理。
10. 专区运营商及专区管理。支持第三方专区运营商入驻并管理其专区。专区运营商实现专区运营商入驻、运营商的信息维护管理。专区管理实现的专区信息及管理运营商的管理和相关数据查看。
11. 系统管理。实现包括用户管理、角色权限管理、菜单管理、日志管理、字典管理、消息管理以及系统监控的功能。为系统的整体运行提供基础设置及维保功能。
12. 系统合规和安全。系统的安全架构、技术架构及整体安全能力须满足网络安全等级保护(三级)要求，在系统设计和开发阶段全面考虑并实施安全策略和合规措施，架构应采用业界领先的安全设计模式，包括但不限于:集成多种安全验证机制，如短信验证码、密码验证等，保障用户数据的安全性和隐私性;涉及个人信息、敏感数据的，加密存储和处理;采用业内通用的安全协议、技术和措施实现平台在信息收集、存储、传输、处理、分析过程中的安全性。集成传输层安全协议(如TLS)，实现数据传输加密;设计并实现多因素认证(MFA)机制，支持灵活的身份验证策略:构建细粒度的基于角色的访问控制(RBAC)系统，实现严格的权限管理。设计数据加密存储方案，实现敏感信息脱敏功能，并提供数据备份与恢复接口:集成日志记录和审计跟踪功能，为后续的安全监控提供基础设施。整体上，系统设计应符合相关的数据保护法规和行业标准，如《网络安全法》《数据安全法》《个人信息保护法》等。
（二）开发建设原则
1.可扩展性原则
具备相应的灵活性和可扩展性，能够适应一定时间内的需求变化，可扩展性强，并保证在进行扩展时，不影响系统核心功能的正常运行。
2.安全可靠性原则
设计和建设时必须从多方面进行全面考虑，通过备份、事故监控和安全等多种技术措施，提供较强的管理机制和控制手段，保证系统运行的安全及正常运行。系统故障恢复时间不得超过【48】小时，且在发生故障时，系统应在【60】分钟内自动切换至备用系统。
3.数据安全与隐私保护原则
系统应确保数据在传输和存储过程中的安全性，采用加密技术和权限控制，防止数据泄露与篡改。系统设计严格遵守数据及隐私信息保护法规，确保平台数据及隐私信息在未经授权的情况下不会被访问、使用或共享。
4.实用性与成熟性原则
系统必须使用业界成熟、可靠和实用的技术，以满足系统的可用性、可靠性和实用性。
5.先进性原则
系统的建设必须采用相对先进的技术和方法，能够满足和适应系统快速变化和发展的要求。
6.开放性与标准化原则
系统建设所采用的产品及技术应该具有良好的开放性和兼容性，支持标准的协议和接口类型，提供多种开放的应用开发接口，能够和业界主流的产品实现高度整合。
7.自动化和操作的简单化原则
（三）技术要求
1.系统按照组件化、服务化的方法进行设计，遵循微服务的体系架构（MSA）；
2.遵守高内聚、低耦合的设计原则，功能和应用业务在逻辑上实现隔离，功能和数据在逻辑上和物理上实现隔离；
3.技术选型遵循稳定、安全、可靠、生态完整、易维护、易于扩展等原则；
4.所有业务系统和功能模块需按照功能清单进行开发；
5.平台业务基于微服务架构设计原则实现业务的服务化、组件化，避免传统烟囱式应用中资源难以共享、资源利用率低、无法快速响应客户需求变化等问题；
6.支持分布式、容器化部署；支持水平扩展，充分利用云平台和容器化技术的优势，建立弹性的应用与服务资源池，通过应用与服务的负载均衡策略提升系统的整体并发能力；
7.支持持续集成和持续交付，支持以容器为载体，实现软件系统从开发、测试到部署和运维的一体化管理，提高软件开发部署的效能，迅速响应业务的需求。
8.后端采用Java或Python开发语言，采用Spring、SpringMVC、SpringBoot、MyBatis等开发框架；
9.数据库采用MySQL、Redis等，并采用分布式缓存技术实现高性能要求；
10.系统部署架构要求设计合理、架构清晰、安全可靠；系统部署采用Linux、Anolis等环境；负载均衡采用Nginx、Ingress、OpenResty等高性能服务；运行容器采用Tomcat等高性能应用服务器；均需要在原有程序上做相关优化，以满足性能要求；
11.引入分布式缓存、消息队列、非结构化数据库、云存储等关键支撑组件，充分分析业务性能瓶颈找到关键支撑组件的应用场景，充分利用支撑组件的特性提升系统运行效率，充分发挥“云”环境优势。
12.系统整体性能要求支持并发1000以上，至少1000 TPS，支付处理响应时间不超过3秒，结算处理的批量任务应在4小时内完成（假设百万级订单），系统可用性达到99.99%，确保在任何情况下资金账目平衡，差错率不超过0.001%
（四）其他需求
1.页面提交平均响应时间≤3秒，查询操作响应时间≤3秒​​
2.项目完成后需提交第三方安全审计报告，如渗透测试报告、代码安全相关报告。
3.满足网络安全等级保护三级标准，并协助采购人完成等保三级备案。
一、商务要求
（一）服务期限
合同签订之日起至中选人完成本合同约定开发、运维质保（1年）等所有交付和服务之日，其中软件开发、部署等工作应在合同签订后 25 日历天内完成，并交付采购人运行。
（二）服务地点
采购人指定地点。
（三）验收要求
1.验收标准：该系统的功能、性能和质量应符合相关技术规范要求，且应运行稳定、安全可靠、无严重错误和缺陷。系统功能需完全符合各项服务内容及功能需求，包括但不限于用户管理、H5移动端、BI大屏、数字资产管理、交易订单管理、发行方信息管理、发售规则和空投管理、内容运营管理、权益管理、专区运营商及专区管理、系统管理等。
2.验收方式：中选人应在系统上线后5个工作日内向采购人并提交验收申请。采购人应在收到申请后【15】个工作日内对本系统进行验收，如验收合格，则签署验收合格报告；如验收不合格，则中选人应在采购人提出修改意见后【5】个工作日内进行修改，直至验收合格为止；如采购人超过验收期未向中选人反馈的，视为验收合格。
