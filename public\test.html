<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 凌云数资</title>
    <style>
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #e0e0e0;
            font-family: 'PingFang SC', Arial, sans-serif;
            padding: 20px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            text-align: center;
        }
        
        h1 {
            color: #4dabf7;
            margin-bottom: 20px;
        }
        
        .card {
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #333;
        }
        
        .btn {
            background: linear-gradient(135deg, #4dabf7, #1890ff);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            margin: 10px;
        }
        
        .price {
            font-size: 18px;
            color: #ff6b6b;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>凌云数资</h1>
        
        <div class="card">
            <h2>测试页面</h2>
            <p>这是一个测试页面，用于验证中文字符显示是否正常。</p>
            <div class="price">￥299.00</div>
            <button class="btn" onclick="testFunction()">点击测试</button>
        </div>
        
        <div class="card">
            <h3>功能测试</h3>
            <p>包含特殊字符：关键字、发行方、限量1000份</p>
            <p>价格符号：￥199.00</p>
            <p>中文标点：，。？！</p>
        </div>
    </div>
    
    <script>
        function testFunction() {
            alert('测试功能正常！页面可以正常显示中文字符。');
        }
        
        console.log('测试页面加载完成');
    </script>
</body>
</html> 