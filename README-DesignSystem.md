# 太阳神鸟设计系统

## 概述

太阳神鸟设计系统是一套基于金沙文化主题的Vue 3组件库和样式系统，专为数字资产平台设计。它提供了一致的视觉语言和可复用的组件，帮助开发者快速构建高质量的用户界面。

## 特性

- 🎨 **金沙文化主题** - 基于太阳神鸟等古蜀文明元素的设计语言
- 🧩 **组件化设计** - 可复用的Vue 3组件
- 🎯 **TypeScript支持** - 完整的类型定义
- 📱 **响应式设计** - 支持移动端和桌面端
- 🎭 **深色主题** - 专为数字艺术平台优化的视觉效果
- ⚡ **轻量高效** - 最小化的样式和组件代码

## 文件结构

```
src/
├── assets/styles/
│   └── design-system.css          # 通用样式文件
├── components/design-system/
│   ├── DSButton.vue              # 按钮组件
│   ├── DSTag.vue                 # 标签组件
│   ├── DSProductCard.vue         # 产品卡片组件
│   ├── DSCountdownCard.vue       # 倒计时卡片组件
│   ├── DSStatsCard.vue           # 统计卡片组件
│   └── index.ts                  # 组件导出文件
└── views/
    ├── DesignSystemView.vue      # 设计系统展示页
    └── ExampleView.vue           # 使用示例页
```

## 快速开始

### 1. 引入样式

在 `main.ts` 中引入设计系统样式：

```typescript
import './assets/styles/design-system.css'
```

### 2. 使用组件

```vue
<script setup lang="ts">
import { DSButton, DSTag, DSProductCard } from '@/components/design-system'
</script>

<template>
  <div class="design-system-container">
    <div class="design-system-inner">
      <!-- 页面内容 -->
    </div>
  </div>
</template>
```

## 核心组件

### DSButton 按钮组件

```vue
<!-- 基础用法 -->
<DSButton type="primary">主要按钮</DSButton>
<DSButton type="secondary">次要按钮</DSButton>
<DSButton type="accent">强调按钮</DSButton>

<!-- 不同尺寸 -->
<DSButton type="primary" size="small">小按钮</DSButton>
<DSButton type="primary" size="large">大按钮</DSButton>

<!-- 带图标 -->
<DSButton type="primary" icon="fas fa-sun">太阳神鸟</DSButton>
```

**Props:**
- `type?: 'primary' | 'secondary' | 'accent'` - 按钮类型
- `size?: 'small' | 'normal' | 'large'` - 按钮尺寸
- `icon?: string` - 图标类名

### DSTag 标签组件

```vue
<!-- 基础用法 -->
<DSTag type="primary">金沙文化</DSTag>
<DSTag type="success">精选藏品</DSTag>
<DSTag type="warning">优先购</DSTag>
<DSTag type="accent">限量发行</DSTag>

<!-- 大尺寸标签 -->
<DSTag type="primary" size="large">太阳神鸟</DSTag>
```

**Props:**
- `type?: 'primary' | 'accent' | 'success' | 'warning'` - 标签类型
- `size?: 'normal' | 'large'` - 标签尺寸

### DSProductCard 产品卡片组件

```vue
<DSProductCard
  name="金沙太阳神鸟金饰"
  price="￥99.00"
  tag="限量"
  sale-time="12:00开售"
  image-placeholder="太阳神鸟图案"
  :tags="[
    { text: '文化遗产', type: 'primary' },
    { text: '优先购', type: 'warning' }
  ]"
/>
```

**Props:**
- `name: string` - 产品名称
- `price: string` - 产品价格
- `tag?: string` - 产品标签
- `saleTime?: string` - 开售时间
- `imagePlaceholder?: string` - 图片占位文字
- `tags?: TagItem[]` - 标签列表

### DSCountdownCard 倒计时卡片组件

```vue
<DSCountdownCard
  title="太阳神鸟藏品发售倒计时"
  label="即将开启"
  :duration="19 * 60 * 60 * 1000"
/>
```

**Props:**
- `title: string` - 倒计时标题
- `label?: string` - 标签文字
- `endTime?: Date | number` - 结束时间
- `duration?: number` - 持续时间（毫秒）

### DSStatsCard 统计卡片组件

```vue
<DSStatsCard :stats="[
  { value: '3000份', label: '限量发行' },
  { value: '￥99.00', label: '价格' },
  { value: '8000+', label: '关注' }
]" />
```

**Props:**
- `stats: StatItem[]` - 统计数据数组
  - `StatItem: { value: string | number, label: string }`

## 样式系统

### CSS变量

设计系统使用CSS变量来管理主题：

```css
:root {
  /* 颜色变量 */
  --primary-color: #daa520;
  --accent-color: #b22222;
  --success-color: #8fbc8f;
  --warning-color: #cd853f;
  
  /* 文字颜色 */
  --text-primary: #f8f0e0;
  --text-secondary: #f0e0d0;
  --text-tertiary: #d4af37;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  
  /* 圆角 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 10px;
  --radius-xl: 12px;
  --radius-xxl: 16px;
  --radius-round: 24px;
}
```

### 布局类

```css
.design-system-container  /* 页面容器 */
.design-system-inner     /* 内容容器 */
.grid                    /* 网格布局 */
.btn-group              /* 按钮组 */
.example-area           /* 示例区域 */
.code                   /* 代码展示 */
```

### 文字样式类

```css
.text-large    /* 大标题 (22px) */
.text-medium   /* 中标题 (18px) */
.text-normal   /* 正文 (15px) */
.text-small    /* 小字 (12px) */

.text-primary  /* 主色调文字 */
.text-accent   /* 强调色文字 */
.text-success  /* 成功状态文字 */
.text-warning  /* 警告状态文字 */
```

## 最佳实践

### 1. 页面结构

每个页面都应该使用标准的设计系统容器：

```vue
<template>
  <div class="design-system-container">
    <div class="design-system-inner">
      <!-- 页面内容 -->
    </div>
  </div>
</template>
```

### 2. 组件命名

- 所有设计系统组件以 `DS` 前缀命名
- 使用PascalCase命名法
- 组件名应该具有描述性

### 3. 样式使用

- 优先使用CSS变量而不是硬编码的颜色值
- 使用标准化的间距变量
- 遵循响应式设计原则

### 4. 组件开发

- 使用TypeScript定义Props接口
- 提供合理的默认值
- 支持插槽（slot）扩展
- 遵循Vue 3 Composition API规范

## 示例页面

- `/design-system` - 设计系统展示页面，包含所有组件的使用示例
- `/example` - 实际应用示例，展示如何在项目中使用设计系统

## 贡献指南

1. 新增组件应放在 `src/components/design-system/` 目录
2. 样式应使用CSS变量保持一致性
3. 组件应提供完整的TypeScript类型定义
4. 更新 `index.ts` 导出文件
5. 在设计系统页面添加使用示例

## 技术栈

- Vue 3
- TypeScript
- CSS Variables
- Font Awesome Icons
- Vite

## 浏览器支持

- Chrome >= 88
- Firefox >= 78
- Safari >= 14
- Edge >= 88 
 