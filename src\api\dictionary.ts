import request from './request'
import type { ApiResponse } from './types'

/**
 * 原始字典项数据结构（API返回格式）
 */
export interface RawDictionaryItem {
  dictCode: number
  dictSort: number
  dictLabel: string
  dictValue: string
  dictType: string
  cssClass?: string
  listClass?: string
  isDefault: string
  status: string
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  remark?: string
}

/**
 * 处理后的字典项数据结构（组件使用格式）
 */
export interface DictionaryItem {
  value: string
  label: string
  code: string
  type: string
  sort: number
  isDefault: boolean
  status: 'active' | 'inactive'
  remark?: string
}

/**
 * 字典查询原始响应
 */
export interface RawDictionaryResponse extends ApiResponse {
  data: RawDictionaryItem[]
}

/**
 * 字典查询处理后响应
 */
export interface DictionaryResponse extends ApiResponse {
  data: DictionaryItem[]
}

/**
 * 省市区查询原始响应数据结构
 */
export interface RawRegionItem {
  regionId: number
  parRegionId: number
  regionName: string
  regionLevel: number
  regionType: number
  regionDesc: string
  regionSort: number
  regionLong: number
  createStaff: number
  updateStaff: number
  createDate: string
  statusDate: string
  updateDate: string
  remark: string
}

/**
 * 省市区查询响应
 */
export interface RegionResponse {
  total: number
  rows: RawRegionItem[]
  code: number
  msg: string
}

/**
 * 省市区查询参数
 */
export interface RegionQueryParams {
  parRegionId?: string | number
  pageNum?: string | number
  regionLevel?: string | number
  pageSize?: string | number
}

/**
 * 字典查询API服务
 */
export class DictionaryAPI {
  /**
   * 转换原始字典数据为组件使用格式
   */
  private static transformDictionaryData(rawItems: RawDictionaryItem[]): DictionaryItem[] {
    return rawItems
      .filter(item => item.status === '0') // 过滤启用状态的字典项
      .sort((a, b) => a.dictSort - b.dictSort) // 按排序字段排序
      .map(item => ({
        value: item.dictValue,
        label: item.dictLabel,
        code: item.dictCode.toString(),
        type: item.dictType,
        sort: item.dictSort,
        isDefault: item.isDefault === 'Y',
        status: item.status === '0' ? 'active' : 'inactive',
        remark: item.remark
      }))
  }

  /**
   * 根据字典类型获取字典数据
   */
  static async getDictionary(dictType: string): Promise<DictionaryResponse> {
    try {
      const rawResponse = await request.get<RawDictionaryResponse>(`/system/dict/data/type/${dictType}`)
      
      // 转换数据格式
      const transformedData = this.transformDictionaryData(rawResponse.data || [])
      
      return {
        ...rawResponse,
        data: transformedData
      }
    } catch (error) {
      console.error(`Failed to fetch dictionary data for type: ${dictType}`, error)
      throw error
    }
  }

  /**
   * 获取证件类型列表
   */
  static async getIdTypes(): Promise<DictionaryResponse> {
    return this.getDictionary('id_type')
  }

  /**
   * 获取个人证件类型列表
   */
  static async getPersonIdTypes(): Promise<DictionaryResponse> {
    return this.getDictionary('id_type_person')
  }

  /**
   * 获取订单状态字典
   */
  static async getOrderStatusTypes(): Promise<DictionaryResponse> {
    return this.getDictionary('dig_order_status')
  }

  /**
   * 转换省市区数据为下拉框格式
   */
  private static transformRegionData(rawItems: RawRegionItem[]): DictionaryItem[] {
    return rawItems
      .sort((a, b) => a.regionSort - b.regionSort) // 按排序字段排序
      .map(item => ({
        value: item.regionId.toString(),
        label: item.regionName,
        code: item.regionId.toString(),
        type: `region_level_${item.regionLevel}`,
        sort: item.regionSort,
        isDefault: false,
        status: 'active' as const,
        remark: item.remark
      }))
  }

  /**
   * 查询省市区数据
   */
  static async getRegions(params: RegionQueryParams): Promise<DictionaryResponse> {
    try {
      const queryParams: Record<string, any> = {}
      
      if (params.parRegionId !== undefined) {
        queryParams.parRegionId = params.parRegionId.toString()
      }
      if (params.pageNum !== undefined) {
        queryParams.pageNum = params.pageNum.toString()
      }
      if (params.regionLevel !== undefined) {
        queryParams.regionLevel = params.regionLevel.toString()
      }
      if (params.pageSize !== undefined) {
        queryParams.pageSize = params.pageSize.toString()
      }

      const response = await request.get<RegionResponse>('/copyright/region/page', queryParams)
      
      // 转换数据格式
      const transformedData = this.transformRegionData(response.rows || [])
      
      return {
        msg: response.msg,
        code: response.code,
        data: transformedData
      }
    } catch (error) {
      console.error('Failed to fetch region data', error)
      throw error
    }
  }

  /**
   * 获取省份列表
   */
  static async getProvinces(): Promise<DictionaryResponse> {
    return this.getRegions({
      pageSize: 100,
      pageNum: 1,
      regionLevel: 1
    })
  }

  /**
   * 根据省份ID获取城市列表
   */
  static async getCitiesByProvinceId(provinceId: string | number): Promise<DictionaryResponse> {
    return this.getRegions({
      parRegionId: provinceId,
      pageSize: 100,
      pageNum: 1,
      regionLevel: 2
    })
  }

  /**
   * 根据城市ID获取区县列表
   */
  static async getDistrictsByCityId(cityId: string | number): Promise<DictionaryResponse> {
    return this.getRegions({
      parRegionId: cityId,
      pageSize: 500,
      pageNum: 1,
      regionLevel: 3
    })
  }

  /**
   * 获取字典项的默认值
   */
  static getDefaultValue(items: DictionaryItem[]): string | null {
    const defaultItem = items.find(item => item.isDefault)
    return defaultItem ? defaultItem.value : null
  }

  /**
   * 根据值获取标签
   */
  static getLabelByValue(items: DictionaryItem[], value: string): string | null {
    const item = items.find(item => item.value === value)
    return item ? item.label : null
  }

  /**
   * 根据标签获取值
   */
  static getValueByLabel(items: DictionaryItem[], label: string): string | null {
    const item = items.find(item => item.label === label)
    return item ? item.value : null
  }
}

export default DictionaryAPI 