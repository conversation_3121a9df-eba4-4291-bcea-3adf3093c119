<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { PresaleAPI, type PresaleAsset } from '@/api'
import { useNotification } from '@/composables/useNotification'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 扩展的预售资产类型，包含UI所需字段
interface ExtendedPresaleAsset extends PresaleAsset {
  image: string
  title: string
  price: string
  quantity: string
  publisher: string
  publisherLogo: string
  keywordTags: string[]
  description: string
  salesProgress: {
    total: number
    sold: number
    remaining: number
  }
  purchaseLimit: string
  tags: Array<{ text: string; type: string }>
  saleTime: string
  endTime: Date
  countdownTitle: string
  reserved: boolean
}

// 路由和通知
const router = useRouter()
const { success, error, info } = useNotification()

// 搜索相关
const searchType = ref('名称')
const searchText = ref('')

// 预售商品数据
const presaleItems = ref<ExtendedPresaleAsset[]>([])
const isLoading = ref(false)

// 加载预售数据
const loadPresaleData = async () => {
  try {
    isLoading.value = true
    console.log('📡 开始请求预售数据...')
    
    const response = await PresaleAPI.getPresaleList()
    console.log('📥 预售数据API响应:', response)
    
    if (response.code === 200 && response.rows && response.rows.length > 0) {
      console.log('📋 开始处理预售数据，原始数据长度:', response.rows.length)
      
      presaleItems.value = response.rows.map((item, index) => {
        console.log(`🔍 处理第${index + 1}项数据:`, {
          assetName: item.assetName,
          assetCover: item.assetCover,
          assetKeywords: item.assetKeywords,
          issuePrice: item.issuePrice,
          issuePriceType: typeof item.issuePrice,
          issuers: item.issuers,
          assetDescription: item.assetDescription,
          issueQuantity: item.issueQuantity,
          soldQuantity: item.soldQuantity,
          remainingQuantity: item.remainingQuantity,
          personalLimit: item.personalLimit,
          assetType: item.assetType,
          assetLevel: item.assetLevel,
          saleStartTime: item.saleStartTime,
          saleEndTime: item.saleEndTime
        })
        
        const extendedItem: ExtendedPresaleAsset = {
          ...item,
          // 使用真实API字段，增加安全检查
          image: item.assetCover || item.assetName || '默认封面',
          title: item.assetName || '未知资产',
          price: (() => {
            // 判断是否已到开售时间
            const isSaleStarted = (() => {
              if (!item.saleStartTime) return true // 没有开售时间则默认已开售
              const saleTime = new Date(item.saleStartTime)
              const now = new Date()
              return saleTime <= now
            })()
            
            // 如果未到开售时间，显示???
            if (!isSaleStarted) {
              return '¥-.-'
            }
            
            // 已开售时显示实际价格
            if (item.issuePrice !== null && item.issuePrice !== undefined && typeof item.issuePrice === 'number') {
              return `¥${item.issuePrice.toFixed(2)}`
            }
            return '价格待定'
          })(),
          quantity: (() => {
            // 判断是否已到开售时间
            const isSaleStarted = (() => {
              if (!item.saleStartTime) return true // 没有开售时间则默认已开售
              const saleTime = new Date(item.saleStartTime)
              const now = new Date()
              return saleTime <= now
            })()
            
            // 如果未到开售时间，显示???
            if (!isSaleStarted) {
              return '限量???件'
            }
            
            // 已开售时显示实际数量
            if (item.issueQuantity && typeof item.issueQuantity === 'number') {
              return `限量${item.issueQuantity}件`
            }
            return '限量发行'
          })(),
          // 处理发行方信息（数组格式）
          publisher: item.issuers && item.issuers.length > 0 
            ? item.issuers.map(issuer => issuer.issuerName).join('、') 
            : '官方发行',
          // 发行方logo（取第一个）
          publisherLogo: item.issuers && item.issuers.length > 0 
            ? item.issuers[0].issuerLogo || '' 
            : '',
          // 解析关键字标签
          keywordTags: item.assetKeywords 
            ? item.assetKeywords.split(',').filter(keyword => keyword.trim()).map(keyword => keyword.trim())
            : [],
          // 添加资产描述
          description: item.assetDescription || '',
          // 添加销售进度信息
          salesProgress: {
            total: item.issueQuantity || 0,
            sold: item.soldQuantity || 0,
            remaining: item.remainingQuantity || item.issueQuantity || 0
          },
          // 添加限购信息
          purchaseLimit: item.personalLimit ? `每人限购${item.personalLimit}件` : '',
          tags: generateTags(item),
          saleTime: formatSaleTime(item.saleStartTime),
          // 使用发售时间作为倒计时目标
          endTime: item.saleStartTime 
            ? new Date(item.saleStartTime) 
            : new Date(Date.now() + 24 * 60 * 60 * 1000),
          // 倒计时标题
          countdownTitle: '开售倒计时',
          reserved: false
        }
        
        console.log(`✨ 第${index + 1}项数据处理结果 (左右布局):`, {
          title: extendedItem.title,
          image: extendedItem.image,
          price: extendedItem.price,
          quantity: extendedItem.quantity,
          publisher: extendedItem.publisher,
          publisherLogo: extendedItem.publisherLogo,
          keywordTags: extendedItem.keywordTags,
          description: extendedItem.description ? `${extendedItem.description.substring(0, 50)}...` : '无描述',
          salesProgress: extendedItem.salesProgress,
          purchaseLimit: extendedItem.purchaseLimit,
          tags: extendedItem.tags.map(tag => tag.text),
          saleTime: extendedItem.saleTime,
          countdownTitle: extendedItem.countdownTitle,
          layoutStructure: '左侧:封面+价格标签 | 右侧:标题+发行方+数量+限购+关键字+开售时间+预约按钮'
        })
        
        return extendedItem
      })
      console.log('✅ 预售数据加载成功:', presaleItems.value.length, '条')
    } else {
      console.warn('⚠️ 预售接口返回空数据，响应详情:', { 
        code: response.code, 
        hasRows: !!response.rows, 
        rowsLength: response.rows?.length || 0 
      })
      presaleItems.value = []
    }
  } catch (err) {
    console.error('❌ 获取预售数据失败:', err)
    error('获取预售数据失败，请稍后重试')
    presaleItems.value = []
  } finally {
    isLoading.value = false
  }
}

// 生成标签
const generateTags = (item: PresaleAsset) => {
  const tags = []
  
  // 安全检查 isLimited 字段
  if (item.isLimited === true) {
    tags.push({ text: '限量发行', type: 'accent' })
  }
  
  // 安全检查 assetType 字段
  if (item.assetType && typeof item.assetType === 'string') {
    tags.push({ text: getAssetTypeText(item.assetType), type: 'primary' })
  }
  
  // 添加资产等级标签
  if (item.assetLevel && typeof item.assetLevel === 'string') {
    tags.push({ text: item.assetLevel, type: 'info' })
  }
  
  // 根据开售时间判断状态，增加安全检查
  if (item.saleStartTime && typeof item.saleStartTime === 'string') {
    try {
      const saleTime = new Date(item.saleStartTime)
      const now = new Date()
      
      // 验证日期是否有效
      if (!isNaN(saleTime.getTime()) && saleTime > now) {
        const timeDiff = saleTime.getTime() - now.getTime()
        const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
        
        if (daysDiff <= 1) {
          tags.push({ text: '即将开售', type: 'warning' })
        } else if (daysDiff <= 7) {
          tags.push({ text: '预售中', type: 'success' })
        } else {
          tags.push({ text: '即将上线', type: 'warning' })
        }
      }
    } catch (error) {
      console.warn('⚠️ 解析开售时间失败:', item.saleStartTime, error)
    }
  }
  
  return tags
}

// 获取资产类型文本
const getAssetTypeText = (assetType: string) => {
  const typeMap: Record<string, string> = {
    'A': '文俗',
    'B': '文旅', 
    'C': '非遗',
    'D': '动漫',
    'E': '音乐'
  }
  return typeMap[assetType] || '数字资产'
}

// 格式化开售时间
const formatSaleTime = (timeStr?: string) => {
  if (!timeStr) return '开售时间待定'
  
  try {
    const date = new Date(timeStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).replace(/\//g, '年').replace(/-/g, '月').replace(/\s/, '日') + ' 开售'
  } catch (error) {
    return '开售时间待定'
  }
}

// 倒计时相关
const countdowns = ref<{[key: number]: { hours: string, minutes: string, seconds: string }}>({})
const timers = ref<{[key: number]: number}>({})

// 方法
const goBack = () => {
  router.push('/')
}

const handleSearch = () => {
  if (searchText.value.trim()) {
    console.log(`按${searchType.value}搜索预售资产: ${searchText.value}`)
    // TODO: 实现搜索功能
  }
}

const handleReserve = (item: ExtendedPresaleAsset) => {
  item.reserved = !item.reserved
  console.log(`${item.reserved ? '预约' : '取消预约'}: ${item.title}`)
  // TODO: 实现预约功能
}

const handleViewDetails = (item: ExtendedPresaleAsset) => {
  console.log(`查看详情: ${item.title}`)
  // 获取资产ID，优先使用assetId，备用id
  const assetId = item.assetId || item.id
  if (assetId) {
    // 跳转到资产详情页面
    router.push(`/asset/${String(assetId)}`)
  } else {
    error('资产信息不完整，无法查看详情')
  }
}

// 倒计时功能
const updateCountdown = (id: number, endTime: Date) => {
  const now = new Date().getTime()
  const distance = endTime.getTime() - now

  if (distance > 0) {
    const hours = Math.floor(distance / (1000 * 60 * 60))
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)

    countdowns.value[id] = {
      hours: hours.toString().padStart(2, '0'),
      minutes: minutes.toString().padStart(2, '0'),
      seconds: seconds.toString().padStart(2, '0')
    }
  } else {
    countdowns.value[id] = {
      hours: '00',
      minutes: '00',
      seconds: '00'
    }
  }
}

const startCountdowns = () => {
  presaleItems.value.forEach(item => {
    updateCountdown(item.id, item.endTime)
    timers.value[item.id] = setInterval(() => {
      updateCountdown(item.id, item.endTime)
    }, 1000)
  })
}

const stopCountdowns = () => {
  Object.values(timers.value).forEach(timer => {
    if (timer) clearInterval(timer)
  })
}

// 生命周期
onMounted(async () => {
  await loadPresaleData()
  startCountdowns()
})

onUnmounted(() => {
  stopCountdowns()
})
</script>

<template>
  <AppPageHeader :title="'即将发售'" @back="$router.back()" />
  <div class="presale-page" :style="{ paddingTop: '40px' }">

    <main class="main-content">
      <div class="container">
        <!-- 搜索区域 -->
        <!-- <section class="search-section">
          <div class="search-container">
            <select v-model="searchType" class="search-dropdown">
              <option>名称</option>
              <option>关键字</option>
              <option>发行方</option>
            </select>
            <input 
              v-model="searchText"
              type="text" 
              class="search-input" 
              placeholder="搜索预售资产..."
              @keypress.enter="handleSearch"
            >
            <button class="search-btn" @click="handleSearch">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </section> -->

        <!-- 提醒横幅 -->
        <!-- <div class="notification-bar">
          <i class="fas fa-bell text-warning"></i>
          <span class="text-small">预约成功后，我们将在开售前5分钟提醒您</span>
        </div> -->

        <!-- 预售列表 -->
        <section class="presale-section">
          <!-- 加载状态 -->
          <div v-if="isLoading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>加载预售数据中...</p>
          </div>
          
          <!-- 暂无数据状态 -->
          <div v-else-if="presaleItems.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-clock"></i>
            </div>
            <p>暂无预售资产</p>
          </div>
          
          <!-- 预售列表 -->
          <div 
            v-else
            v-for="item in presaleItems" 
            :key="item.id"
            class="presale-card"
            @click="handleViewDetails(item)"
          >
            <!-- 资产卡片内容 - 根据图片样式调整 -->
            <div class="asset-content">
              <!-- 左图右文布局 -->
              <div class="asset-layout">
                <!-- 左侧封面区域 -->
                <div class="asset-cover-section">
                  <div class="asset-cover-container">
                    <img 
                      v-if="item.image && item.image.startsWith('http')" 
                      :src="item.image" 
                      :alt="item.title" 
                      class="asset-cover-img"
                    />
                    <div v-else class="asset-cover-placeholder">
                      <i class="fas fa-image"></i>
                      <span>{{ item.title }}</span>
                    </div>
                  </div>
                </div>
                
                <!-- 右侧信息区域 -->
                <div class="asset-info-section">
                  <!-- 资产标题 -->
                  <h3 class="asset-title">{{ item.title }}</h3>
                  
                  <!-- 关键字标签 -->
                  <div v-if="item.keywordTags.length > 0" class="keyword-tags">
                    <span 
                      v-for="keyword in item.keywordTags.slice(0, 2)" 
                      :key="`${item.id}-keyword-${keyword}`"
                      class="keyword-tag"
                    >
                      #{{ keyword }}
                    </span>
                  </div>
                  
                  <!-- 限量信息 -->
                  <div class="quantity-display">
                    <i class="fas fa-gem"></i>
                    <span>{{ item.quantity }}</span>
                  </div>
                  
                  <!-- 价格显示 -->
                  <div class="price-display">
                    {{ item.price }}
                  </div>
                  
                  <!-- 开售时间 -->
                  <div class="sale-time-display">
                    {{ item.saleTime }}
                  </div>
                  
                  <!-- 预约按钮 -->
                  <!-- <div class="action-section">
                    <button 
                      class="reserve-button"
                      :class="{ reserved: item.reserved }"
                      @click.stop="handleReserve(item)"
                    >
                      <i class="fas fa-bell"></i>
                      {{ item.reserved ? '已预约' : '预约提醒' }}
                    </button>
                  </div> -->
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 底部导航 -->
    <!-- <nav class="bottom-nav">
      <div class="container">
        <div class="nav-items">
          <router-link to="/" class="nav-item" :class="{ active: $route.name === 'home' }">
            <i class="nav-icon fas fa-home"></i>
            <span class="nav-label">首页</span>
          </router-link>
          <router-link to="/exhibition" class="nav-item" :class="{ active: $route.name === 'exhibition' }">
            <i class="nav-icon fas fa-store"></i>
            <span class="nav-label">资产</span>
          </router-link>
          <router-link to="/activities" class="nav-item" :class="{ active: $route.name === 'activities' }">
            <i class="nav-icon fas fa-calendar-star"></i>
            <span class="nav-label">活动</span>
          </router-link>
          <router-link to="/profile" class="nav-item" :class="{ active: $route.name === 'profile' }">
            <i class="nav-icon fas fa-user"></i>
            <span class="nav-label">我的</span>
          </router-link>
        </div>
      </div>
    </nav> -->
  </div>
</template>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.presale-page {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 页面头部样式 */
.auth-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  position: sticky;
  top: 0;
  background: var(--gradient-hero);
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.back-btn {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--neutral-100);
  font-size: 20px;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s;
  z-index: 1;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.main-content {
  /* padding-top: 0; 移除顶部padding，因为已有header */
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* 搜索样式 */
.search-section {
  margin: 20px 0;
}

.search-container {
  display: flex;
  background: var(--gradient-card);
  border-radius: 24px;
  padding: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.search-dropdown {
  background: var(--primary-gold-alpha);
  border: none;
  color: var(--primary-gold);
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  outline: none;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--neutral-100);
  padding: 8px 16px;
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: var(--neutral-400);
}

.search-btn {
  background: var(--gradient-primary);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-gold);
}

/* 通知横幅 */
.notification-bar {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 12px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-warning {
  color: var(--accent-orange);
}

.text-small {
  font-size: 12px;
  color: var(--neutral-400);
}

/* 预售卡片 */
.presale-section {
  margin: 24px 0;
  padding-bottom: 65px;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 暂无数据状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: var(--text-tertiary);
  text-align: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-state p {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

/* 删除旧的presale-card样式，已在上方重新定义 */

/* 根据图片样式重新设计的资产卡片 */
.presale-card .asset-content {
  padding: 0;
}

.asset-layout {
  display: flex;
  padding: 16px;
  gap: 16px;
  align-items: flex-start;
}

/* 左侧封面区域 */
.asset-cover-section {
  width: 150px;
  flex-shrink: 0;
}

.asset-cover-container {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(212, 165, 116, 0.1);
}

.asset-cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.asset-cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 11px;
  text-align: center;
  padding: 8px;
}

.asset-cover-placeholder i {
  font-size: 20px;
  margin-bottom: 6px;
  opacity: 0.6;
}

.asset-cover-placeholder span {
  line-height: 1.2;
  word-break: break-all;
}

/* 右侧信息区域 */
.asset-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 150px;
}

.asset-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

/* 关键字标签 */
.keyword-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.keyword-tag {
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.15), rgba(212, 165, 116, 0.1));
  color: var(--primary-color);
  border: 1px solid rgba(212, 165, 116, 0.3);
  border-radius: 6px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 500;
  transition: all 0.2s;
}

/* 限量信息 */
.quantity-display {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 8px;
  font-size: 13px;
  color: var(--text-secondary);
}

.quantity-display i {
  color: var(--primary-color);
  font-size: 11px;
}

/* 价格显示 */
.price-display {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

/* 开售时间 */
.sale-time-display {
  font-size: 12px;
  color: var(--text-tertiary);
  margin-bottom: 12px;
}

/* 预约按钮 */
.action-section {
  margin-top: auto;
}

.reserve-button {
  width: 100%;
  background: var(--gradient-primary);
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.reserve-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
}

.reserve-button.reserved {
  background: linear-gradient(135deg, var(--success-color), #7a8c4a);
}

.reserve-button.reserved:hover {
  box-shadow: 0 4px 12px rgba(144, 169, 85, 0.4);
}

.reserve-button i {
  font-size: 11px;
}

/* 优化后的卡片整体样式 */
.presale-card {
  background: var(--gradient-card);
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.4s ease;
  backdrop-filter: blur(20px);
  cursor: pointer;
}

.presale-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  border-color: var(--primary-color);
}

.presale-card:hover .asset-cover-img {
  transform: scale(1.05);
}



.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  text-decoration: none;
}

.btn-secondary {
  background: rgba(40, 40, 40, 0.7);
  color: var(--neutral-200);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(60, 60, 60, 0.7);
  border-color: var(--primary-gold);
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 0;
  z-index: 50;
}

.nav-items {
  display: flex;
  justify-content: space-around;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--neutral-400);
  transition: color 0.3s;
  padding: 8px 4px;
}

.nav-item.active {
  color: var(--primary-gold);
}

.nav-item:hover {
  color: var(--primary-gold-light);
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-label {
  font-size: 12px;
}

.main-content {
  /* padding: 20px 0 80px 0; */
}

/* 响应式设计 - 移动端优化 */
@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .presale-card {
    border-radius: 12px;
    margin-bottom: 12px;
  }
  
  .asset-layout {
    padding: 12px;
    gap: 12px;
  }
  
  .asset-cover-section {
    width: 120px;
  }
  
  .asset-cover-container {
    width: 120px;
    height: 160px;
    border-radius: 10px;
  }
  
  .asset-info-section {
    min-height: 120px;
  }
  
  .asset-title {
    font-size: 15px;
    margin-bottom: 6px;
  }
  
  .keyword-tag {
    padding: 1px 4px;
    font-size: 10px;
    border-radius: 4px;
  }
  
  .quantity-display {
    font-size: 12px;
    margin-bottom: 6px;
  }
  
  .quantity-display i {
    font-size: 10px;
  }
  
  .price-display {
    font-size: 16px;
    margin-bottom: 6px;
  }
  
  .sale-time-display {
    font-size: 11px;
    margin-bottom: 10px;
  }
  
  .reserve-button {
    padding: 6px 10px;
    font-size: 11px;
  }
  
  .reserve-button i {
    font-size: 10px;
  }
}

/* 平板端优化 */
@media (min-width: 481px) and (max-width: 768px) {
  .asset-layout {
    gap: 14px;
  }
  
  .asset-cover-section {
    width: 135px;
  }
  
  .asset-cover-container {
    width: 135px;
    height: 135px;
  }
  
  .asset-info-section {
    min-height: 135px;
  }
  
  .asset-title {
    font-size: 15px;
  }
  
  .keyword-tag {
    font-size: 10px;
  }
}
</style> 