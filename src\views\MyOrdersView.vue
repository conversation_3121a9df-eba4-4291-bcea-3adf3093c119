<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import { PayAPI } from '@/api/pay'
import { DictionaryAPI } from '@/api/dictionary'
import type { OrderListItem, OrderListQueryParams } from '@/api/pay'
import type { DictionaryItem } from '@/api/dictionary'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const route = useRoute()
const router = useRouter()
const notification = useNotification()

// 数据状态
const loading = ref(false)
const loadingMore = ref(false)
const orders = ref<OrderListItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(5)

// 筛选条件
const searchForm = ref({
  assetName: '', // 保留用于未来可能的搜索功能扩展
  statusCd: ''   // 用于Tab状态筛选
})

// Tab状态筛选
const activeTab = ref('all')

// 订单状态Tab配置
const statusTabs = ref([
  { key: 'all', label: '全部', statusCd: '' },
  { key: 'pending', label: '待支付', statusCd: '1' },
  { key: 'completed', label: '已完成', statusCd: '2' },
  { key: 'cancelled', label: '已取消', statusCd: '3' }
])

// 字典数据
const orderStatusOptions = ref<DictionaryItem[]>([])

// 格式化价格
const formatPrice = (price: number): string => {
  return price.toFixed(2)
}

// 格式化时间
const formatDate = (dateStr: string): string => {
  const date = new Date(dateStr)
  return date.toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取订单状态显示文本
const getOrderStatusText = (statusCd: string): string => {
  const statusItem = orderStatusOptions.value.find(item => item.value === statusCd)
  return statusItem ? statusItem.label : statusCd
}

// 检查是否为待支付状态
const isPendingPayment = (statusCd: string): boolean => {
  // 支持多种待支付状态代码
  const pendingStatuses = [
    '1', 'pending', 'wait_pay', 'waitPay', 'unpaid', 
    '0', 'created', 'init', 'waiting', 'toBePaid',
    'WAIT_PAY', 'PENDING', 'UNPAID', 'CREATED'
  ]
  
  // 基于字典数据智能判断
  const statusItem = orderStatusOptions.value.find(item => item.value === statusCd)
  const statusLabel = statusItem ? statusItem.label.toLowerCase() : ''
  
  // 如果状态标签包含"待支付"、"未支付"等关键词，也认为是待支付状态
  const isPendingByLabel = statusLabel.includes('待支付') || 
                          statusLabel.includes('未支付') || 
                          statusLabel.includes('等待支付') ||
                          statusLabel.includes('wait') ||
                          statusLabel.includes('pending')
  
  return pendingStatuses.includes(statusCd) || isPendingByLabel
}

// 获取订单状态样式类
const getOrderStatusClass = (statusCd: string): string => {
  // 根据状态返回不同的样式类
  if (isPendingPayment(statusCd)) {
    return 'status-pending'
  }
  if (statusCd === '2' || statusCd === 'paid' || statusCd === 'success') {
    return 'status-success'
  }
  if (statusCd === '3' || statusCd === 'cancelled' || statusCd === 'cancel') {
    return 'status-cancelled'
  }
  return 'status-default'
}

// 加载订单状态字典（使用dig_order_status字典类型）
const loadOrderStatusDictionary = async () => {
  try {
    // 调用字典API获取订单状态数据，参数为 'dig_order_status'
    const response = await DictionaryAPI.getOrderStatusTypes()
    if (response.code === 200 && response.data) {
      orderStatusOptions.value = response.data
      
      // 根据字典数据动态调整Tab配置
      updateStatusTabs(response.data)
    }
  } catch (error) {
    console.error('❌ 加载订单状态字典失败:', error)
  }
}

// 根据字典数据更新状态Tab配置
const updateStatusTabs = (statusOptions: DictionaryItem[]) => {
  const tabs = [{ key: 'all', label: '全部', statusCd: '' }]
  
  // 查找待支付状态
  const pendingStatus = statusOptions.find(item => 
    isPendingPayment(item.value) || 
    item.label.includes('待支付') || 
    item.label.includes('未支付')
  )
  if (pendingStatus) {
    tabs.push({ key: 'pending', label: '待支付', statusCd: pendingStatus.value })
  }
  
  // 查找已完成状态
  const completedStatus = statusOptions.find(item => 
    item.value === '2' || 
    item.value === 'paid' || 
    item.value === 'success' ||
    item.label.includes('已完成') ||
    item.label.includes('已支付') ||
    item.label.includes('成功')
  )
  if (completedStatus) {
    tabs.push({ key: 'completed', label: '已完成', statusCd: completedStatus.value })
  }
  
  // 查找已取消状态
  const cancelledStatus = statusOptions.find(item => 
    item.value === '3' || 
    item.value === 'cancelled' || 
    item.value === 'cancel' ||
    item.label.includes('已取消') ||
    item.label.includes('取消')
  )
  if (cancelledStatus) {
    tabs.push({ key: 'cancelled', label: '已取消', statusCd: cancelledStatus.value })
  }
  
  statusTabs.value = tabs
}

// 加载订单列表
const loadOrderList = async (reset: boolean = false) => {
  try {
    if (reset) {
      loading.value = true
      currentPage.value = 1
    } else {
      loadingMore.value = true
    }
    
    const params: OrderListQueryParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
    
    // 添加筛选条件
    // 资产名称搜索（当前已注释搜索框，但保留逻辑）
    if (searchForm.value.assetName?.trim()) {
      params.assetName = searchForm.value.assetName.trim()
    }
    // 状态筛选（通过Tab切换）
    if (searchForm.value.statusCd) {
      params.statusCd = searchForm.value.statusCd
    }
    
    const response = await PayAPI.getOrderList(params)
    
    if (response.code === 200) {
      if (reset) {
        orders.value = response.rows || []
      } else {
        orders.value = [...orders.value, ...(response.rows || [])]
      }
      total.value = response.total || 0
    } else {
      throw new Error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('❌ 加载订单列表失败:', error)
    const errorMessage = error instanceof Error ? error.message : '获取订单列表失败'
    notification.error(errorMessage)
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

// 切换Tab
const switchTab = (tabKey: string) => {
  activeTab.value = tabKey
  const selectedTab = statusTabs.value.find(tab => tab.key === tabKey)
  if (selectedTab) {
    searchForm.value.statusCd = selectedTab.statusCd
    loadOrderList(true)
  }
}

// 搜索订单（保留函数供其他地方调用）
const handleSearch = () => {
  loadOrderList(true)
}

// 重置搜索条件（保留函数供其他地方调用）
const handleReset = () => {
  searchForm.value.assetName = ''
  activeTab.value = 'all' // 重置到全部状态
  searchForm.value.statusCd = ''
  handleSearch()
}

// 加载更多
const loadMore = () => {
  if (loadingMore.value || orders.value.length >= total.value) return
  currentPage.value += 1
  loadOrderList(false)
}

// 继续支付
const continuePayment = (order: OrderListItem) => {
  // 跳转到支付页面，传递订单信息
  router.push({
    name: 'payment',
    params: {
      orderNo: order.orderNo
    },
    query: {
      assetName: order.assetName,
      assetPrice: order.orderAmount.toString(),
      assetCover: order.assetCover || ''
    }
  })
}

// 查看订单详情
const viewOrderDetail = (order: OrderListItem) => {
  // TODO: 实现订单详情页面
  notification.info('订单详情页面待开发')
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 计算属性
const hasMore = computed(() => orders.value.length < total.value)

// ========== 新增：滚动监听相关 ==========
// 用于保存滚动监听器的引用
let scrollListener: (() => void) | null = null

// 判断是否滚动到底部
const handleScroll = () => {
  // 获取页面滚动高度、可视区域高度、总高度
  const scrollTop = window.scrollY || document.documentElement.scrollTop
  const clientHeight = window.innerHeight || document.documentElement.clientHeight
  const scrollHeight = document.documentElement.scrollHeight
  // 距离底部小于100px时自动加载更多
  if (scrollTop + clientHeight >= scrollHeight - 100) {
    loadMore()
  }
}

// 绑定和解绑滚动事件
const bindScrollListener = () => {
  if (!scrollListener) {
    scrollListener = handleScroll
    window.addEventListener('scroll', scrollListener)
  }
}
const unbindScrollListener = () => {
  if (scrollListener) {
    window.removeEventListener('scroll', scrollListener)
    scrollListener = null
  }
}

// 生命周期
onMounted(() => {
  loadOrderStatusDictionary()
  loadOrderList(true)
  // 绑定滚动监听
  bindScrollListener()
})
onUnmounted(() => {
  // 解绑滚动监听，防止内存泄漏
  unbindScrollListener()
})
</script>

<template>
  <AppPageHeader :title="'我的订单'" @back="$router.back()" />
  <div class="my-orders-container" :style="{ paddingTop: '40px' }">


    <!-- Tab筛选条件 -->
    <section class="filter-section">
      <!-- 状态Tab -->
      <div class="status-tabs">
        <button
          v-for="tab in statusTabs"
          :key="tab.key"
          :class="['tab-btn', { active: activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </button>
      </div>
      
      <!-- 搜索框 -->
      <!-- <div class="search-container">
        <div class="search-input-wrapper">
          <input 
            v-model="searchForm.assetName" 
            type="text" 
            class="search-input"
            placeholder="搜索资产名称"
            @keyup.enter="handleSearch"
          />
          <button class="search-btn" @click="handleSearch">
            <i class="fas fa-search"></i>
          </button>
          <button 
            v-if="searchForm.assetName"
            class="clear-btn" 
            @click="handleReset"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div> -->
    </section>

    <!-- 订单列表 -->
    <section class="orders-section">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <i class="fas fa-spinner fa-spin"></i>
        <p>正在加载订单...</p>
      </div>

      <!-- 订单列表 -->
      <div v-else-if="orders.length > 0" class="orders-list">
        <div 
          v-for="order in orders" 
          :key="order.orderId"
          class="order-item"
        >
          <!-- 订单头部 -->
          <div class="order-header">
            <div class="order-info">
              <span class="order-no">订单号: {{ order.orderNo }}</span>
              <span class="order-date">{{ formatDate(order.createDate) }}</span>
            </div>
            <div class="order-status" :class="getOrderStatusClass(order.statusCd)">
              {{ getOrderStatusText(order.statusCd) }}
            </div>
          </div>

          <!-- 订单内容 -->
          <div class="order-content">
            <div class="asset-info">
              <div class="asset-cover">
                <img 
                  v-if="order.assetCoverThumbnail" 
                  :src="order.assetCoverThumbnail" 
                  :alt="order.assetName"
                  class="cover-image"
                />
                <div v-else class="cover-placeholder">
                  <i class="fas fa-image"></i>
                </div>
              </div>
              <div class="asset-details">
                <h3 class="asset-name">{{ order.assetName }}</h3>
                <div class="asset-price">
                  <span class="price-label">订单金额</span>
                  <span class="price-value">¥{{ formatPrice(order.orderAmount) }}</span>
                </div>
              </div>
            </div>

            <!-- 订单操作 -->
            <div class="order-actions">
              <!-- <button 
                class="action-btn detail-btn"
                @click="viewOrderDetail(order)"
              >
                <i class="fas fa-eye"></i>
                查看详情
              </button> -->
              <button 
                v-if="isPendingPayment(order.statusCd)"
                class="action-btn payment-btn"
                @click="continuePayment(order)"
              >
                <i class="fas fa-credit-card"></i>
                继续支付
              </button>
            </div>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more">
          <button 
            class="load-more-btn"
            :disabled="loadingMore"
            @click="loadMore"
          >
            <i v-if="loadingMore" class="fas fa-spinner fa-spin"></i>
            <i v-else class="fas fa-chevron-down"></i>
            {{ loadingMore ? '加载中...' : '加载更多' }}
          </button>
        </div>
        <!-- 新增：没有更多订单了 -->
        <div v-else class="no-more-orders" style="text-align:center;color:var(--text-muted);margin:16px 0;">
          <i class="fas fa-box-open" style="font-size:20px;margin-right:6px;"></i>没有更多订单了
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <i class="fas fa-receipt"></i>
        <h3>暂无订单</h3>
        <p>您还没有任何订单，快去选购数字资产吧！</p>
        <button class="go-shopping-btn" @click="router.push('/exhibition')">
          <i class="fas fa-shopping-cart"></i>
          去购物
        </button>
      </div>
    </section>
  </div>
</template>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
}

.my-orders-container {
  background: var(--gradient-bg);
  color: var(--text-primary);
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 头部导航 */
.orders-header {
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  padding: 16px 20px;
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 100;
  width: 100%;
  box-sizing: border-box;
}

.back-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  grid-column: 1;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  text-align: center;
  grid-column: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-placeholder {
  width: 40px;
  grid-column: 3;
}

/* Tab筛选条件 */
.filter-section {
  background: var(--gradient-card);
  margin: 12px;
  border-radius: 12px;
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* 状态Tab */
.status-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--border-light);
}

.tab-btn {
  flex: 1;
  padding: 16px 8px;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-btn:hover {
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.05);
}

.tab-btn.active {
  color: var(--primary-color);
  background: rgba(212, 165, 116, 0.1);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
}

/* 搜索相关样式已注释 - 如需恢复搜索功能可取消注释 */
/*
.search-container {
  padding: 16px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
}

.search-input {
  flex: 1;
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-btn {
  padding: 12px 16px;
  background: transparent;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid var(--border-light);
}

.search-btn:hover {
  background: rgba(212, 165, 116, 0.1);
  color: var(--primary-light);
}

.clear-btn {
  padding: 12px;
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.clear-btn:hover {
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.05);
}
*/

/* 订单列表 */
.orders-section {
  padding: 0 16px 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  background: var(--gradient-card);
  border-radius: 16px;
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: all 0.3s ease;
}

.order-item:hover {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-md);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid var(--border-light);
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-no {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.order-date {
  font-size: 12px;
  color: var(--text-muted);
}

.order-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.status-pending {
  background: rgba(249, 132, 74, 0.2);
  color: var(--warning-color);
}

.status-success {
  background: rgba(144, 169, 85, 0.2);
  color: var(--success-color);
}

.status-cancelled {
  background: rgba(230, 57, 70, 0.2);
  color: var(--error-color);
}

.status-default {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-tertiary);
}

.order-content {
  padding: 16px;
}

.asset-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.asset-cover {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--bg-secondary);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  font-size: 20px;
}

.asset-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.asset-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-label {
  font-size: 12px;
  color: var(--text-muted);
}

.price-value {
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-color);
}

.order-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.detail-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

.detail-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.payment-btn {
  background: var(--gradient-primary);
  color: white;
}

.payment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.load-more-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.load-more-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-state i {
  font-size: 48px;
  color: var(--text-muted);
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 18px;
  color: var(--text-secondary);
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  color: var(--text-muted);
  margin: 0 0 24px 0;
}

.go-shopping-btn {
  padding: 12px 24px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.go-shopping-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    margin: 8px;
  }
  
  .status-tabs {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .tab-btn {
    min-width: 70px;
    padding: 14px 12px;
    font-size: 13px;
  }
  
  /* 搜索相关响应式样式已注释 - 如需恢复搜索功能可取消注释 */
  /*
  .search-container {
    padding: 12px;
  }
  
  .search-input {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .search-btn {
    padding: 10px 12px;
  }
  
  .clear-btn {
    padding: 10px;
  }
  */
}

@media (max-width: 480px) {
  .order-actions {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style> 