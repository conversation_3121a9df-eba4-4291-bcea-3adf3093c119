# 三套环境配置说明

## 环境概述

项目现在支持三套完整的环境配置：

1. **开发环境** - 本地开发调试
2. **测试环境** - 测试服务器环境  
3. **生产环境** - 正式线上环境

## 环境配置文件

### 1. `.env` (默认配置)
```
# 默认开发环境配置
VITE_API_BASE_URL=https://ts.sccdex.com/api
```

### 2. `.env.local` (本地开发环境)
```
# 本地开发环境配置
VITE_API_BASE_URL=http://127.0.0.1:9020
VITE_ENV=local
```

### 3. `.env.ts` (测试环境)
```
# 测试环境配置
VITE_API_BASE_URL=https://ts.sccdex.com/api
VITE_ENV=ts
```

### 4. `.env.prod` (生产环境)
```
# 生产环境配置
VITE_API_BASE_URL=https://www.sccdex.com/api
VITE_ENV=prod
```

## 开发服务器命令

### 本地开发环境
```bash
npm run dev
```
- **接口地址**: `http://127.0.0.1:9020`
- **用途**: 本地后端开发调试

### 测试环境
```bash
npm run dev:ts
```
- **接口地址**: `https://ts.sccdex.com/api`
- **用途**: 连接测试服务器进行开发

### 生产环境
```bash
npm run dev:prod
```
- **接口地址**: `https://www.sccdex.com/api`
- **用途**: 连接生产服务器进行开发（谨慎使用）

## 构建命令

### 开发环境构建
```bash
npm run build:dev
```
- **接口地址**: `http://127.0.0.1:9020`

### 测试环境构建
```bash
npm run build:ts
```
- **接口地址**: `https://ts.sccdex.com/api`

### 生产环境构建
```bash
npm run build:prod
```
- **接口地址**: `https://www.sccdex.com/api`

### 默认构建
```bash
npm run build
```
- **接口地址**: `https://www.sccdex.com/api`

## 使用建议

1. **日常开发**: 使用 `npm run dev` 或 `npm run dev:ts`
2. **功能测试**: 使用 `npm run dev:ts` 连接测试环境
3. **生产验证**: 谨慎使用 `npm run dev:prod`
4. **部署构建**: 
   - 测试环境部署: `npm run build:ts`
   - 生产环境部署: `npm run build:prod` 或 `npm run build`

## 注意事项

1. 环境变量必须以 `VITE_` 前缀开头才能在客户端代码中访问
2. 生产环境命令请谨慎使用，避免对线上数据造成影响
3. 如需添加新环境，创建对应的 `.env.{mode}` 文件即可
