import request from './request'
import type { ApiResponse } from './types'

/**
 * 用户统计数据
 */
export interface UserStats {
  collections: number      // 我的藏品数量
  transactions: number     // 交易记录数量
  totalValue: string       // 总价值
}

/**
 * 用户积分信息
 */
export interface UserPoints {
  points: number           // 用户积分
}

/**
 * 用户资产数量信息
 */
export interface UserAssetNum {
  userAddress: string      // 用户链上地址
  assetQuantity: number    // 资产数量
}

/**
 * 资产数据信息
 */
export interface AssetData {
  dataAssetId: number      // 数据资产ID
  assetId: number          // 资产ID
  assetName: string        // 资产名称
  assetCover: string       // 资产封面
  assetCoverThumbnail: string       // 资产封面
  assetCode: string        // 资产唯一编号
  chainHash: string        // 链上HASH
  chainTime: string        // 上链时间
  isRedeemed: number       // 是否已兑换权益(0未兑换 1已兑换)
  statusCd: string         // 状态
  createDate: string       // 创建时间
}

/**
 * 用户钱包信息
 */
export interface UserWallet {
  address: string          // 钱包地址
  balance: string          // 余额
  isVerified: boolean      // 是否已验证
}

/**
 * 收藏品信息
 */
export interface Collection {
  id: number
  name: string
  image: string
  price: string
  createTime: string
  status: 'owned' | 'transferred' | 'sold'
}

/**
 * 订单信息
 */
export interface Order {
  id: number
  orderNo: string
  collectionName: string
  price: string
  status: 'pending' | 'completed' | 'cancelled'
  createTime: string
}

/**
 * 交易记录
 */
export interface Transaction {
  id: number
  type: 'buy' | 'sell' | 'transfer'
  collectionName: string
  amount: string
  fromAddress?: string
  toAddress?: string
  createTime: string
  txHash: string
}

/**
 * 用户API服务
 */
export class UserAPI {
  /**
   * 获取用户统计数据
   */
  static async getUserStats(): Promise<ApiResponse<UserStats>> {
    return request.get<ApiResponse<UserStats>>('/user/stats')
  }

  /**
   * 获取用户资产数量信息
   */
  static async getUserAssetNum(): Promise<ApiResponse<UserAssetNum>> {
    return request.get<ApiResponse<UserAssetNum>>('/asset/asset/client/my/num')
  }

  /**
   * 获取用户资产列表
   */
  static async getUserAssetList(params: {
    pageNum: number
    pageSize: number
    assetName?: string
    [key: string]: any  // 允许其他可能的查询参数
  }): Promise<{
    code: number
    rows: AssetData[]
    total: number
    msg?: string
  }> {
    return request.get<{
      code: number
      rows: AssetData[]
      total: number
      msg?: string
    }>('/assetData/assetData/client/list', params)
  }

  /**
   * 获取资产详情
   */
  static async getAssetDetail(dataAssetId: number): Promise<ApiResponse<AssetData>> {
    return request.get(`/assetData/assetData/client/getInfo/${dataAssetId}`)
  }

  /**
   * 获取资产模型文件地址
   */
  static async getAssetModel(assetId: number): Promise<{
    code: number
    msg: string        // 模型文件URL
  }> {
    return request.get<{
      code: number
      msg: string
    }>(`/asset/asset/client/my/assetsFile/${assetId}`, undefined, {
      timeout: 60000  // 设置60秒超时，因为模型文件可能较大
    })
  }

  /**
   * 获取用户钱包信息
   */
  static async getUserWallet(): Promise<ApiResponse<UserWallet>> {
    return request.get<ApiResponse<UserWallet>>('/user/wallet')
  }

  /**
   * 更新用户钱包地址
   */
  static async updateWalletAddress(address: string): Promise<ApiResponse> {
    return request.post('/user/wallet/address', { address })
  }

  /**
   * 获取用户收藏品列表
   */
  static async getUserCollections(page: number = 1, pageSize: number = 10): Promise<ApiResponse<{
    list: Collection[]
    total: number
    page: number
    pageSize: number
  }>> {
    return request.get('/user/collections', { page, pageSize })
  }

  /**
   * 获取用户订单列表
   */
  static async getUserOrders(page: number = 1, pageSize: number = 10): Promise<ApiResponse<{
    list: Order[]
    total: number
    page: number
    pageSize: number
  }>> {
    return request.get('/user/orders', { page, pageSize })
  }

  /**
   * 获取用户交易记录
   */
  static async getUserTransactions(page: number = 1, pageSize: number = 10): Promise<ApiResponse<{
    list: Transaction[]
    total: number
    page: number
    pageSize: number
  }>> {
    return request.get('/user/transactions', { page, pageSize })
  }

  /**
   * 转赠藏品
   */
  static async transferCollection(collectionId: number, toAddress: string): Promise<ApiResponse> {
    return request.post('/user/transfer', {
      collectionId,
      toAddress
    })
  }

  /**
   * 获取实名认证状态和信息
   */
  static async getVerificationStatus(): Promise<ApiResponse<{
    identifyId?: number
    userId?: number
    identifyType?: 'person' | 'enterprise'
    enterpriseType?: string
    idType?: string
    idName?: string
    idNo?: string
    phone?: string
    idCard?: string
    legalName?: string
    address?: string
    registrationPlace?: string
    registrationArea?: string
    businessScope?: string
    enterprisePhone?: string
    enterpriseEmail?: string
    statusCd?: string
    statusDate?: string
    province?: string
    city?: string
    businessTerm?: string
    isVerified?: boolean
  }>> {
    return request.get('/identify')
  }

  /**
   * 提交实名认证（个人或企业）
   */
  static async submitVerification(data: {
    identifyType: 'person' | 'enterprise'
    idType: string
    idName: string
    idNo: string
    idCard: string
    phone: string
    address: string
    smsCode: string
    legalName?: string
    registrationPlace?: string
    registrationArea?: string
    businessScope?: string
    enterprisePhone?: string
    enterpriseEmail?: string
    businessTerm?: string
    province?: string
    city?: string
  }): Promise<ApiResponse> {
    return request.post('/issue/identify', data)
  }

  /**
   * 获取安全设置
   */
  static async getSecuritySettings(): Promise<ApiResponse<{
    hasPassword: boolean
    hasPhone: boolean
    hasEmail: boolean
    twoFactorEnabled: boolean
  }>> {
    return request.get('/user/security/settings')
  }

  /**
   * 修改密码
   */
  static async changePassword(data: {
    oldPassword: string
    newPassword: string
  }): Promise<ApiResponse> {
    return request.post('/user/security/password', data)
  }

  /**
   * 绑定手机号
   */
  static async bindPhone(data: {
    phone: string
    smsCode: string
  }): Promise<ApiResponse> {
    return request.post('/user/security/bind-phone', data)
  }

  /**
   * 获取消息通知设置
   */
  static async getNotificationSettings(): Promise<ApiResponse<{
    pushEnabled: boolean
    emailEnabled: boolean
    smsEnabled: boolean
    tradeNotify: boolean
    systemNotify: boolean
  }>> {
    return request.get('/user/notification/settings')
  }

  /**
   * 更新消息通知设置
   */
  static async updateNotificationSettings(settings: {
    pushEnabled?: boolean
    emailEnabled?: boolean
    smsEnabled?: boolean
    tradeNotify?: boolean
    systemNotify?: boolean
  }): Promise<ApiResponse> {
    return request.post('/user/notification/settings', settings)
  }

  /**
   * 获取用户积分
   */
  static async getUserPoints(): Promise<ApiResponse<UserPoints>> {
    return request.get<ApiResponse<UserPoints>>('/member/userPonints/getUserPoints')
  }
}

export default UserAPI 