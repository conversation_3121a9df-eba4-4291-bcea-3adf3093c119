<template>
  <header class="app-page-header">
    <button class="back-btn" @click="$emit('back')">
      <i class="fas fa-arrow-left"></i>
    </button>
    <div class="header-title">
      <slot>{{ title }}</slot>
    </div>
    <div class="header-placeholder"></div>
  </header>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
const props = defineProps<{ title?: string }>()
defineEmits(['back'])
</script>

<style scoped>
:root {
  --app-header-height: 56px;
}

.app-page-header {
  width: 100%;
  height: var(--app-header-height);
  display: grid;
  grid-template-columns: 40px 1fr 40px;
  align-items: center;
  background: linear-gradient(180deg, #232323 0%, #181818 100%);
  box-sizing: border-box;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

:global(body),
:global(#app),
:global(.app-main),
:global(.main-content),
:global(.page-content) {
  padding-top: var(--app-header-height);
  box-sizing: border-box;
}

.back-btn {
  width: 40px;
  height: 40px;
  border-radius: 0;
  background: transparent;
  border: none;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: background 0.2s;
  grid-column: 1;
}
.back-btn:hover {
  background: rgba(255,255,255,0.08);
}
.header-title {
  grid-column: 2;
  text-align: center;
  font-size: 20px;
  font-weight: 700;
  color: #fff;
  letter-spacing: 1px;
  user-select: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.header-placeholder {
  width: 40px;
  height: 40px;
  grid-column: 3;
}
</style> 