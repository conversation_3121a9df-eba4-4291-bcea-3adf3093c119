import { ref } from 'vue'

export interface NotificationOptions {
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

export interface Notification {
  id: number
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration: number
}

// 全局通知状态
export const notifications = ref<Notification[]>([])
let notificationId = 0

/**
 * 通知组合式函数
 */
export function useNotification() {
  /**
   * 显示通知
   */
  const showNotification = (
    message: string, 
    options: NotificationOptions = {}
  ) => {
    const { type = 'info', duration = 3000 } = options
    
    const notification: Notification = {
      id: ++notificationId,
      message,
      type,
      duration
    }
    
    notifications.value.push(notification)
    
    // 控制台输出用于调试
    console.log(`[${type.toUpperCase()}] ${message}`)
    
    // 自动移除通知
    if (duration > 0) {
      setTimeout(() => {
        removeNotification(notification.id)
      }, duration)
    }
    
    return notification.id
  }

  /**
   * 移除通知
   */
  const removeNotification = (id: number) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  /**
   * 清除所有通知
   */
  const clearNotifications = () => {
    notifications.value = []
  }

  /**
   * 快捷方法
   */
  const success = (message: string, duration?: number) => {
    return showNotification(message, { type: 'success', duration })
  }

  const error = (message: string, duration?: number) => {
    return showNotification(message, { type: 'error', duration })
  }

  const warning = (message: string, duration?: number) => {
    return showNotification(message, { type: 'warning', duration })
  }

  const info = (message: string, duration?: number) => {
    return showNotification(message, { type: 'info', duration })
  }

  return {
    notifications,
    showNotification,
    removeNotification,
    clearNotifications,
    success,
    error,
    warning,
    info
  }
} 