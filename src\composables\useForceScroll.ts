import { onMounted, onUnmounted } from 'vue'

export function useForceScroll() {
  let touchStartY = 0
  let touchCurrentY = 0
  let scrollTop = 0

  // 强制启用滚动的函数
  const enableScrolling = () => {
    // 移除所有可能阻止滚动的样式
    const elements = [document.documentElement, document.body]
    
    elements.forEach(el => {
      if (el) {
        (el as HTMLElement).style.overflow = 'auto'
        ;(el as HTMLElement).style.overflowY = 'auto'
        ;(el as HTMLElement).style.height = 'auto'
        ;(el as HTMLElement).style.minHeight = '100vh'
        ;(el as any).style.webkitOverflowScrolling = 'touch'
        // 不对根元素设置transform，避免影响fixed定位
        ;(el as HTMLElement).style.transform = 'none'
      }
    })

    // 单独处理app容器，启用硬件加速但不影响fixed元素
    const appElement = document.querySelector('#app') as HTMLElement
    if (appElement) {
      appElement.style.overflow = 'auto'
      appElement.style.overflowY = 'auto'
      appElement.style.height = 'auto'
      appElement.style.minHeight = '100vh'
      ;(appElement as any).style.webkitOverflowScrolling = 'touch'
      // 为app容器设置独立的层叠上下文，但不影响fixed定位
      appElement.style.position = 'relative'
      appElement.style.zIndex = '1'
      // 不设置transform，避免影响底部导航
    }

    // 强制重新计算布局
    document.body.offsetHeight
    
    // 确保底部导航始终在最顶层
    setTimeout(() => {
      const bottomNav = document.querySelector('.bottom-nav-fixed') as HTMLElement
      if (bottomNav) {
        bottomNav.style.zIndex = '99999'
        bottomNav.style.position = 'fixed'
        bottomNav.style.bottom = '0'
        bottomNav.style.left = '0'
        bottomNav.style.right = '0'
      }
    }, 100)
  }

  // 手动实现触摸滚动
  const handleTouchStart = (e: TouchEvent) => {
    touchStartY = e.touches[0].clientY
    scrollTop = window.pageYOffset || document.documentElement.scrollTop
  }

  const handleTouchMove = (e: TouchEvent) => {
    touchCurrentY = e.touches[0].clientY
    const deltaY = touchStartY - touchCurrentY
    
    // 手动滚动
    const newScrollTop = scrollTop + deltaY
    window.scrollTo(0, Math.max(0, newScrollTop))
    
    // 阻止默认的橡皮筋效果
    if (newScrollTop >= 0 && newScrollTop <= document.body.scrollHeight - window.innerHeight) {
      e.preventDefault()
    }
  }

  // 鼠标滚轮事件
  const handleWheel = (e: WheelEvent) => {
    const delta = e.deltaY
    const currentScroll = window.pageYOffset || document.documentElement.scrollTop
    const newScroll = currentScroll + delta
    
    window.scrollTo({
      top: newScroll,
      behavior: 'auto'
    })
  }

  // 键盘滚动支持
  const handleKeyDown = (e: KeyboardEvent) => {
    const scrollAmount = 50
    const currentScroll = window.pageYOffset || document.documentElement.scrollTop
    
    switch (e.key) {
      case 'ArrowUp':
        window.scrollTo({ top: currentScroll - scrollAmount, behavior: 'smooth' })
        e.preventDefault()
        break
      case 'ArrowDown':
        window.scrollTo({ top: currentScroll + scrollAmount, behavior: 'smooth' })
        e.preventDefault()
        break
      case 'PageUp':
        window.scrollTo({ top: currentScroll - window.innerHeight, behavior: 'smooth' })
        e.preventDefault()
        break
      case 'PageDown':
        window.scrollTo({ top: currentScroll + window.innerHeight, behavior: 'smooth' })
        e.preventDefault()
        break
    }
  }

  onMounted(() => {
    // 延迟执行，确保DOM完全加载
    setTimeout(() => {
      enableScrolling()
      
      // 添加事件监听器
      document.addEventListener('touchstart', handleTouchStart, { passive: false })
      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('wheel', handleWheel, { passive: false })
      document.addEventListener('keydown', handleKeyDown)
      
      // 强制触发一次滚动测试
      setTimeout(() => {
        window.scrollTo({ top: 10, behavior: 'auto' })
        setTimeout(() => {
          window.scrollTo({ top: 0, behavior: 'auto' })
        }, 100)
      }, 500)
      
      console.log('🔧 强制滚动模式已启用')
    }, 100)
  })

  onUnmounted(() => {
    document.removeEventListener('touchstart', handleTouchStart)
    document.removeEventListener('touchmove', handleTouchMove)
    document.removeEventListener('wheel', handleWheel)
    document.removeEventListener('keydown', handleKeyDown)
  })

  return {
    enableScrolling
  }
} 