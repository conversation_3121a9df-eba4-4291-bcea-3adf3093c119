{"openapi": "3.0.1", "info": {"title": "川文数服", "description": "", "version": "1.0.0"}, "tags": [{"name": "实名认证"}], "paths": {"/identify": {"post": {"summary": "新增实名认证", "x-apifox-folder": "实名认证", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["实名认证"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"identifyType": {"type": "string", "description": "认证类型"}, "idType": {"type": "string", "description": "证件类型"}, "idName": {"type": "string", "description": "认证名称"}, "idNo": {"type": "string", "description": "认证证件号"}, "idCard": {"type": "string", "description": "证件照片"}, "phone": {"type": "string", "description": "手机号"}, "address": {"type": "string", "description": "地址"}, "smsCode": {"type": "string", "description": "手机号验证码"}, "legalName": {"type": "string", "description": "法人姓名"}, "registrationPlace": {"type": "string", "description": "注册地"}, "registrationArea": {"type": "string", "description": "注册区域"}, "businessScope": {"type": "string", "description": "主营业务/业务"}, "enterprisePhone": {"type": "string", "description": "企业联系电话"}, "enterpriseEmail": {"type": "string", "description": "企业邮箱"}, "businessTerm": {"type": "string", "description": "营业期限"}}, "x-apifox-orders": ["identifyType", "idType", "idName", "idNo", "idCard", "phone", "address", "smsCode", "legalName", "registrationPlace", "registrationArea", "businessScope", "enterprisePhone", "enterpriseEmail", "businessTerm"], "required": ["identifyType", "idType", "idName", "idNo", "idCard", "phone", "address", "smsCode", "legalName", "registrationPlace", "registrationArea", "businessScope", "enterprisePhone", "enterpriseEmail", "businessTerm"], "x-apifox-ignore-properties": []}, "example": {"identifyType": "person", "idType": "0", "idName": "牟毅", "idNo": "513124199501125275", "idCard": "identify-file/dc0b5f8b-355f-4c9d-9056-b180eb3b33d6.jpg,identify-file/7fc7e359-8a92-4467-9ab8-b05bbcbcc87d.jpg", "phone": "***********", "address": "成都市锦江区书院南街41号", "smsCode": "298122", "province": "四川省", "city": "成都市"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-228906612-run", "security": []}, "get": {"summary": "实名信息查询", "x-apifox-folder": "实名认证", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["实名认证"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}, "examples": {"1": {"summary": "成功示例", "value": {"code": 200, "msg": "已实名", "data": {"identifyId": 70, "userId": 101, "identifyType": "enterprise", "enterpriseType": "2", "idType": "1", "idName": "牟*", "idNo": "513124***********5275", "phone": "182****2826", "idCard": "identify-file/c748f7a9-d441-4393-a910-600c5d333f88.png,identify-file/f4344f38-cd0b-4fdf-be2a-ef9b74ac22bd.jpg", "legalName": null, "address": "成都市锦江区*******", "registrationPlace": null, "registrationArea": null, "businessScope": null, "enterprisePhone": null, "enterpriseEmail": null, "statusCd": "1000", "statusDate": "2025-01-02T17:47:41", "createStaff": "101", "createDate": "2025-01-02T17:47:41", "updateStaff": null, "updateDate": "2025-01-02T17:47:41", "remark": null, "cardMessage": null, "country": null, "province": "四川省", "city": "成都市", "businessTerm": null, "wcUserId": null, "smsCode": null, "legalPerson": null}}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-230261718-run", "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [{"url": "https://ts.sccdex.com", "description": "测试环境"}]}