<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import BottomNavigation from '@/components/BottomNavigation.vue'
import AppPageHeader from '@/components/AppPageHeader.vue'

const router = useRouter()

// 控制各个部分的展开状态
const expandedSections = ref({
  guidance: false,      // 指导单位
  support: false,       // 支持单位
  organizer: false,     // 主办单位
  operator: false,      // 运营单位
  introduction: true,   // 平台介绍
  qualification: false  // 平台资质
})

/**
 * 切换展开状态
 */
const toggleSection = (section: keyof typeof expandedSections.value) => {
  expandedSections.value[section] = !expandedSections.value[section]
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.go(-1)
}
</script>

<template>
  <AppPageHeader :title="'关于我们'" @back="$router.back()" />
  <div class="about-page with-bottom-nav" :style="{ paddingTop: '40px' }">


    <main class="main-content">
      <!-- 指导单位 -->
      <!-- <section class="info-section">
        <div class="section-header" @click="toggleSection('guidance')">
          <h2 class="section-title">指导单位</h2>
          <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.guidance }"></i>
        </div>
        <div v-show="expandedSections.guidance" class="section-content">
          <div class="organization-item">
            <div class="org-name">国家数字经济创新发展试验区建设工作组</div>
            <div class="org-desc">负责数字经济发展战略规划和政策指导</div>
          </div>
          <div class="organization-item">
            <div class="org-name">中国互联网协会</div>
            <div class="org-desc">推动互联网行业健康发展的国家级行业组织</div>
          </div>
        </div>
      </section> -->

      <!-- 支持单位 -->
      <!-- <section class="info-section">
        <div class="section-header" @click="toggleSection('support')">
          <h2 class="section-title">支持单位</h2>
          <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.support }"></i>
        </div>
        <div v-show="expandedSections.support" class="section-content">
          <div class="organization-item">
            <div class="org-name">中国区块链技术与产业发展论坛</div>
            <div class="org-desc">区块链技术标准制定和产业发展推进机构</div>
          </div>
          <div class="organization-item">
            <div class="org-name">数字资产研究院</div>
            <div class="org-desc">专注数字资产技术研究和应用创新</div>
          </div>
        </div>
      </section> -->

      <!-- 主办单位 -->
      <!-- <section class="info-section">
        <div class="section-header" @click="toggleSection('organizer')">
          <h2 class="section-title">主办单位</h2>
          <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.organizer }"></i>
        </div>
        <div v-show="expandedSections.organizer" class="section-content">
          <div class="organization-item">
            <div class="org-name">凌云科技集团有限公司</div>
            <div class="org-desc">专注于数字经济和区块链技术的创新型科技企业</div>
          </div>
        </div>
      </section> -->

      <!-- 运营单位 -->
      <!-- <section class="info-section">
        <div class="section-header" @click="toggleSection('operator')">
          <h2 class="section-title">运营单位</h2>
          <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.operator }"></i>
        </div>
        <div v-show="expandedSections.operator" class="section-content">
          <div class="organization-item">
            <div class="org-name">凌云数字资产（北京）科技有限公司</div>
            <div class="org-desc">凌云科技集团全资子公司，专业从事数字资产平台运营</div>
          </div>
        </div>
      </section> -->

      <!-- 平台介绍 -->
      <section class="info-section platform-intro">
        <div class="section-header" @click="toggleSection('introduction')">
          <h2 class="section-title">平台介绍</h2>
          <!-- <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.introduction }"></i> -->
        </div>
        <div v-show="expandedSections.introduction" class="section-content">
          <div class="platform-content">
            <h3 class="platform-name">凌云数资平台</h3>
            <div class="platform-description">
              <p>
                “凌云数资”是四川省文化数据交易平台旗下数字资产发行平台，以“激活文化资源的数字价值”为核心使命，依托区块链技术实现文化资源的数字化确权、发行与流通。平台构建了“衍生级、系列级、品牌级、特殊级”四级运营体系，覆盖从跨界衍生到顶级文化资产的全场景，通过“数字-实物权益互通”“数字场景融合”等创新机制，让文化内容成为可收藏、可互动、可运用的数字资产，最终实现“文化资源→数字资产→商业价值”的闭环生态。
              </p>
              <!-- <p>
                通过区块链、大数据、3D建模等技术，实现文化资源的可信数字化转化与可持续资产运营，为文化产业注入新动能。
              </p> -->
              <!-- <p>
                通过区块链、大数据、3D建模等技术，实现文化资源的可信数字化转化与可持续资产运营，为四川文化产业注入新动能。
              </p> -->
            </div>
          </div>
        </div>
      </section>

      <!-- 平台资质 -->
      <!-- <section class="info-section">
        <div class="section-header" @click="toggleSection('qualification')">
          <h2 class="section-title">平台资质</h2>
          <i class="fas fa-chevron-down section-icon" :class="{ 'expanded': expandedSections.qualification }"></i>
        </div>
        <div v-show="expandedSections.qualification" class="section-content">
          <div class="qualification-grid">
            <div class="qualification-item">
              <div class="qual-icon">
                <i class="fas fa-certificate"></i>
              </div>
              <div class="qual-content">
                <div class="qual-title">区块链信息服务备案</div>
                <div class="qual-number">备案号：京网信备11010819313-19201号</div>
              </div>
            </div>
            <div class="qualification-item">
              <div class="qual-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div class="qual-content">
                <div class="qual-title">信息安全等级保护认证</div>
                <div class="qual-number">等保三级认证</div>
              </div>
            </div>
            <div class="qualification-item">
              <div class="qual-icon">
                <i class="fas fa-award"></i>
              </div>
              <div class="qual-content">
                <div class="qual-title">ISO27001信息安全管理体系</div>
                <div class="qual-number">国际标准认证</div>
              </div>
            </div>
            <div class="qualification-item">
              <div class="qual-icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <div class="qual-content">
                <div class="qual-title">增值电信业务经营许可证</div>
                <div class="qual-number">ICP许可证</div>
              </div>
            </div>
          </div>
        </div>
      </section> -->

      <!-- 联系我们 -->
      <section class="contact-section">
        <h3 class="contact-title">联系我们</h3>
        <div class="contact-grid">
          <div class="contact-item">
            <i class="fas fa-envelope"></i>
            <span><EMAIL></span>
          </div>
          <!-- <div class="contact-item">
            <i class="fas fa-phone"></i>
            <span>19113980787</span>
          </div> -->
          <!-- <div class="contact-item">
            <i class="fas fa-map-marker-alt"></i>
            <span>北京市朝阳区数字经济产业园区</span>
          </div> -->
        </div>
      </section>
    </main>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<style scoped>
/* CSS变量定义 */
.about-page {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --accent-color: #f4a261;
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --border-light: rgba(248, 246, 240, 0.1);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);

  background: var(--gradient-hero);
  color: var(--text-primary);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  padding: 12px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  max-width: 100%;
  margin: 0 auto;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--primary-color);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.back-btn:hover {
  background: rgba(212, 165, 116, 0.2);
  transform: translateX(-2px);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  text-align: center;
}

.header-actions {
  width: 40px; /* 与back-btn宽度相同，保持对称 */
}

/* 主要内容 */
.main-content {
  padding: 0 16px 100px 16px;
}

/* 信息区域 */
.info-section {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  margin-bottom: 16px;
  overflow: hidden;
  backdrop-filter: blur(20px);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid var(--border-light);
}

.section-header:hover {
  background: rgba(255, 255, 255, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.section-icon {
  color: var(--primary-color);
  font-size: 16px;
  transition: transform 0.3s ease;
}

.section-icon.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding: 20px;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 组织机构项 */
.organization-item {
  margin-bottom: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border-left: 4px solid var(--primary-color);
}

.organization-item:last-child {
  margin-bottom: 0;
}

.org-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.org-desc {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 平台介绍 */
.platform-intro .section-content {
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.1) 0%, rgba(212, 165, 116, 0.05) 100%);
}

.platform-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 16px 0;
  text-align: center;
}

.platform-description {
  line-height: 1.6;
}

.platform-description p {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 16px;
  text-align: justify;
}

.platform-description p:last-child {
  margin-bottom: 0;
}

/* 资质认证 */
.qualification-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.qualification-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.qualification-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.qual-icon {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  color: white;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.qual-content {
  flex: 1;
}

.qual-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.qual-number {
  font-size: 12px;
  color: var(--text-muted);
  font-family: 'Courier New', monospace;
}

/* 联系我们 */
.contact-section {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 20px;
  margin-top: 20px;
  backdrop-filter: blur(20px);
}

.contact-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 16px 0;
  text-align: center;
}

.contact-grid {
  display: grid;
  gap: 12px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
}

.contact-item i {
  color: var(--primary-color);
  width: 20px;
  text-align: center;
}

.contact-item span {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 0 12px 100px 12px;
  }
  
  .qualification-grid {
    grid-template-columns: 1fr;
  }
  
  .platform-description p {
    font-size: 13px;
  }
  
  .section-header {
    padding: 16px;
  }
  
  .section-content {
    padding: 16px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .about-page {
    padding-bottom: max(100px, env(safe-area-inset-bottom));
  }
}
</style>
