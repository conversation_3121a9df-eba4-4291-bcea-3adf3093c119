<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的订单 - 四川省数字资产发行平�?/title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
            background: linear-gradient(135deg, #e6b422, #c99a10);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #b22222, #8b1a1a);
            color: #f8f0e0;
            box-shadow: 0 3px 10px rgba(178, 34, 34, 0.4);
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(178, 34, 34, 0.6);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .tag-accent {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .tag-success {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .tag-warning {
            background: rgba(205, 133, 63, 0.2);
            color: #cd853f;
            border: 1px solid rgba(205, 133, 63, 0.4);
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: #f8f0e0;
        }
        
        .search-section {
            margin: 16px 0;
        }
        
        .search-container {
            display: flex;
            background: rgba(60, 40, 30, 0.7);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #f0e0d0;
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: rgba(240, 224, 208, 0.5);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #daa520, #b8860b);
            border: none;
            color: #2a1616;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .status-tabs {
            display: flex;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 24px;
            padding: 4px;
            margin: 16px 0;
            border: 1px solid rgba(218, 165, 32, 0.2);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .status-tabs::-webkit-scrollbar {
            display: none;
        }
        
        .status-tab {
            flex: 1;
            min-width: 80px;
            padding: 8px 12px;
            border: none;
            background: transparent;
            color: rgba(240, 224, 208, 0.7);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-tab.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            font-weight: 600;
        }
        
        .order-list {
            margin: 16px 0;
        }
        
        .order-card {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(218, 165, 32, 0.3);
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid rgba(218, 165, 32, 0.2);
        }
        
        .order-number {
            font-size: 13px;
            color: #daa520;
            font-weight: 600;
        }
        
        .order-status {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }
        
        .status-pending {
            background: rgba(205, 133, 63, 0.2);
            color: #cd853f;
            border: 1px solid rgba(205, 133, 63, 0.4);
        }
        
        .status-paid {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .status-cancelled {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .status-completed {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .order-content {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .order-image {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #daa520;
            font-size: 10px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .order-info {
            flex: 1;
            min-width: 0;
        }
        
        .order-title {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .order-details {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
            margin-bottom: 6px;
        }
        
        .order-price {
            font-size: 16px;
            font-weight: bold;
            color: #b22222;
        }
        
        .order-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            padding-top: 8px;
            border-top: 1px solid rgba(218, 165, 32, 0.2);
        }
        
        .order-time {
            font-size: 11px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .order-actions {
            display: flex;
            gap: 8px;
        }
        
        .countdown-timer {
            background: rgba(178, 34, 34, 0.2);
            border: 1px solid rgba(178, 34, 34, 0.4);
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 10px;
            color: #b22222;
            font-weight: 600;
            margin-left: 8px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: rgba(218, 165, 32, 0.5);
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #f0e0d0;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 24px;
        }
        
        .load-more {
            text-align: center;
            margin: 24px 0;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-item:hover {
            color: #f8f0e0;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .payment-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 100;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .payment-content {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 16px;
            padding: 24px;
            width: 90%;
            max-width: 360px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .payment-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 20px;
        }
        
        .payment-order-info {
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
        }
        
        .payment-order-title {
            font-size: 14px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 4px;
        }
        
        .payment-order-price {
            font-size: 18px;
            font-weight: bold;
            color: #b22222;
        }
        
        .payment-methods {
            margin: 20px 0;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid rgba(218, 165, 32, 0.2);
            transition: all 0.3s;
        }
        
        .payment-method:hover {
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
        }
        
        .payment-method.selected {
            border-color: #daa520;
            background: rgba(218, 165, 32, 0.1);
        }
        
        .payment-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .unionpay { background: #1E88E5; color: white; }
        .alipay { background: #00A6FB; color: white; }
        .wechatpay { background: #00C851; color: white; }
        
        .payment-name {
            flex: 1;
            font-size: 14px;
            color: #f0e0d0;
        }
        
        .payment-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">我的订单</h1>
                <div style="width: 24px;"></div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 搜索�?-->
            <section class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索订单号或商品名称..." id="search-input">
                    <button class="search-btn" onclick="searchOrders()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 订单状态标�?-->
            <section class="status-tabs">
                <button class="status-tab active" data-status="all">全部</button>
                <button class="status-tab" data-status="pending">待支�?/button>
                <button class="status-tab" data-status="paid">已支�?/button>
                <button class="status-tab" data-status="completed">已完�?/button>
                <button class="status-tab" data-status="cancelled">已取�?/button>
            </section>

            <!-- 订单列表 -->
            <section class="order-list" id="order-list">
                <!-- 待支付订�?-->
                <div class="order-card" data-status="pending" data-order-id="ORD2024011501">
                    <div class="order-header">
                        <div class="order-number">订单号：ORD2024011501</div>
                        <div class="order-status status-pending">
                            待支�?
                            <span class="countdown-timer" id="countdown-1">14:59</span>
                        </div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            太阳神鸟
                        </div>
                        <div class="order-info">
                            <div class="order-title">金沙太阳神鸟金饰数字藏品</div>
                            <div class="order-details">古蜀文明 · 限量�?· 数量�?</div>
                            <div class="order-price">�?99.00</div>
                        </div>
                    </div>
                    <div class="order-footer">
                        <div class="order-time">下单时间�?024-01-15 10:30</div>
                        <div class="order-actions">
                            <button class="btn btn-small btn-secondary" onclick="cancelOrder('ORD2024011501')">取消订单</button>
                            <button class="btn btn-small btn-primary" onclick="payOrder('ORD2024011501', '金沙太阳神鸟金饰数字藏品', 299)">立即支付</button>
                        </div>
                    </div>
                </div>

                <!-- 已支付订�?-->
                <div class="order-card" data-status="paid" data-order-id="ORD2024011401">
                    <div class="order-header">
                        <div class="order-number">订单号：ORD2024011401</div>
                        <div class="order-status status-paid">已支�?/div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            青铜面具
                        </div>
                        <div class="order-info">
                            <div class="order-title">三星堆青铜面具数字藏�?/div>
                            <div class="order-details">三星�?· 考古发现 · 数量�?</div>
                            <div class="order-price">�?99.00</div>
                        </div>
                    </div>
                    <div class="order-footer">
                        <div class="order-time">支付时间�?024-01-14 16:20</div>
                        <div class="order-actions">
                            <button class="btn btn-small btn-secondary" onclick="viewOrderDetail('ORD2024011401')">查看详情</button>
                            <button class="btn btn-small btn-primary" onclick="contactService('ORD2024011401')">联系客服</button>
                        </div>
                    </div>
                </div>

                <!-- 已完成订�?-->
                <div class="order-card" data-status="completed" data-order-id="ORD2024011201">
                    <div class="order-header">
                        <div class="order-number">订单号：ORD2024011201</div>
                        <div class="order-status status-completed">已完�?/div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            金沙遗址
                        </div>
                        <div class="order-info">
                            <div class="order-title">金沙遗址3D全景数字藏品</div>
                            <div class="order-details">金沙遗址 · 3D模型 · 数量�?</div>
                            <div class="order-price">�?99.00</div>
                        </div>
                    </div>
                    <div class="order-footer">
                        <div class="order-time">完成时间�?024-01-12 14:15</div>
                        <div class="order-actions">
                            <button class="btn btn-small btn-secondary" onclick="viewAsset('ORD2024011201')">查看藏品</button>
                            <button class="btn btn-small btn-primary" onclick="shareAsset('ORD2024011201')">分享</button>
                        </div>
                    </div>
                </div>

                <!-- 已取消订�?-->
                <div class="order-card" data-status="cancelled" data-order-id="ORD2024011101">
                    <div class="order-header">
                        <div class="order-number">订单号：ORD2024011101</div>
                        <div class="order-status status-cancelled">已取�?/div>
                    </div>
                    <div class="order-content">
                        <div class="order-image">
                            蜀锦纹�?
                        </div>
                        <div class="order-info">
                            <div class="order-title">蜀锦织品纹样集数字藏品</div>
                            <div class="order-details">蜀锦文�?· 传统工艺 · 数量�?</div>
                            <div class="order-price">�?9.00</div>
                        </div>
                    </div>
                    <div class="order-footer">
                        <div class="order-time">取消时间�?024-01-11 09:45</div>
                        <div class="order-actions">
                            <button class="btn btn-small btn-secondary" onclick="deleteOrder('ORD2024011101')">删除订单</button>
                            <button class="btn btn-small btn-primary" onclick="rebuyOrder('ORD2024011101')">重新购买</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 空状�?-->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="empty-title">暂无订单</div>
                <div class="empty-description">
                    您还没有任何订单记录<br>
                    快去选购心仪的数字藏品吧
                </div>
                <button class="btn btn-primary" onclick="goToShop()">去购�?/button>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" id="load-more">
                <button class="btn btn-secondary">
                    <i class="fas fa-plus"></i> 加载更多订单
                </button>
            </div>
        </div>
    </main>

    <!-- 支付弹窗 -->
    <div class="payment-modal" id="paymentModal">
        <div class="payment-content">
            <div class="payment-title">订单支付</div>
            <div class="payment-order-info" id="payment-order-info">
                <div class="payment-order-title">金沙太阳神鸟金饰数字藏品</div>
                <div class="payment-order-price">�?99.00</div>
            </div>
            
            <div class="payment-methods">
                <div class="payment-method selected" data-method="unionpay">
                    <div class="payment-icon unionpay">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="payment-name">银联云闪�?/div>
                    <i class="fas fa-check-circle" style="color: #daa520;"></i>
                </div>
                <div class="payment-method" data-method="alipay">
                    <div class="payment-icon alipay">
                        <i class="fab fa-alipay"></i>
                    </div>
                    <div class="payment-name">支付�?/div>
                </div>
                <div class="payment-method" data-method="wechatpay">
                    <div class="payment-icon wechatpay">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="payment-name">微信支付</div>
                </div>
            </div>
            
            <div class="payment-actions">
                <button class="btn btn-secondary" style="flex: 1;" onclick="hidePaymentModal()">取消</button>
                <button class="btn btn-primary" style="flex: 2;" onclick="processPayment()">确认支付</button>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        let currentPaymentOrderId = '';
        let currentPaymentPrice = 0;

        // 订单状态切�?
        document.querySelectorAll('.status-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.status-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const status = this.getAttribute('data-status');
                filterOrdersByStatus(status);
            });
        });

        function filterOrdersByStatus(status) {
            const orders = document.querySelectorAll('.order-card');
            let visibleCount = 0;
            
            orders.forEach(order => {
                const orderStatus = order.getAttribute('data-status');
                
                if (status === 'all' || orderStatus === status) {
                    order.style.display = 'block';
                    visibleCount++;
                } else {
                    order.style.display = 'none';
                }
            });
            
            // 显示空状�?
            const emptyState = document.getElementById('empty-state');
            const orderList = document.getElementById('order-list');
            const loadMore = document.getElementById('load-more');
            
            if (visibleCount === 0) {
                orderList.style.display = 'none';
                loadMore.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                orderList.style.display = 'block';
                loadMore.style.display = 'block';
                emptyState.style.display = 'none';
            }
        }

        // 搜索功能
        function searchOrders() {
            const searchText = document.getElementById('search-input').value.trim().toLowerCase();
            const orders = document.querySelectorAll('.order-card');
            
            orders.forEach(order => {
                const orderNumber = order.querySelector('.order-number').textContent.toLowerCase();
                const orderTitle = order.querySelector('.order-title').textContent.toLowerCase();
                const isMatch = orderNumber.includes(searchText) || orderTitle.includes(searchText);
                order.style.display = isMatch ? 'block' : 'none';
            });
        }

        // 搜索框回车事�?
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchOrders();
            }
        });

        // 倒计时功�?
        function updateCountdown() {
            const countdownElements = document.querySelectorAll('.countdown-timer');
            
            countdownElements.forEach(element => {
                const currentTime = element.textContent.split(':');
                let minutes = parseInt(currentTime[0]);
                let seconds = parseInt(currentTime[1]);
                
                if (seconds > 0) {
                    seconds--;
                } else if (minutes > 0) {
                    minutes--;
                    seconds = 59;
                } else {
                    // 倒计时结束，订单超时
                    const orderCard = element.closest('.order-card');
                    orderCard.setAttribute('data-status', 'cancelled');
                    orderCard.querySelector('.order-status').className = 'order-status status-cancelled';
                    orderCard.querySelector('.order-status').innerHTML = '已超�?;
                    element.remove();
                    
                    // 更新操作按钮
                    const actions = orderCard.querySelector('.order-actions');
                    actions.innerHTML = `
                        <button class="btn btn-small btn-secondary" onclick="deleteOrder('${orderCard.getAttribute('data-order-id')}')">删除订单</button>
                        <button class="btn btn-small btn-primary" onclick="rebuyOrder('${orderCard.getAttribute('data-order-id')}')">重新购买</button>
                    `;
                    return;
                }
                
                element.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            });
        }

        // 每秒更新倒计�?
        setInterval(updateCountdown, 1000);

        // 订单操作
        function cancelOrder(orderId) {
            if (confirm('确定要取消这个订单吗�?)) {
                const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
                orderCard.setAttribute('data-status', 'cancelled');
                orderCard.querySelector('.order-status').className = 'order-status status-cancelled';
                orderCard.querySelector('.order-status').textContent = '已取�?;
                
                // 移除倒计�?
                const countdown = orderCard.querySelector('.countdown-timer');
                if (countdown) countdown.remove();
                
                // 更新操作按钮
                const actions = orderCard.querySelector('.order-actions');
                actions.innerHTML = `
                    <button class="btn btn-small btn-secondary" onclick="deleteOrder('${orderId}')">删除订单</button>
                    <button class="btn btn-small btn-primary" onclick="rebuyOrder('${orderId}')">重新购买</button>
                `;
                
                alert('订单已取�?);
            }
        }

        function payOrder(orderId, productName, price) {
            currentPaymentOrderId = orderId;
            currentPaymentPrice = price;
            
            document.getElementById('payment-order-info').innerHTML = `
                <div class="payment-order-title">${productName}</div>
                <div class="payment-order-price">�?{price}.00</div>
            `;
            
            showPaymentModal();
        }

        function deleteOrder(orderId) {
            if (confirm('确定要删除这个订单吗？删除后无法恢复�?)) {
                const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
                orderCard.remove();
                alert('订单已删�?);
                
                // 检查是否需要显示空状�?
                const visibleOrders = document.querySelectorAll('.order-card[style*="block"], .order-card:not([style])');
                if (visibleOrders.length === 0) {
                    filterOrdersByStatus(document.querySelector('.status-tab.active').getAttribute('data-status'));
                }
            }
        }

        function rebuyOrder(orderId) {
            const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
            const productName = orderCard.querySelector('.order-title').textContent;
            alert(`重新购买"${productName}"`);
            // 这里可以跳转到商品详情页
        }

        function viewOrderDetail(orderId) {
            alert(`查看订单${orderId}的详细信息`);
            // 这里可以跳转到订单详情页
        }

        function contactService(orderId) {
            alert(`联系客服处理订单${orderId}`);
            // 这里可以打开客服聊天或拨打客服电�?
        }

        function viewAsset(orderId) {
            alert(`查看订单${orderId}的数字藏品`);
            // 这里可以跳转到我的资产页�?
        }

        function shareAsset(orderId) {
            const orderCard = document.querySelector(`[data-order-id="${orderId}"]`);
            const productName = orderCard.querySelector('.order-title').textContent;
            alert(`分享"${productName}"`);
        }

        // 支付相关
        function showPaymentModal() {
            document.getElementById('paymentModal').style.display = 'flex';
        }

        function hidePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
        }

        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        function processPayment() {
            const selectedMethod = document.querySelector('.payment-method.selected').getAttribute('data-method');
            
            hidePaymentModal();
            
            // 模拟支付处理
            alert('正在处理支付...');
            
            setTimeout(() => {
                const orderCard = document.querySelector(`[data-order-id="${currentPaymentOrderId}"]`);
                orderCard.setAttribute('data-status', 'paid');
                orderCard.querySelector('.order-status').className = 'order-status status-paid';
                orderCard.querySelector('.order-status').textContent = '已支�?;
                
                // 移除倒计�?
                const countdown = orderCard.querySelector('.countdown-timer');
                if (countdown) countdown.remove();
                
                // 更新操作按钮
                const actions = orderCard.querySelector('.order-actions');
                actions.innerHTML = `
                    <button class="btn btn-small btn-secondary" onclick="viewOrderDetail('${currentPaymentOrderId}')">查看详情</button>
                    <button class="btn btn-small btn-primary" onclick="contactService('${currentPaymentOrderId}')">联系客服</button>
                `;
                
                // 更新时间显示
                const footer = orderCard.querySelector('.order-time');
                const now = new Date();
                footer.textContent = `支付时间�?{now.getFullYear()}-${String(now.getMonth()+1).padStart(2,'0')}-${String(now.getDate()).padStart(2,'0')} ${String(now.getHours()).padStart(2,'0')}:${String(now.getMinutes()).padStart(2,'0')}`;
                
                alert('支付成功�?);
            }, 2000);
        }

        // 其他功能
        function goToShop() {
            window.location.href = 'index.html';
        }

        // 点击遮罩关闭弹窗
        document.getElementById('paymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hidePaymentModal();
            }
        });

        // 订单卡片点击
        document.querySelectorAll('.order-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮，不跳转
                if (e.target.closest('.order-actions')) {
                    e.stopPropagation();
                    return;
                }
                
                const orderId = this.getAttribute('data-order-id');
                viewOrderDetail(orderId);
            });
        });

        // 加载更多
        document.querySelector('.load-more button').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载�?..';
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-plus"></i> 加载更多订单';
                alert('已加载更多订�?);
            }, 1500);
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .status-tab, .order-card').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
