<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import BottomNavigation from '@/components/BottomNavigation.vue'
import SearchBox from '@/components/SearchBox.vue'
import AssetCard from '@/components/AssetCard.vue'
import { BannerAPI, type BannerItem, RecommendationAPI, type RecommendationAsset, PresaleAPI, type PresaleAsset } from '@/api'
import { DictionaryAPI } from '@/api/dictionary'
import type { DictionaryItem } from '@/api/dictionary'
import { useNotification } from '@/composables/useNotification'

// 检测是否为触摸设备
const isTouchDevice = typeof window !== 'undefined' && 'ontouchstart' in window

// 路由和通知
const router = useRouter()
const notification = useNotification()

// 搜索相关
const searchText = ref('')

// 轮播相关
const banners = ref<BannerItem[]>([])
const currentBannerIndex = ref(0)
const bannerTimer = ref<number | null>(null)
const isLoadingBanners = ref(false)
const slideDirection = ref<'left' | 'right'>('left')

// 移除滑动相关变量
// const touchStartX = ref(0)
// const touchEndX = ref(0)
// const isSliding = ref(false)
// const slideOffset = ref(0)
// const canClickBanner = ref(true)
// const wasSwiped = ref(false)
// const isActuallySwiping = ref(false)

// 移除defaultBanners、defaultUpcomingProducts、defaultHotProducts等模拟数据定义和使用

// 即将发售数据
const upcomingProducts = ref<PresaleAsset[]>([])
const isLoadingUpcomingProducts = ref(false)

// 分页参数
const pageSize = 4

// 热门推荐数据
const hotProducts = ref<RecommendationAsset[]>([])
const isLoadingHotProducts = ref(false)

// 资产类型字典数据
const assetTypesList = ref<DictionaryItem[]>([])
const isLoadingAssetTypes = ref(false)

// 获取轮播图数据
const loadBanners = async () => {
  try {
    isLoadingBanners.value = true
    console.log('📡 开始请求轮播图数据...')
    const response = await BannerAPI.getBannerList()
    
    console.log('📥 轮播图API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      console.log('🔍 原始轮播图数据:', response.data)
      console.log('🔍 数据类型:', typeof response.data, Array.isArray(response.data))
      
      // 检查每个轮播图的字段结构
      response.data.forEach((banner, index) => {
        console.log(`Banner ${index}:`, {
          id: banner.id,
          title: banner.title,
          isActive: banner.isActive,
          isActiveType: typeof banner.isActive,
          sortOrder: banner.sortOrder,
          imageUrl: banner.imageUrl,
          jumpType: banner.jumpType,
          contentId: banner.contentId,
          linkUrl: banner.linkUrl
        })
      })
      
      // 只显示激活状态的轮播图，并按排序字段排序
      const activeBanners = response.data.filter(banner => {
        // 处理可能的字符串类型或数字类型的isActive
        const isActiveValue = banner.isActive === true || 
                             String(banner.isActive) === 'true' || 
                             Number(banner.isActive) === 1
        console.log(`Banner ${banner.id}: isActive = ${banner.isActive} (${typeof banner.isActive}) -> filtered: ${isActiveValue}`)
        return isActiveValue
      })
      
      banners.value = activeBanners.sort((a, b) => a.sortOrder - b.sortOrder)
      
      console.log('🎯 过滤后的激活轮播图:', activeBanners.length, '条')
      
      // 如果没有激活的轮播图，使用全部数据
      if (activeBanners.length === 0) {
        console.warn('⚠️ 没有激活状态的轮播图，使用全部轮播图数据')
        banners.value = response.data.sort((a, b) => a.sortOrder - b.sortOrder)
        console.log('🔄 使用全部数据:', banners.value.length, '条')
      } else {
        banners.value = activeBanners
        console.log('✅ 使用激活状态轮播图:', banners.value.length, '条')
      }
    } else {
      // 接口返回空数据，使用默认数据
      banners.value = []
      console.warn('⚠️ 接口返回空数据，使用默认轮播图')
    }
  } catch (error) {
    console.error('❌ 获取轮播图失败:', error)
    // 接口失败时使用默认数据，不显示错误提示给用户
    banners.value = []
    console.log('🔄 使用默认轮播图数据')
  } finally {
    isLoadingBanners.value = false
  }
}

// 获取热门推荐数据
const loadHotProducts = async () => {
  try {
    isLoadingHotProducts.value = true
    console.log('📡 开始请求热门推荐数据...')
    const response = await RecommendationAPI.getHotRecommendations(1, 8)
    
    console.log('📥 热门推荐API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      console.log('✅ 热门推荐数据加载成功:', response.data.length, '条')
      
      hotProducts.value = response.data
      console.log('✅ 热门推荐数据加载成功:', hotProducts.value.length, '条')
    } else {
      // 接口返回空数据，显示暂无数据状态
      hotProducts.value = []
      console.warn('⚠️ 接口返回空数据，显示暂无数据状态')
    }
  } catch (error) {
    console.error('❌ 获取热门推荐失败:', error)
    // 接口失败时显示暂无数据状态
    hotProducts.value = []
    console.log('🔄 显示暂无数据状态')
  } finally {
    isLoadingHotProducts.value = false
  }
}

// 获取即将发售数据
const loadUpcomingProducts = async () => {
  try {
    isLoadingUpcomingProducts.value = true
    console.log('📡 开始请求即将发售数据...')
    const response = await PresaleAPI.getPresaleList(pageSize)
    
    console.log('📥 即将发售API响应:', response)
    
    if (response.code === 200 && response.rows && response.rows.length > 0) {
      console.log('✅ 即将发售数据加载成功:', response.rows.length, '条')
      
      upcomingProducts.value = response.rows
      console.log('✅ 即将发售数据加载成功:', upcomingProducts.value.length, '条')
    } else {
      // 接口返回空数据，显示暂无数据状态
      upcomingProducts.value = []
      console.warn('⚠️ 接口返回空数据，显示暂无数据状态')
    }
  } catch (error) {
    console.error('❌ 获取即将发售失败:', error)
    // 接口失败时显示暂无数据状态
    upcomingProducts.value = []
    console.log('🔄 显示暂无数据状态')
  } finally {
    isLoadingUpcomingProducts.value = false
  }
}

// 获取资产类型字典数据
const loadAssetTypes = async () => {
  try {
    isLoadingAssetTypes.value = true
    console.log('📡 开始请求资产类型字典数据...')
    const response = await DictionaryAPI.getDictionary('dig_asset_type')
    
    console.log('📥 资产类型字典API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      assetTypesList.value = response.data
      console.log('✅ 资产类型字典数据加载成功:', assetTypesList.value.length, '条')
    } else {
      assetTypesList.value = []
      console.warn('⚠️ 资产类型字典接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取资产类型字典失败:', error)
    assetTypesList.value = []
  } finally {
    isLoadingAssetTypes.value = false
  }
}

// 根据资产类型值获取对应的汉字标签
const getAssetTypeLabel = (assetTypeValue: string): string => {
  if (!assetTypeValue || !assetTypesList.value.length) {
    return assetTypeValue || ''
  }
  
  const found = assetTypesList.value.find(item => item.value === assetTypeValue)
  return found ? found.label : assetTypeValue
}



// 方法
const handleSearch = (keyword: string) => {
  if (keyword?.trim()) {
    console.log(`搜索: ${keyword}`)
    // 跳转到搜索结果页面，由SearchBox组件处理
  }
}

const handleProductClick = (product: RecommendationAsset | any) => {
  console.log(`查看${product.assetName}详情`)
  // 跳转到资产详情页面
  router.push(`/asset/${product.assetId}`)
}

const handleUpcomingProductClick = (product: PresaleAsset | any) => {
  console.log(`查看${product.assetName}详情`)
  // 跳转到资产详情页面
  router.push(`/asset/${product.assetId}`)
}

const handleBannerClick = (banner: BannerItem) => {
  console.log('handleBannerClick触发', banner)
  if (banner.jumpType === 'A' && banner.contentId) {
    router.push(`/asset/${banner.contentId}`)
    return
  }
  if (banner.jumpType === 'B' && banner.contentId) {
    router.push(`/activity/${banner.contentId}`)
    return
  }
  if (banner.jumpType === 'C' ) {
    return
  }
  if (banner.linkUrl) {
    if (banner.linkUrl.startsWith('http')) {
      window.open(banner.linkUrl, '_blank')
    } else {
      router.push(banner.linkUrl)
    }
  } else {
    // 无跳转配置
  }
}

const nextBanner = () => {
  slideDirection.value = 'left'
  currentBannerIndex.value = (currentBannerIndex.value + 1) % banners.value.length
}

const prevBanner = () => {
  slideDirection.value = 'right'
  currentBannerIndex.value = currentBannerIndex.value === 0
    ? banners.value.length - 1
    : currentBannerIndex.value - 1
}

const startBannerTimer = () => {
  bannerTimer.value = setInterval(() => {
    nextBanner()
  }, 5000)
}

const stopBannerTimer = () => {
  if (bannerTimer.value) {
    clearInterval(bannerTimer.value)
    bannerTimer.value = null
  }
}

const goToBanner = (index: number) => {
  slideDirection.value = index > currentBannerIndex.value ? 'left' : 'right'
  currentBannerIndex.value = index
}

// 移除handleTouchStart、handleTouchMove、handleTouchEnd等滑动相关函数

const handleBannerClickPC = () => {
  if (!isTouchDevice) {
    handleBannerClick(banners.value[currentBannerIndex.value])
  }
}

// 导航方法
const goToPresale = () => {
  router.push('/presale')
}

const goToExhibition = () => {
  router.push('/exhibition')
}

// 图片加载错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
  // 可以在这里添加默认图片或其他处理逻辑
}

// 生命周期
onMounted(async () => {
  // 并行加载轮播图、热门推荐、即将发售数据和资产类型字典
  await Promise.all([
    loadBanners(),
    loadHotProducts(),
    loadUpcomingProducts(),
    loadAssetTypes()
  ])
  
  // 如果有轮播图数据，才启动轮播定时器
  if (banners.value.length > 1) {
    startBannerTimer()
  }
})

onUnmounted(() => {
  stopBannerTimer()
})
</script>

<template>
  <div class="home-container">
    <main class="main-content">
      <div class="container">
        <!-- 搜索栏 -->
        <SearchBox 
          v-model="searchText"
          @search="handleSearch"
        />

        <!-- Banner轮播 -->
        <section class="banner-section">
          <div 
            class="banner-slider" 
            @mouseenter="stopBannerTimer" 
            @mouseleave="startBannerTimer"
            :style="{}"
          >
            <!-- 加载状态 -->
            <div v-if="isLoadingBanners" class="banner-loading">
              <div class="loading-spinner"></div>
              <p>加载中...</p>
            </div>
            
            <!-- 只渲染当前active的banner，带滑动动画 -->
            <transition :name="slideDirection === 'left' ? 'slide-left' : 'slide-right'">
              <div 
                v-if="banners.length"
                :key="banners[currentBannerIndex].id"
                class="banner-slide active"
                :style="{ 
                  backgroundImage: `url(${banners[currentBannerIndex].imageUrl})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }"
                @click="handleBannerClick(banners[currentBannerIndex])"
              >
                <!-- 纯图片展示 -->
              </div>
            </transition>
            
            <!-- 轮播指示器 -->
            <div v-if="!isLoadingBanners && banners.length > 1" class="banner-indicators">
              <button
                v-for="(banner, index) in banners"
                :key="`indicator-${banner.id}`"
                class="banner-indicator"
                :class="{ active: index === currentBannerIndex }"
                @click.stop="goToBanner(index)"
              ></button>
            </div>
            
            <!-- 滑动提示 -->
            <div v-if="!isLoadingBanners && banners.length > 1" class="swipe-hint">
              <i class="fas fa-hand-pointer"></i>
              <span>左右滑动切换</span>
            </div>
          </div>
        </section>

<!-- 热门推荐 -->
<section class="section">
  <div class="section-header">
    <h3 class="section-title">
      <i class="fas fa-fire"></i>
      热门推荐
    </h3>
    <a href="#" class="section-more" @click.prevent="goToExhibition">查看更多 →</a>
  </div>
  
  <!-- 加载状态 -->
  <div v-if="isLoadingHotProducts" class="loading-container">
    <div class="loading-spinner"></div>
    <p>加载热门推荐中...</p>
  </div>
  
  <!-- 暂无数据状态 -->
  <div v-else-if="hotProducts.length === 0" class="empty-state">
    <div class="empty-icon">
      <i class="fas fa-fire"></i>
    </div>
    <p>暂无热门推荐数据</p>
  </div>
  
  <!-- 热门推荐列表 -->
  <div v-else class="products-grid">
    <AssetCard 
      v-for="product in hotProducts" 
      :key="product.id"
      :asset="product"
      :show-asset-type="true"
      :show-issuer-label="false"
      :asset-types-list="assetTypesList"
      @click="handleProductClick"
    />
  </div>
</section>

        <!-- 即将发售 -->
        <section class="section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="fas fa-clock"></i>
              即将发售
            </h3>
            <a href="#" class="section-more" @click.prevent="goToPresale">查看全部 →</a>
          </div>
          
          <!-- 加载状态 -->
          <div v-if="isLoadingUpcomingProducts" class="loading-container">
            <div class="loading-spinner"></div>
            <p>加载即将发售中...</p>
          </div>
          
          <!-- 暂无数据状态 -->
          <div v-else-if="upcomingProducts.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-clock"></i>
            </div>
            <p>暂无即将发售的数字资产</p>
          </div>
          
          <!-- 即将发售列表 -->
          <div v-else class="products-grid">
            <AssetCard 
              v-for="product in upcomingProducts" 
              :key="product.id"
              :asset="product as any"
              :show-asset-type="true"
              :show-issuer-label="false"
              :asset-types-list="assetTypesList"
              @click="handleUpcomingProductClick"
            />
          </div>
        </section>


      </div>
    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />
  </div>
</template>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

/* 基础样式 */
.home-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 12px;
}

/* 文字样式 */
.text-large {
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  margin: 8px 0;
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: #fff;
  margin: 8px 0;
}

.text-normal {
  font-size: 15px;
  color: #e0e0e0;
  margin: 6px 0;
}

.text-small {
  font-size: 12px;
  color: var(--primary-gold);
  margin: 6px 0;
}

.text-primary {
  color: var(--primary-gold);
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 10px 24px;
  border-radius: 24px;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  margin: 4px;
  min-width: 120px;
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  background: var(--gradient-primary);
}

.btn-secondary {
  background: rgba(40, 40, 40, 0.7);
  color: #e0e0e0;
  border: 1px solid rgba(218, 165, 32, 0.3);
}

.btn-secondary:hover {
  background: rgba(60, 60, 60, 0.7);
  border-color: var(--primary-gold);
}

.btn-small {
  padding: 6px 16px;
  font-size: 13px;
  min-width: auto;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  margin: 2px;
}

.tag-primary {
  background: var(--primary-gold-alpha);
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
}

.tag-accent {
  background: rgba(220, 38, 38, 0.1);
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
}

.tag-success {
  background: rgba(5, 150, 105, 0.1);
  color: var(--accent-green);
  border: 1px solid var(--accent-green);
}

.tag-warning {
  background: rgba(234, 88, 12, 0.1);
  color: var(--accent-orange);
  border: 1px solid var(--accent-orange);
}

/* 产品网格保留 */

/* 搜索栏样式已移除，使用SearchBox组件 */

/* Banner样式 - 增加高度 */
.banner-section {
  margin: 20px 0;
}

/* 轮播图加载状态 */
.banner-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 220px;
  background: var(--bg-card);
  border-radius: 16px;
  color: var(--text-secondary);
  border: 1px solid rgba(218, 165, 32, 0.3);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 热门推荐加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
}

.loading-container .loading-spinner {
  width: 20px;
  height: 20px;
  margin-bottom: 8px;
}

/* 发行方信息样式 - 单独一行 */
.publisher-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.publisher-logo {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.publisher-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.publisher-name {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 暂无数据状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
  text-align: center;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.6;
}

.empty-state p {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

.banner-slider {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  height: 220px; /* 从160px增加到220px */
  border: 1px solid rgba(218, 165, 32, 0.3);
  touch-action: pan-y; /* 允许垂直滚动，但拦截水平滑动 */
  user-select: none; /* 防止选中文本 */
  transition: transform 0.2s ease-out; /* 添加滑动偏移过渡效果 */
}

.banner-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.5s ease;
  cursor: pointer;
  -webkit-user-drag: none; /* 防止图片拖拽 */
  -webkit-user-select: none; /* 防止选中 */
  pointer-events: none; /* 只有active能点 */
}

.banner-slide.active {
  opacity: 1;
  pointer-events: auto; /* 只有当前显示的那一张能点 */
}

.banner-slide::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(218, 165, 32, 0.1) 0%, transparent 70%);
  z-index: 0;
}

.banner-content {
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 40px 20px; /* 增加padding */
}



.banner-indicators {
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.banner-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s;
}

.banner-indicator.active {
  background: var(--primary-gold);
  width: 18px;
  border-radius: 3px;
}

/* 滑动提示样式 */
.swipe-hint {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  padding: 6px 10px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  opacity: 0.7;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 15;
}

.swipe-hint i {
  font-size: 10px;
  color: var(--primary-gold);
  animation: pulse 2s infinite;
}

.swipe-hint:hover {
  opacity: 1;
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

/* 移动端隐藏滑动提示，因为移动端用户会自然地尝试滑动 */
@media (max-width: 768px) {
  .swipe-hint {
    display: none;
  }
}

/* 章节样式 */
.section {
  margin: 30px 0;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-gold-light);
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: var(--primary-gold);
}

.section-more {
  color: var(--primary-gold);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.section-more:hover {
  color: var(--primary-gold-light);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 页面布局样式 */

.main-content {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding: 20px 0 80px 0;
  padding-bottom: 65px;
}

/* 响应式调整 */
@media (max-width: 380px) {
  .products-grid {
    gap: 12px;
  }
  
  .product-image {
    height: 140px;
  }
  
  .container {
    padding: 0 8px;
  }
  
  .product-info {
    padding: 8px;
    min-height: 80px;
  }
  
  .product-name {
    font-size: 13px;
    margin-bottom: 4px;
  }
  
  .banner-slider {
    height: 180px; /* 移动端稍微减少高度 */
  }
  
  .banner-content {
    padding: 30px 16px;
  }
}

/* 特殊banner样式 - 科幻风格 */
.special-banner {
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  position: relative;
}

.special-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.special-content {
  position: relative;
  z-index: 2;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 30px 40px !important;
  border: 1px solid rgba(232, 163, 23, 0.3);
}

.special-title {
  color: #e8a317 !important;
  font-size: 48px !important;
  font-weight: 900 !important;
  text-shadow: 0 0 20px rgba(232, 163, 23, 0.5);
  margin-bottom: 12px;
  letter-spacing: 2px;
}

.special-description {
  color: #00d4ff !important;
  font-size: 18px !important;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  margin-bottom: 20px;
  letter-spacing: 4px;
}

.btn-special {
  background: linear-gradient(45deg, #c8860d, #e8a317, #c8860d) !important;
  color: #0a0a0f !important;
  border: 2px solid #e8a317;
  padding: 12px 24px;
  font-weight: 700;
  font-size: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
  box-shadow: 0 0 20px rgba(232, 163, 23, 0.4);
  transition: all 0.3s ease;
}

.btn-special:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 25px rgba(232, 163, 23, 0.6);
  background: linear-gradient(45deg, #e8a317, #f4b942, #e8a317) !important;
  border-color: #f4b942;
}

@media (max-width: 380px) {
  .special-title {
    font-size: 36px !important;
  }
  
  .special-description {
    font-size: 16px !important;
    letter-spacing: 2px;
  }
  
  .special-content {
    padding: 20px 24px !important;
  }
}

/* 资产类型标签样式 - 显示在右侧 */
.asset-type-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
  transition: all 0.3s ease;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
  max-width: 65px; /* 限制最大宽度，为发售时间留出更多空间 */
}

.asset-type-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.asset-type-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, #7c88f0 0%, #8b5fbf 100%);
  border-color: rgba(255, 255, 255, 0.5);
}

.asset-type-tag:hover::before {
  left: 100%;
}

/* 资产页面样式兼容 - 新的资产卡片布局 */

/* 产品封面图片样式 */
.product-cover-image {
  width: 100%;
  height: auto;
  display: block;
  max-width: 100%;
}

.product-placeholder {
  color: var(--primary-gold);
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 20px;
  width: 100%;
}

/* 顶部信息条样式 - 包含发行时间和资产类型标签 */
.product-top-info {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  z-index: 3;
}

/* 发行时间标签样式 - 显示在左侧 */
.product-sale-time {
  background: rgba(0, 0, 0, 0.7);
  color: var(--neutral-100);
  font-size: 9px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 3px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  flex-shrink: 1;
  min-width: 0;
  max-width: calc(100% - 65px); /* 为右侧标签留出空间，增加发售时间显示空间 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-sale-time:hover {
  background: rgba(0, 0, 0, 0.8);
  border-color: var(--primary-gold);
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.product-sale-time i {
  flex-shrink: 0;
  font-size: 8px;
  color: var(--primary-gold);
}

/* 关键字标签容器样式 - 显示在图片左下角 */
.product-keywords {
  position: absolute;
  bottom: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  z-index: 2;
  max-height: 40px;
  overflow: hidden;
}

.keyword-tag {
  background: rgba(212, 165, 116, 0.9);
  color: white;
  font-size: 8px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword-more {
  background: rgba(0, 0, 0, 0.8);
  color: var(--neutral-200);
  font-size: 8px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 发行方信息样式 */
.issuer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.issuer-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.issuer-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.issuer-default-icon {
  color: white;
  font-size: 8px;
}

.issuer-details {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.issuer-label {
  font-size: 9px;
  color: var(--text-muted);
  font-weight: 400;
  line-height: 1.2;
}

.issuer-name {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.2;
}

/* 价格和限量信息布局 */
.price-and-limit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  gap: 6px;
  flex-shrink: 0;
}

.limited-quantity {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 10px;
  color: var(--text-secondary);
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.2) 0%, rgba(244, 162, 97, 0.15) 100%);
  padding: 4px 8px;
  border-radius: 10px;
  border: 1px solid var(--primary-gold);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  line-height: 1.2;
}

.limited-quantity::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.limited-quantity:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border-color: var(--primary-light);
}

.limited-quantity:hover::before {
  left: 100%;
}

.limited-quantity i {
  color: var(--primary-gold);
  font-size: 8px;
}

.limited-label {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quantity-number {
  color: var(--primary-gold);
  font-weight: 700;
  font-size: 11px;
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-light) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.quantity-unit {
  color: var(--text-secondary);
  font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 380px) {
  .product-top-info {
    gap: 6px;
  }
  
  .product-sale-time {
    font-size: 8px;
    padding: 2px 4px;
    max-width: calc(100% - 50px); /* 移动端为右侧标签留出更少空间，增加时间显示空间 */
  }
  
  .keyword-tag {
    font-size: 7px;
    padding: 1px 4px;
    max-width: 50px;
  }
  
  .keyword-more {
    font-size: 7px;
    padding: 1px 4px;
  }
  
  .issuer-info {
    padding: 4px 6px;
    gap: 6px;
  }
  
  .issuer-avatar {
    width: 18px;
    height: 18px;
  }
  
  .issuer-default-icon {
    font-size: 7px;
  }
  
  .issuer-label {
    font-size: 8px;
  }
  
  .issuer-name {
    font-size: 10px;
  }
  
  .limited-quantity {
    font-size: 9px;
    padding: 3px 6px;
    gap: 2px;
    border-radius: 8px;
  }
  
  .limited-quantity i {
    font-size: 7px;
  }
  
  .quantity-number {
    font-size: 10px;
  }
  
  .product-price {
    font-size: 15px;
    line-height: 1.1;
  }
  
  .price-and-limit {
    gap: 4px;
  }
  
  /* 资产类型标签移动端优化 */
  .asset-type-tag {
    font-size: 9px;
    padding: 3px 6px;
    border-radius: 10px;
    max-width: 50px; /* 移动端限制更小的宽度，为时间留出更多空间 */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .asset-type-tag:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
  }
}

/* Banner滑动动画 */
.slide-left-enter-active, .slide-right-enter-active {
  transition: all 0.5s cubic-bezier(0.4,0,0.2,1);
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;
}
.slide-left-leave-active, .slide-right-leave-active {
  transition: all 0.5s cubic-bezier(0.4,0,0.2,1);
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
}
.slide-left-enter-from {
  transform: translateX(100%);
  opacity: 0.7;
}
.slide-left-leave-to {
  transform: translateX(-100%);
  opacity: 0.7;
}
.slide-right-enter-from {
  transform: translateX(-100%);
  opacity: 0.7;
}
.slide-right-leave-to {
  transform: translateX(100%);
  opacity: 0.7;
}

</style>
