<template>
  <AppPageHeader :title="'系列详情'" @back="$router.back()" />

  <div class="series-detail-container" :style="{ paddingTop: '40px' }">


    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在加载系列详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button class="retry-btn" @click="fetchData">重试</button>
    </div>

    <!-- 系列详情内容 -->
    <div v-else-if="seriesDetail" class="series-content">
      <!-- 系列封面区域 -->
      <section class="series-cover-section">
        <div class="cover-image" v-if="seriesDetail.seriesCover">
          <img 
            :src="seriesDetail.seriesCover" 
            :alt="seriesDetail.seriesName"
            class="cover-img"
          >
        </div>
        <div v-else class="cover-placeholder">
          <i class="fas fa-folder-open"></i>
          <p>暂无封面图</p>
        </div>
        <div class="cover-overlay">
          <div class="series-info">
            <h1 class="series-title">{{ seriesDetail.seriesName }}</h1>
            <div v-if="seriesDetail.assetCount" class="series-stats">
              <span class="stats-item">
                <i class="fas fa-cube"></i>
                {{ seriesDetail.assetCount }} 个资产
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 系列介绍 -->
      <section v-if="seriesDetail.seriesDesc" class="series-description">
        <div class="section-header">
          <h3 class="section-title">系列介绍</h3>
          <div class="section-divider"></div>
        </div>
        <div class="description-content">
          {{ seriesDetail.seriesDesc }}
        </div>
      </section>

      <!-- 系列资产列表 -->
      <section class="series-assets-section">
        <div class="section-header">
          <h3 class="section-title">系列资产</h3>
          <div class="section-divider"></div>
        </div>

        <!-- 资产列表加载状态 -->
        <div v-if="assetsLoading" class="assets-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>正在加载资产列表...</p>
        </div>

        <!-- 资产列表 -->
        <div v-else-if="assetList.length > 0" class="products-grid">
          <AssetCard 
            v-for="(product, index) in assetList" 
            :key="product.assetId || index"
            :asset="product as any"
            :show-asset-type="true"
            :show-issuer-label="true"
            :asset-types-list="assetTypesList"
            @click="handleAssetClick"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <i class="fas fa-inbox"></i>
          <p>该系列暂无资产</p>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && !assetsLoading" class="load-more-container">
          <button class="load-more-btn" @click="loadMoreAssets">
            <i class="fas fa-plus"></i>
            加载更多
          </button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AssetCard from '@/components/AssetCard.vue'
import { SeriesAPI, type SeriesDetail } from '@/api'
import type { AssetItem } from '@/api/series'
import { DictionaryAPI } from '@/api'
import type { DictionaryItem } from '@/api'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 数据状态
const loading = ref(true)
const error = ref('')
const seriesDetail = ref<SeriesDetail | null>(null)

// 资产列表相关
const assetsLoading = ref(false)
const assetList = ref<AssetItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalAssets = ref(0)

// 资产类型字典数据
const assetTypesList = ref<DictionaryItem[]>([])
const isLoadingAssetTypes = ref(false)

// 计算属性
const seriesId = computed(() => route.params.seriesId as string)
const hasMore = computed(() => assetList.value.length < totalAssets.value)

// 工具方法（保持兼容性）
const formatPrice = (price?: number): string => {
  if (!price) return '0.00'
  return price.toFixed(2)
}

// 数据展示辅助函数
const getProductName = (product: AssetItem): string => {
  return product.assetName || '未命名资产'
}

const getProductImage = (product: AssetItem): string | undefined => {
  const image = product.assetCover
  if (image && (image.startsWith('http') || image.startsWith('/'))) {
    return image
  }
  return undefined
}

const getProductPrice = (product: AssetItem): string => {
  const price = product.issuePrice || 0
  if (typeof price === 'number') {
    return price.toFixed(2)
  }
  return String(price).replace(/[¥]/g, '') || '0.00'
}

const getProductSaleTime = (product: AssetItem): string => {
  return product.saleStartTime || product.createDate || '未知时间'
}

const getProductKeywords = (product: AssetItem): string[] => {
  if (product.assetKeywords) {
    return product.assetKeywords.split(',').map(k => k.trim()).filter(k => k)
  }
  return []
}

const getProductTag = (product: AssetItem): { text: string, type: string } | null => {
  // 根据状态代码生成标签
  if (product.status) {
    switch (product.status) {
      case 1:
        return { text: '在售', type: 'on-sale' }
      case 2:
        return { text: '售罄', type: 'sold-out' }
      case 3:
        return { text: '限量', type: 'limited' }
      default:
        return { text: `状态${product.status}`, type: 'default' }
    }
  }
  
  // 检查开售时间
  if (product.saleStartTime) {
    const saleTime = new Date(product.saleStartTime)
    const now = new Date()
    if (saleTime > now) {
      return { text: '预售', type: 'limited' }
    }
  }
  
  return null
}

const formatSaleTime = (timeStr: string): string => {
  try {
    const date = new Date(timeStr)
    
    // 格式化为年月日时分秒
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
  } catch (error) {
    // 如果解析失败，尝试直接返回原字符串的前19位（YYYY-MM-DD HH:mm:ss）
    if (timeStr.length >= 19) {
      return timeStr.slice(0, 19).replace('T', ' ')
    }
    return timeStr
  }
}

const getIssuerInfo = (product: AssetItem): { name: string, logo?: string, issuerName?: string, issuerLogo?: string } | null => {
  // 优先使用issuers字段获取发行方信息（从接口返回的list）
  if (product.issuers && Array.isArray(product.issuers) && product.issuers.length > 0) {
    // 取第一个发行方
    const firstIssuer = product.issuers[0]
    
    if (firstIssuer) {
      const name = firstIssuer.issuerName || '未知发行方'
      const logo = firstIssuer.issuerLogo
      
      return { 
        name, 
        logo,
        issuerName: name,
        issuerLogo: logo
      }
    }
  }
  
  // 兼容旧的单个发行方字段
  if (product.issuerName) {
    return {
      name: product.issuerName,
      logo: product.issuerLogo,
      issuerName: product.issuerName,
      issuerLogo: product.issuerLogo
    }
  }
  
  return null
}

// 解析关键字（保持兼容）
const getKeywords = (keywords?: string): string[] => {
  return getProductKeywords({ assetKeywords: keywords } as AssetItem)
}

// 获取资产类型字典数据
const loadAssetTypes = async () => {
  try {
    isLoadingAssetTypes.value = true
    console.log('📡 开始请求资产类型字典数据...')
    const response = await DictionaryAPI.getDictionary('dig_asset_type')
    
    console.log('📥 资产类型字典API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      assetTypesList.value = response.data
      console.log('✅ 资产类型字典数据加载成功:', assetTypesList.value.length, '条')
    } else {
      assetTypesList.value = []
      console.warn('⚠️ 资产类型字典接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取资产类型字典失败:', error)
    assetTypesList.value = []
  } finally {
    isLoadingAssetTypes.value = false
  }
}

// 根据资产类型值获取对应的汉字标签
const getAssetTypeLabel = (assetTypeValue: string): string => {
  if (!assetTypeValue || !assetTypesList.value.length) {
    return assetTypeValue || ''
  }
  
  const found = assetTypesList.value.find(item => item.value === assetTypeValue)
  return found ? found.label : assetTypeValue
}

// 获取系列详情
const fetchSeriesDetail = async () => {
  try {
    const response = await SeriesAPI.getSeriesDetail(seriesId.value)
    if (response && response.code === 200 && response.data) {
      seriesDetail.value = response.data
    } else {
      error.value = response?.msg || '获取系列详情失败'
    }
  } catch (err) {
    console.error('获取系列详情失败:', err)
    error.value = '网络错误，请稍后重试'
  }
}

// 获取系列资产列表
const fetchAssetList = async (page: number = 1, append: boolean = false) => {
  try {
    assetsLoading.value = true
    const response = await SeriesAPI.getSeriesAssetList({
      seriesId: seriesId.value,
      pageNum: page,
      pageSize: pageSize.value
    })
    
    if (response && response.code === 200 && response.rows) {
      const { rows, total } = response
      if (append) {
        assetList.value.push(...rows)
      } else {
        assetList.value = rows
      }
      
      totalAssets.value = total
      currentPage.value = page
    } else {
      console.error('获取资产列表失败:', response?.msg)
    }
  } catch (err) {
    console.error('获取资产列表失败:', err)
  } finally {
    assetsLoading.value = false
  }
}

// 加载更多资产
const loadMoreAssets = () => {
  if (!assetsLoading.value && hasMore.value) {
    fetchAssetList(currentPage.value + 1, true)
  }
}

// 跳转到资产详情页
const goToAssetDetail = (assetId: string) => {
  router.push(`/asset/${assetId}`)
}

// 处理资产卡片点击事件
const handleAssetClick = (asset: any) => {
  const assetId = asset.assetId || asset.id
  if (assetId) {
    router.push(`/asset/${assetId}`)
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await Promise.all([
      fetchSeriesDetail(),
      fetchAssetList(1, false),
      loadAssetTypes()
    ])
  } catch (err) {
    console.error('获取数据失败:', err)
    error.value = '获取数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.series-detail-container {
  background: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  color: #f8f6f0;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  min-height: 100vh;
}

/* 页面头部导航 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  position: sticky;
  top: 0;
  background: linear-gradient(180deg, rgba(26, 26, 26, 0.95) 0%, rgba(36, 36, 36, 0.9) 100%);
  backdrop-filter: blur(20px);
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.back-btn {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #f8f6f0;
  font-size: 18px;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background-color: rgba(212, 165, 116, 0.2);
  color: #d4a574;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #f8f6f0;
  margin: 0;
  text-align: center;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #e0ddd4;
}

.loading-container i, .error-container i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d4a574;
}

.retry-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
}

/* 系列封面区域 */
.series-cover-section {
  position: relative;
  height: 300px;
  margin-bottom: 24px;
  border-radius: 0 0 20px 20px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  position: relative;
}

.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: #242424;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #a39d8e;
  border: 2px dashed rgba(255, 255, 255, 0.1);
}

.cover-placeholder i {
  font-size: 48px;
  margin-bottom: 12px;
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 32px 20px 20px;
}

.series-title {
  font-size: 28px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.series-stats {
  display: flex;
  gap: 16px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #e8c49a;
  font-size: 14px;
  font-weight: 500;
}

/* 公共区域样式 */
.series-description,
.series-assets-section {
  padding: 0 20px;
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0 0 12px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.section-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #d4a574 0%, transparent 50%, #d4a574 100%);
  opacity: 0.3;
}

.description-content {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  border: 1px solid rgba(248, 246, 240, 0.1);
  padding: 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #e0ddd4;
}

/* 资产网格和卡片 */
.assets-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  color: #e0ddd4;
}

.assets-loading i {
  font-size: 32px;
  margin-bottom: 12px;
  color: #d4a574;
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin: 24px 0;
  padding-bottom: 65px;
}

.product-card {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.6);
  border-color: #d4a574;
}

.product-image {
  width: 100%;
  min-height: 120px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #242424, #2e2e2e);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d4a574;
  font-size: 13px;
}

.product-cover-image {
  width: 100%;
  height: auto;
  display: block;
  max-width: 100%;
}

.product-placeholder {
  color: #d4a574;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 20px;
  width: 100%;
}

.product-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  color: white;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.product-tag.on-sale {
  background: #90a955;
}

.product-tag.limited {
  background: #d4a574;
}

.product-tag.sold-out {
  background: #a39d8e;
}

.product-tag.default {
  background: #e63946;
}

/* 顶部信息容器：发行时间和资产类型同一行显示 */
.product-top-info {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  z-index: 2;
}

/* 发行时间标签样式 - 显示在图片左上角 */
.product-sale-time {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: #f8f6f0;
  font-size: 9px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
  min-width: 0;
}

.product-sale-time:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: #d4a574;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.product-sale-time i {
  font-size: 8px;
  color: #e8c49a;
  flex-shrink: 0;
}

/* 资产类型标签容器 - 显示在图片右上角 */
.product-asset-type {
  flex-shrink: 0;
}

/* 关键字标签样式 - 显示在图片左下角 */
.product-keywords {
  position: absolute;
  bottom: 8px;
  left: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: calc(100% - 16px);
  z-index: 3;
}

.keyword-tag {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: #e8c49a;
  font-size: 9px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid rgba(200, 134, 13, 0.3);
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.keyword-tag:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: #d4a574;
  transform: translateY(-1px);
}

.keyword-more {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: #a39d8e;
  font-size: 9px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.product-info {
  padding: 12px;
  flex: 0 0 auto;
}

.product-name {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #f8f6f0;
  line-height: 1.4;
}

/* 资产类型标签样式 - 在图片区域显示 */
.asset-type-tag {
  display: inline-block;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  font-size: 9px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(79, 172, 254, 0.4);
  box-shadow: 0 2px 4px rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.asset-type-tag:hover {
  background: linear-gradient(135deg, #3d9cee 0%, #00e2ee 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 172, 254, 0.4);
}

/* 发行方信息样式 */
.issuer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.issuer-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(200, 134, 13, 0.3);
  flex-shrink: 0;
  overflow: hidden;
}

.issuer-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.issuer-default-icon {
  color: #d4a574;
  font-size: 10px;
}

.issuer-details {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.issuer-label {
  font-size: 10px;
  color: #a39d8e;
  font-weight: 500;
  flex-shrink: 0;
}

.issuer-name {
  font-size: 11px;
  color: #e8c49a;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 价格和限量信息容器 */
.price-and-limit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  gap: 8px;
}

.product-price {
  font-size: 16px;
  font-weight: bold;
  color: #d4a574;
  flex-shrink: 0;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

/* 限量信息突出显示 */
.limited-quantity {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.15), rgba(200, 134, 13, 0.15));
  border: 1px solid #d4a574;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(200, 134, 13, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.limited-quantity::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.limited-quantity:hover {
  background: linear-gradient(135deg, rgba(200, 134, 13, 0.2), rgba(200, 134, 13, 0.25));
  border-color: #e8c49a;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(200, 134, 13, 0.3);
}

.limited-quantity i {
  color: #d4a574;
  font-size: 10px;
  margin-right: 2px;
}

.limited-label {
  color: #b8956a;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quantity-number {
  color: #d4a574;
  font-weight: 800;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quantity-unit {
  color: #b8956a;
  font-weight: 600;
}

/* 其他状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #a39d8e;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.load-more-btn {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border: 1px solid rgba(248, 246, 240, 0.1);
  color: #e0ddd4;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.load-more-btn:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(212, 165, 116, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px 12px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .back-btn {
    left: 12px;
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .back-btn:hover {
    transform: translateY(-50%);
    box-shadow: none;
  }
  
  .series-cover-section {
    height: 220px;
  }
  
  .series-title {
    font-size: 24px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding-bottom: 60px;
  }
  
  .product-card {
    border-radius: 10px;
  }
  
  .product-info {
    padding: 10px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .issuer-info {
    padding: 4px 6px;
    margin-bottom: 6px;
  }
  
  .issuer-label {
    font-size: 9px;
  }
  
  .issuer-name {
    font-size: 10px;
  }
  
  .product-price {
    font-size: 14px;
  }
  
  .limited-quantity {
    padding: 3px 6px;
    font-size: 10px;
  }
  
  .asset-type-tag {
    font-size: 9px;
    padding: 3px 6px;
  }
  
  /* 顶部信息容器平板端优化 */
  .product-top-info {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 14px 10px;
  }
  
  .page-title {
    font-size: 15px;
  }
  
  .back-btn {
    left: 10px;
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .back-btn:hover {
    transform: translateY(-50%);
    box-shadow: none;
  }
  
  .series-cover-section {
    height: 220px;
  }
  
  .cover-overlay {
    padding: 20px 16px 16px;
  }
  
  .series-title {
    font-size: 20px;
  }
  
  .series-description,
  .series-assets-section {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  /* 保持两列布局，但调整间距和大小 */
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding-bottom: 50px;
  }
  
  .product-card {
    border-radius: 8px;
  }
  
  .product-image {
    min-height: 100px;
    font-size: 11px;
  }
  
  .product-info {
    padding: 8px;
  }
  
  .product-name {
    font-size: 13px;
    margin-bottom: 6px;
  }
  
  .issuer-info {
    padding: 3px 5px;
    margin-bottom: 5px;
    gap: 6px;
  }
  
  .issuer-avatar {
    width: 16px;
    height: 16px;
  }
  
  .issuer-label {
    font-size: 8px;
  }
  
  .issuer-name {
    font-size: 9px;
  }
  
  .product-price {
    font-size: 13px;
  }
  
  .limited-quantity {
    padding: 2px 5px;
    font-size: 9px;
    gap: 3px;
  }
  
  .limited-quantity i {
    font-size: 8px;
  }
  
  .quantity-number {
    font-size: 10px;
  }
  
  /* 关键字标签移动端优化 */
  .keyword-tag {
    font-size: 8px;
    padding: 1px 4px;
  }
  
  .keyword-more {
    font-size: 8px;
    padding: 1px 4px;
  }
  
  /* 顶部信息容器移动端优化 */
  .product-top-info {
    gap: 6px;
  }
  
  /* 发行时间标签移动端优化 */
  .product-sale-time {
    font-size: 8px;
    padding: 2px 4px;
  }
  
  .product-sale-time i {
    font-size: 7px;
  }
  
  .asset-type-tag {
    font-size: 8px;
    padding: 2px 5px;
  }
}
</style> 