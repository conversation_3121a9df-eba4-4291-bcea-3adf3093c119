import request from './request'
import type { ApiResponse } from './types'

/**
 * 文件上传响应数据
 */
export interface UploadFileData {
  attachId: number
  attachmentObjectTypeCode: string
  fileName: string
  mimeType: string
  fileSize: string
  fileDesc: string
  createDate: string
  filePath: string
  fileUrl: string
  fileScalePath: string | null
  hashCode: string
  statusCd: string
  statusDate: string
  createStaff: number
  updateStaff: number | null
  updateDate: string | null
  remark: string | null
}

/**
 * 文件上传响应
 */
export interface UploadResponse extends ApiResponse {
  data: UploadFileData
}

/**
 * 文件上传参数
 */
export interface UploadParams {
  file: File
  attachmentObjectTypeCode: string
  fileDesc?: string
}

/**
 * 文件验证结果
 */
export interface FileValidationResult {
  valid: boolean
  error?: string
}

/**
 * 文件上传API服务
 */
export class UploadAPI {
  /**
   * 验证文件名长度（不超过30个字符）
   */
  static validateFileName(fileName: string): FileValidationResult {
    // 移除文件扩展名后检查长度
    const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '')
    
    if (nameWithoutExt.length > 30) {
      return {
        valid: false,
        error: `文件名过长，不能超过30个字符（当前：${nameWithoutExt.length}个字符）`
      }
    }
    
    return { valid: true }
  }

  /**
   * 验证文件类型（必须为图片）
   */
  static validateImageType(file: File): FileValidationResult {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp']
    
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: '文件格式不正确，请上传图片格式（JPG、PNG、GIF、BMP、WEBP）'
      }
    }
    
    return { valid: true }
  }

  /**
   * 验证文件大小（最大5MB）
   */
  static validateFileSize(file: File, maxSizeMB: number = 5): FileValidationResult {
    const maxSizeBytes = maxSizeMB * 1024 * 1024
    
    if (file.size > maxSizeBytes) {
      return {
        valid: false,
        error: `文件大小超出限制，最大支持${maxSizeMB}MB（当前：${(file.size / 1024 / 1024).toFixed(2)}MB）`
      }
    }
    
    return { valid: true }
  }

  /**
   * 综合验证文件
   */
  static validateFile(file: File): FileValidationResult {
    // 验证文件名长度
    const nameValidation = this.validateFileName(file.name)
    if (!nameValidation.valid) {
      return nameValidation
    }

    // 验证文件类型
    const typeValidation = this.validateImageType(file)
    if (!typeValidation.valid) {
      return typeValidation
    }

    // 验证文件大小
    const sizeValidation = this.validateFileSize(file)
    if (!sizeValidation.valid) {
      return sizeValidation
    }

    return { valid: true }
  }

  /**
   * 通用文件上传
   */
  static async uploadFile(params: UploadParams): Promise<UploadResponse> {
    try {
      // 创建FormData，按照后端 @RequestParam 的参数名称
      const formData = new FormData()
      formData.append('file', params.file)
      formData.append('attachmentObjectTypeCode', params.attachmentObjectTypeCode)
      
      // fileDesc 参数，后端有默认值
      if (params.fileDesc) {
        formData.append('fileDesc', params.fileDesc)
      } else {
        formData.append('fileDesc', '') // 显式传递空字符串
      }

      // 调用上传接口
      // 注意：FormData需要特殊处理，不能使用默认的JSON序列化
      const response = await request.post<UploadResponse>('/file/cosUpload', formData)

      return response
    } catch (error) {
      console.error('文件上传失败:', error)
      throw error
    }
  }

  /**
   * 创建图片预览URL
   */
  static createImagePreview(file: File): string {
    return URL.createObjectURL(file)
  }

  /**
   * 释放图片预览URL
   */
  static revokeImagePreview(url: string): void {
    URL.revokeObjectURL(url)
  }

  /**
   * 上传证件照片
   */
  static async uploadIdentifyFile(file: File, fileDesc?: string): Promise<UploadResponse> {
    return this.uploadFile({
      file,
      attachmentObjectTypeCode: 'identifyFile',
      fileDesc: fileDesc || '证件照片'
    })
  }
}

export default UploadAPI 