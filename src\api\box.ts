import { request } from './request'

export interface BlindBox {
  userBoxId: number
  userId: number
  boxId: number
  boxCode: string
  acquireType: string
  acquireRecordId: number
  isOpened: string
  openTime: string
  statusCd: string
  createDate: string
  updateDate: string
  boxName: string
  boxCover: string
}

export async function getUserBoxList(params: { pageNum: number; pageSize: number }) {
  return request.get('/box/box/getUserBoxList', params)
}

export async function openBox(boxCode: string) {
  return request.post('/box/box/openBox', { boxCode })
} 