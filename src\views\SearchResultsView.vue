<template>
  <AppPageHeader :title="'搜索结果'" @back="$router.back()" />

  <div class="search-results-container" :style="{ paddingTop: '30px' }">
    <!-- 顶部搜索栏 -->
    <!-- <section class="search-header">
      <div class="search-header-content">
        <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button>
        <div class="search-title">
          <h1>搜索结果</h1>
          <p v-if="searchKeyword" class="search-keyword">
            "{{ searchKeyword }}"
          </p>
        </div>
      </div>
    </section> -->

    <!-- 搜索框区域 -->
    <section class="search-section">
      <div class="search-container">
        <!-- <button class="back-btn" @click="goBack">
          <i class="fas fa-arrow-left"></i>
        </button> -->
        <input 
          v-model="searchKeyword"
          type="text" 
          class="search-input" 
          placeholder="搜索资产、系列、专区"
          @keypress="handleKeypress"
          @input="handleInput"
        >
        <button class="search-btn" @click="handleSearchClick" :disabled="searchLoading">
          <i v-if="searchLoading" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-search"></i>
        </button>
      </div>
    </section>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在搜索...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button class="retry-btn" @click="() => performSearch()">重试</button>
    </div>

    <!-- 搜索结果 -->
    <div v-else-if="searchResults" class="results-content">
      <!-- 结果统计 -->
      <div v-if="totalResults > 0" class="results-summary">
        <i class="fas fa-search"></i>
        共找到 <span class="highlight">{{ totalResults }}</span> 个结果
      </div>

      <!-- 资产结果 -->
      <section v-if="searchResults.assets && searchResults.assets.length > 0" class="result-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-cube"></i>
            资产 ({{ searchResults.assets.length }})
          </h3>
        </div>
        <div class="result-list">
          <div 
            v-for="asset in searchResults.assets" 
            :key="asset.assetId"
            class="result-item"
            @click="goToAsset(asset)"
          >
            <div class="result-image">
              <img 
                v-if="asset.assetCoverThumbnail || asset.assetImage" 
                :src="asset.assetCoverThumbnail || asset.assetImage" 
                :alt="asset.assetName"
                class="item-image"
              />
              <div v-else class="image-placeholder">
                <i class="fas fa-image"></i>
              </div>
            </div>
            <div class="result-info">
              <h4 class="result-name">{{ asset.assetName }}</h4>
              <div class="result-type">
                <i class="fas fa-cube"></i>
                <span>数字资产</span>
              </div>
            </div>
            <div class="result-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </div>
        </div>
      </section>

      <!-- 系列结果 -->
      <section v-if="searchResults.series && searchResults.series.length > 0" class="result-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-layer-group"></i>
            系列 ({{ searchResults.series.length }})
          </h3>
        </div>
        <div class="result-list">
          <div 
            v-for="series in searchResults.series" 
            :key="series.seriesId"
            class="result-item"
            @click="goToSeries(series)"
          >
            <div class="result-image">
              <img 
                v-if="series.seriesCover || series.seriesImage" 
                :src="series.seriesCover || series.seriesImage" 
                :alt="series.seriesName"
                class="item-image"
              />
              <div v-else class="image-placeholder">
                <i class="fas fa-layer-group"></i>
              </div>
            </div>
            <div class="result-info">
              <h4 class="result-name">{{ series.seriesName }}</h4>
              <div class="result-type">
                <i class="fas fa-layer-group"></i>
                <span>资产系列</span>
              </div>
            </div>
            <div class="result-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </div>
        </div>
      </section>

      <!-- 专区结果 -->
      <section v-if="searchResults.zones && searchResults.zones.length > 0" class="result-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="fas fa-map-marked-alt"></i>
            专区 ({{ searchResults.zones.length }})
          </h3>
        </div>
        <div class="result-list">
          <div 
            v-for="zone in searchResults.zones" 
            :key="zone.zoneId"
            class="result-item"
            @click="goToZone(zone)"
          >
            <div class="result-image">
              <img 
                v-if="zone.zoneCover || zone.zoneImage" 
                :src="zone.zoneCover || zone.zoneImage" 
                :alt="zone.zoneName"
                class="item-image"
              />
              <div v-else class="image-placeholder">
                <i class="fas fa-map-marked-alt"></i>
              </div>
            </div>
            <div class="result-info">
              <h4 class="result-name">{{ zone.zoneName }}</h4>
              <div class="result-type">
                <i class="fas fa-map-marked-alt"></i>
                <span>专题专区</span>
              </div>
            </div>
            <div class="result-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </div>
        </div>
      </section>

      <!-- 空结果状态 -->
      <div v-if="totalResults === 0" class="empty-state">
        <i class="fas fa-search"></i>
        <h3>未找到相关结果</h3>
        <p>尝试使用其他关键词搜索</p>
        <div class="search-suggestions">
          <h4>搜索建议：</h4>
          <ul>
            <li>检查拼写是否正确</li>
            <li>尝试使用更简短的关键词</li>
            <li>使用更通用的搜索词</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { SearchAPI, type DigSearchVo, type SearchData } from '@/api/search'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 数据状态
const loading = ref(false)
const error = ref('')
const searchKeyword = ref('')
const searchResults = ref<SearchData | null>(null)
const searchLoading = ref(false) // 搜索按钮加载状态

// 计算属性
const totalResults = computed(() => {
  if (!searchResults.value) return 0
  const assetsCount = searchResults.value.assets?.length || 0
  const seriesCount = searchResults.value.series?.length || 0
  const zonesCount = searchResults.value.zones?.length || 0
  return assetsCount + seriesCount + zonesCount
})

// 执行搜索
const performSearch = async (keyword?: string) => {
  const searchTerm = keyword || searchKeyword.value
  if (!searchTerm.trim()) {
    return
  }

  try {
    loading.value = true
    searchLoading.value = true
    error.value = ''
    
    console.log('🔍 开始搜索:', searchTerm)
    
    const searchParams: DigSearchVo = {
      searchValue: searchTerm.trim()
    }
    
    const response = await SearchAPI.search(searchParams)
    
    console.log('📥 搜索结果:', response)
    console.log('📥 搜索结果.data:', response?.data)
    
    if (response && response.code === 200 && response.data) {
      // 正确提取 response.data 中的数据
      searchResults.value = {
        assets: response.data.assets || [],
        series: response.data.series || [],
        zones: response.data.zones || []
      }
      console.log('✅ 解析后的搜索结果:', {
        assets: searchResults.value.assets?.length || 0,
        series: searchResults.value.series?.length || 0,
        zones: searchResults.value.zones?.length || 0
      })
    } else {
      error.value = response?.msg || '搜索失败'
      searchResults.value = null
    }
  } catch (err) {
    console.error('搜索失败:', err)
    error.value = '网络错误，请稍后重试'
    searchResults.value = null
  } finally {
    loading.value = false
    searchLoading.value = false
  }
}

// 处理搜索
const handleSearch = (keyword: string) => {
  searchKeyword.value = keyword
  performSearch(keyword)
  // 更新URL查询参数
  router.replace({
    query: { q: keyword }
  })
}

// 导航方法
const goBack = () => {
  router.back()
}

const goToAsset = (asset: any) => {
  const assetId = asset.assetId || asset.id
  if (assetId) {
    router.push(`/asset/${assetId}`)
  }
}

const goToSeries = (series: any) => {
  const seriesId = series.seriesId || series.id
  if (seriesId) {
    router.push(`/series/${seriesId}`)
  }
}

const goToZone = (zone: any) => {
  const zoneId = zone.zoneId || zone.id
  if (zoneId) {
    router.push(`/zone/${zoneId}`)
  }
}

// 监听路由查询参数变化
watch(() => route.query.q, (newKeyword) => {
  if (newKeyword && typeof newKeyword === 'string') {
    searchKeyword.value = newKeyword
    performSearch(newKeyword)
  }
})

// 生命周期
onMounted(() => {
  const keyword = route.query.q as string
  if (keyword) {
    searchKeyword.value = keyword
    performSearch(keyword)
  }
})

// 处理输入事件
const handleInput = () => {
  // 可以在这里添加实时搜索逻辑
}

// 处理键盘事件
const handleKeypress = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    handleSearchClick()
  }
}

// 处理搜索按钮点击
const handleSearchClick = () => {
  const keyword = searchKeyword.value.trim()
  if (keyword) {
    handleSearch(keyword)
  }
}
</script>

<style scoped>
.search-results-container {
  background: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  color: #f8f6f0;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  min-height: 100vh;
  padding-bottom: 60px;
  padding-left: 12px;
  padding-right: 12px;
}

/* 顶部区域 */
.search-header {
  padding: 20px;
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-bottom: 1px solid rgba(248, 246, 240, 0.1);
}

.search-header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  background: none;
  border: none;
  color: #d4a574;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(212, 165, 116, 0.1);
  transform: translateX(-2px);
}

.search-title h1 {
  font-size: 24px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0;
}

.search-keyword {
  font-size: 14px;
  color: #d4a574;
  margin: 4px 0 0 0;
  font-style: italic;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-container i, .error-container i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d4a574;
}

.retry-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
}

/* 结果内容 */
.results-content {
  padding: 0 20px;
}

.results-summary {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  border: 1px solid rgba(248, 246, 240, 0.1);
  padding: 16px;
  margin: 20px 0;
  text-align: center;
  font-size: 14px;
  color: #e0ddd4;
}

.results-summary i {
  color: #d4a574;
  margin-right: 8px;
}

.highlight {
  color: #d4a574;
  font-weight: 600;
}

/* 结果区域 */
.result-section {
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: #d4a574;
  font-size: 16px;
}

/* 结果列表 */
.result-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-item {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  border: 1px solid rgba(248, 246, 240, 0.1);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.result-item:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(212, 165, 116, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.result-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: #242424;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #a39d8e;
  font-size: 24px;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 16px;
  font-weight: 600;
  color: #f8f6f0;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.result-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #a39d8e;
}

.result-type i {
  color: #d4a574;
}

.result-arrow {
  color: #a39d8e;
  font-size: 14px;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.result-item:hover .result-arrow {
  color: #d4a574;
  transform: translateX(4px);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #a39d8e;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 24px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 20px;
  color: #e0ddd4;
  margin: 0 0 12px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 24px 0;
}

.search-suggestions {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  border: 1px solid rgba(248, 246, 240, 0.1);
  padding: 20px;
  max-width: 300px;
  margin: 0 auto;
  text-align: left;
}

.search-suggestions h4 {
  font-size: 14px;
  color: #d4a574;
  margin: 0 0 12px 0;
}

.search-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-suggestions li {
  font-size: 12px;
  color: #c4c0b1;
  margin-bottom: 6px;
  padding-left: 16px;
  position: relative;
}

.search-suggestions li::before {
  content: '•';
  color: #d4a574;
  position: absolute;
  left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    padding: 0 4px;
    margin: 16px 0;
  }
  
  .search-section .back-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
    margin-right: 2px;
  }
  
  .search-section .back-btn::after {
    right: -4px;
    height: 16px;
  }
  
  .search-input {
    padding: 10px 14px 10px 6px;
    font-size: 14px;
  }
  
  .search-btn {
    padding: 8px 14px;
    min-width: 40px;
  }
  
  .search-btn i {
    font-size: 14px;
  }
  
  .search-header {
    padding: 16px;
  }
  
  .search-title h1 {
    font-size: 20px;
  }
  
  .results-content {
    padding: 0 16px;
  }
  
  .result-item {
    padding: 12px;
    gap: 12px;
  }
  
  .result-image {
    width: 50px;
    height: 50px;
  }
  
  .result-name {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .search-section {
    padding: 0;
    margin: 16px 0;
  }
  
  .search-container {
    border-radius: 20px;
    padding: 4px 4px 4px 6px;
  }
  
  .search-section .back-btn {
    width: 32px;
    height: 32px;
    font-size: 12px;
    margin-right: 2px;
  }
  
  .search-section .back-btn::after {
    right: -3px;
    height: 14px;
  }
  
  .search-input {
    padding: 8px 12px 8px 4px;
    font-size: 13px;
  }
  
  .search-btn {
    padding: 6px 12px;
    border-radius: 16px;
    min-width: 36px;
  }
  
  .search-header {
    padding: 12px;
  }
  
  .search-header-content {
    gap: 12px;
  }
  
  .search-title h1 {
    font-size: 18px;
  }
  
  .results-content {
    padding: 0 12px;
  }
  
  .section-title {
    font-size: 16px;
  }
  
  .result-item {
    padding: 10px;
    gap: 10px;
  }
  
  .result-image {
    width: 40px;
    height: 40px;
  }
  
  .empty-state {
    padding: 40px 16px;
  }
  
  .empty-state i {
    font-size: 48px;
  }
  
  .empty-state h3 {
    font-size: 18px;
  }
}

/* 搜索区域布局 */
.search-section {
  margin: 20px 0;
  padding: 0 8px;
}

.search-container {
  display: flex;
  align-items: center;
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 24px;
  padding: 4px 4px 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  gap: 0;
}

.search-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #d4a574, transparent);
  opacity: 0.6;
}

.search-container:focus-within {
  border-color: rgba(212, 165, 116, 0.5);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
  transform: translateY(-1px);
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: #f8f6f0;
  padding: 12px 16px 12px 8px;
  font-size: 14px;
  outline: none;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: #c4c0b1;
  font-style: italic;
}

.search-input:focus {
  color: #f8f6f0;
}

.search-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  border: none;
  color: white;
  padding: 10px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  position: relative;
  overflow: hidden;
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.search-btn:hover::before {
  width: 100%;
  height: 100%;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
  background: linear-gradient(135deg, #e8c49a 0%, #d4a574 100%);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.search-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

.search-btn:disabled:hover {
  transform: none;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.search-btn i {
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.search-section .back-btn {
  background: none;
  border: none;
  color: #d4a574;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  flex-shrink: 0;
  margin-right: 4px;
  position: relative;
  transform: translateY(0%);
}

.search-section .back-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(212, 165, 116, 0.1);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.3s ease;
  transform: scale(0.8);
}

.search-section .back-btn:hover::before {
  opacity: 1;
  transform: scale(1);
}

.search-section .back-btn:hover {
  color: #e8c49a;
  transform: translateX(-2px);
}

.search-section .back-btn:active {
  transform: translateX(0) scale(0.95);
}

/* 添加分隔线 */
.search-section .back-btn::after {
  content: '';
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 20px;
  background: linear-gradient(to bottom, transparent, rgba(212, 165, 116, 0.3), transparent);
}
</style> 