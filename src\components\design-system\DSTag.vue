<template>
  <span :class="tagClasses">
    <slot></slot>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'accent' | 'success' | 'warning'
  size?: 'normal' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'normal'
})

const tagClasses = computed(() => [
  'tag',
  `tag-${props.type}`,
  props.size === 'large' && 'tag-large'
])
</script> 