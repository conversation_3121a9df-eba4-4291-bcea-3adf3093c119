<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>关于我们 - 凌云数资</title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            
            /* 主色系 - 基于太阳神鸟的金色演化 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 深邃科技蓝 */
            --secondary-blue: #1E3A8A;         
            --secondary-blue-light: #3B82F6;   
            --secondary-blue-dark: #1E40AF;    
            --secondary-blue-alpha: rgba(30, 58, 138, 0.1);
            
            /* 背景色系 - 现代深色调 */
            --bg-primary: #0F0F23;             
            --bg-secondary: #1A1B3A;           
            --bg-tertiary: #252759;            
            --bg-glass: rgba(37, 39, 89, 0.8); 
            --bg-card: rgba(37, 39, 89, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 27, 58, 0.9) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-blue: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: var(--neutral-100);
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-100);
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: var(--neutral-200);
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: var(--primary-gold);
            margin: 6px 0;
        }
        
        .text-primary {
            color: var(--primary-gold);
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .btn-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-gold);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: var(--primary-gold-light);
        }
        
        .hero-section {
            text-align: center;
            padding: 40px 0;
            background: var(--gradient-card);
            margin: 20px 0;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .platform-logo {
            width: 80px;
            height: 80px;
            background: var(--gradient-primary);
            border-radius: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            margin-bottom: 20px;
            box-shadow: var(--shadow-gold);
        }
        
        .platform-name {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-gold);
            margin-bottom: 8px;
        }
        
        .platform-slogan {
            font-size: 14px;
            color: var(--neutral-400);
            line-height: 1.5;
            max-width: 280px;
            margin: 0 auto;
        }
        
        .info-section {
            background: var(--gradient-card);
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--primary-gold);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .info-content {
            font-size: 14px;
            color: var(--neutral-200);
            line-height: 1.6;
        }
        
        .info-content p {
            margin-bottom: 12px;
        }
        
        .info-content ul {
            margin: 12px 0;
            padding-left: 20px;
        }
        
        .info-content li {
            margin-bottom: 6px;
            color: var(--neutral-300);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px 0;
        }
        
        .feature-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            transition: all 0.3s;
        }
        
        .feature-item:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-gold-alpha);
            transform: translateY(-2px);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: var(--gradient-primary);
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 12px;
        }
        
        .feature-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 6px;
        }
        
        .feature-desc {
            font-size: 12px;
            color: var(--neutral-400);
            line-height: 1.4;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            text-align: center;
            margin: 20px 0;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: var(--primary-gold);
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--neutral-400);
        }
        
        .contact-item {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .contact-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .contact-info {
            flex: 1;
        }
        
        .contact-label {
            font-size: 13px;
            color: var(--primary-gold);
            margin-bottom: 2px;
        }
        
        .contact-value {
            font-size: 15px;
            color: var(--neutral-100);
            font-weight: 500;
        }
        
        .team-member {
            background: var(--bg-tertiary);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .member-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: var(--gradient-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
        }
        
        .member-info {
            flex: 1;
        }
        
        .member-name {
            font-size: 15px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 2px;
        }
        
        .member-role {
            font-size: 13px;
            color: var(--primary-gold);
            margin-bottom: 4px;
        }
        
        .member-desc {
            font-size: 12px;
            color: var(--neutral-400);
            line-height: 1.3;
        }
        
        .version-info {
            background: var(--primary-gold-alpha);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
        }
        
        .version-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-gold);
            margin-bottom: 8px;
        }
        
        .version-details {
            font-size: 12px;
            color: var(--neutral-300);
            line-height: 1.4;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: var(--neutral-400);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">关于我们</h1>
                <div></div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 平台介绍 -->
            <section class="hero-section">
                <div class="platform-logo">
                    <i class="fas fa-mountain"></i>
                </div>
                <div class="platform-name">凌云数资</div>
                <div class="platform-slogan">
                    传承巴蜀文化，创新数字未来<br>
                    打造全国领先的文化数字资产平台
                </div>
            </section>

            <!-- 平台介绍 -->
            <section class="info-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    平台介绍
                </div>
                <div class="info-content">
                    <p>凌云数资是基于区块链技术的文化数字资产交易平台，专注于巴蜀文化的数字化传承与创新。</p>
                    <p>我们致力于将四川丰富的历史文化资源转化为数字资产，包括古蜀文明、三星堆文化、金沙遗址、蜀锦工艺等珍贵文化遗产。</p>
                    <p>通过现代科技手段，让更多人了解、收藏和传承巴蜀文化，推动文化产业数字化发展。</p>
                </div>
            </section>

            <!-- 平台特色 -->
            <section class="info-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    平台特色
                </div>
                <div class="feature-grid">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="feature-title">安全可靠</div>
                        <div class="feature-desc">区块链技术保障，资产安全有保证</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-palette"></i>
                        </div>
                        <div class="feature-title">文化传承</div>
                        <div class="feature-desc">专注巴蜀文化，传承历史文明</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="feature-title">用户至上</div>
                        <div class="feature-desc">优质服务，用户体验第一</div>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <div class="feature-title">技术创新</div>
                        <div class="feature-desc">前沿技术，引领行业发展</div>
                    </div>
                </div>
            </section>

            <!-- 平台数据 -->
            <section class="info-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    平台数据
                </div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">50万</div>
                        <div class="stat-label">注册用户</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1000+</div>
                        <div class="stat-label">数字藏品</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">10万</div>
                        <div class="stat-label">交易总量</div>
                    </div>
                </div>
            </section>

            <!-- 核心团队 -->
            <section class="info-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-user-friends"></i>
                    </div>
                    核心团队
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">张</div>
                    <div class="member-info">
                        <div class="member-name">张文华</div>
                        <div class="member-role">创始人 & CEO</div>
                        <div class="member-desc">20年文化产业经验，致力于传统文化数字化</div>
                    </div>
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">李</div>
                    <div class="member-info">
                        <div class="member-name">李技师</div>
                        <div class="member-role">CTO</div>
                        <div class="member-desc">区块链技术专家，前阿里巴巴资深架构师</div>
                    </div>
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">王</div>
                    <div class="member-info">
                        <div class="member-name">王运营</div>
                        <div class="member-role">COO</div>
                        <div class="member-desc">资深产品运营，专注用户体验优化</div>
                    </div>
                </div>
            </section>

            <!-- 联系我们 -->
            <section class="info-section">
                <div class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    联系我们
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="contact-info">
                        <div class="contact-label">客服热线</div>
                        <div class="contact-value">************</div>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="contact-info">
                        <div class="contact-label">邮箱地址</div>
                        <div class="contact-value"><EMAIL></div>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="contact-info">
                        <div class="contact-label">办公地址</div>
                        <div class="contact-value">成都市天府新区兴隆街道</div>
                    </div>
                </div>
                
                <div class="contact-item">
                    <div class="contact-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="contact-info">
                        <div class="contact-label">工作时间</div>
                        <div class="contact-value">周一至周日 9:00-21:00</div>
                    </div>
                </div>
            </section>

            <!-- 版本信息 -->
            <section class="version-info">
                <div class="version-title">版本信息</div>
                <div class="version-details">
                    当前版本：v2.1.0<br>
                    更新时间：2024-01-15<br>
                    ©2024 凌云数资 版权所有
                </div>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 添加点击效果
        document.querySelectorAll('.btn, .feature-item, .team-member, .contact-item').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 联系方式点击功能
        document.querySelectorAll('.contact-item').forEach(item => {
            item.addEventListener('click', function() {
                const label = this.querySelector('.contact-label').textContent;
                const value = this.querySelector('.contact-value').textContent;
                
                if (label === '客服热线') {
                    if (confirm(`是否拨打客服热线：${value}？`)) {
                        window.location.href = `tel:${value}`;
                    }
                } else if (label === '邮箱地址') {
                    if (confirm(`是否发送邮件至：${value}？`)) {
                        window.location.href = `mailto:${value}`;
                    }
                } else {
                    alert(`${label}：${value}`);
                }
            });
        });

        // 统计数据动画效果
        function animateStats() {
            const statValues = document.querySelectorAll('.stat-value');
            const targets = ['50万', '1000+', '10万'];
            
            statValues.forEach((value, index) => {
                value.textContent = '0';
                setTimeout(() => {
                    value.textContent = targets[index];
                }, index * 200);
            });
        }

        // 页面加载完成后执行动画
        window.addEventListener('load', () => {
            setTimeout(animateStats, 500);
        });
    </script>
</body>
</html> 
