<template>
  <div class="notification-container">
    <transition-group name="notification" tag="div">
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="['notification', notification.type]"
      >
        <span>{{ getIcon(notification.type) }}</span>
        <span>{{ notification.message }}</span>
        <button 
          class="notification-close"
          @click="removeNotification(notification.id)"
        >
          ✕
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import { notifications, useNotification } from '@/composables/useNotification'

const { removeNotification } = useNotification()

const getIcon = (type: string) => {
  const icons = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  }
  return icons[type as keyof typeof icons] || icons.info
}
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999999;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
}

.notification {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  margin-bottom: 12px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: var(--shadow-lg);
  backdrop-filter: blur(20px);
  pointer-events: auto;
  max-width: min(400px, calc(100vw - 40px));
  min-width: min(300px, calc(100vw - 40px));
  text-align: left;
  word-break: break-word;
  position: relative;
}

.notification.success {
  background: var(--accent-green);
  color: white;
  border-left: 4px solid #22c55e;
}

.notification.error {
  background: var(--accent-red);
  color: white;
  border-left: 4px solid #ef4444;
}

.notification.warning {
  background: var(--accent-orange);
  color: white;
}

.notification.info {
  background: var(--secondary-purple);
  color: white;
}

.notification-close {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-left: auto;
}

.notification-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 动画效果 */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.notification-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 确保在所有设备上都固定在屏幕视口右上角 */
@media screen and (max-width: 768px) {
  .notification-container {
    top: 10px;
    right: 10px;
    max-height: calc(100vh - 20px);
  }
  
  .notification {
    max-width: calc(100vw - 20px);
    min-width: calc(100vw - 20px);
    font-size: 13px;
    padding: 14px 16px;
  }
}

/* 防止页面缩放影响定位 */
@media screen and (max-width: 480px) {
  .notification-container {
    top: 5px;
    right: 5px;
  }
  
  .notification {
    max-width: calc(100vw - 10px);
    min-width: calc(100vw - 10px);
    padding: 12px 14px;
  }
}
</style> 