# AssetCard 通用资产卡片组件

## 概述

`AssetCard` 是一个通用的资产展示卡片组件，用于统一首页、系列页面、展馆页面等所有需要展示数字资产的地方。该组件整合了所有资产展示的样式和功能，减少重复代码，提高维护性。

## 功能特性

- **统一样式**：深色金黄主题配色，符合项目整体设计风格
- **灵活配置**：支持多种展示配置选项
- **兼容多种数据格式**：自动适配不同的API数据结构
- **完整信息展示**：
  - 资产封面图
  - 状态标签（在售、限量、售罄等）
  - 发行时间和资产类型（可选）
  - 关键字标签
  - 资产名称
  - 发行方信息
  - 价格和限量信息
- **响应式设计**：适配桌面端、平板端、移动端
- **交互效果**：悬停动画、闪光效果等

## 使用方法

### 基础使用

```vue
<template>
  <AssetCard 
    :asset="assetData"
    @click="handleAssetClick"
  />
</template>

<script setup>
import AssetCard from '@/components/AssetCard.vue'

const assetData = {
  assetId: '123',
  assetName: '金沙太阳神鸟',
  assetCover: '/images/cover.jpg',
  issuePrice: 299.00,
  saleStartTime: '2024-12-31 10:00:00',
  assetKeywords: '限量发行,稀有,文物复刻',
  issuers: [
    {
      issuerName: '金沙博物馆',
      issuerLogo: '/images/logo.png'
    }
  ],
  issueQuantity: 1000,
  isOnSale: true
}

const handleAssetClick = (asset) => {
  // 处理点击事件
  console.log('点击了资产:', asset)
}
</script>
```

### 配置选项

```vue
<AssetCard 
  :asset="assetData"
  :show-asset-type="true"
  :show-issuer-label="false"
  :asset-types-list="assetTypesList"
  @click="handleAssetClick"
/>
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `asset` | `AssetData` | - | **必需**，资产数据对象 |
| `showAssetType` | `boolean` | `false` | 是否显示资产类型标签 |
| `showIssuerLabel` | `boolean` | `true` | 是否显示"发行方:"标签 |
| `assetTypesList` | `Array` | `[]` | 资产类型字典列表，用于类型转换 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `click` | `asset: AssetData` | 点击卡片时触发，传入资产数据 |

## AssetData 数据结构

组件支持以下数据字段，会自动适配不同的API数据格式：

### 基础信息
- `id` / `assetId`: 资产ID
- `assetName` / `name`: 资产名称

### 图片信息
- `assetCover` / `assetImage` / `image`: 封面图片

### 价格信息
- `issuePrice` / `assetPrice` / `price`: 发行价格

### 时间信息
- `saleStartTime` / `saleTime` / `createTime` / `createDate`: 发行时间

### 数量信息
- `issueQuantity` / `totalQuantity`: 发行数量

### 关键字信息
- `assetKeywords`: 字符串格式，逗号分隔
- `keywords`: 数组格式

### 状态信息
- `status`: 数字状态码（1:在售, 2:售罄, 3:限量）
- `statusCd`: 字符串状态码（'ON_SALE', 'SOLD_OUT', 'LIMITED'）
- `isOnSale`: 布尔值，是否在售
- `isLimited`: 布尔值，是否限量

### 类型信息
- `assetType`: 资产类型代码

### 发行方信息
- `issuers`: 数组格式的发行方列表
- `issuerName` / `publisherName`: 单个发行方名称
- `issuerLogo`: 单个发行方Logo

## 使用场景

### 1. 首页热门推荐

```vue
<AssetCard 
  v-for="product in hotProducts" 
  :key="product.id"
  :asset="product"
  :show-asset-type="true"
  :show-issuer-label="false"
  :asset-types-list="assetTypesList"
  @click="handleProductClick"
/>
```

### 2. 系列页面资产列表

```vue
<AssetCard 
  v-for="product in assetList" 
  :key="product.assetId"
  :asset="product"
  :show-asset-type="true"
  :show-issuer-label="true"
  :asset-types-list="assetTypesList"
  @click="handleAssetClick"
/>
```

### 3. 展馆页面资产展示

```vue
<AssetCard 
  v-for="product in exhibitions" 
  :key="product.id"
  :asset="product"
  :show-asset-type="false"
  :show-issuer-label="true"
  @click="handleProductClick"
/>
```

## 样式自定义

组件使用CSS变量进行样式定义，可以通过修改根级别的CSS变量来自定义主题：

```css
:root {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  /* ... 更多变量 */
}
```

## 注意事项

1. **数据兼容性**：组件会自动处理不同的数据字段名称，但建议使用标准的字段名称
2. **图片错误处理**：组件内置了图片加载失败的处理逻辑
3. **类型安全**：使用TypeScript时，建议传入正确的类型定义
4. **性能优化**：大量资产展示时，建议使用虚拟滚动等优化技术

## 维护说明

- 样式修改请直接在 `AssetCard.vue` 组件中进行
- 新增字段支持请更新 `AssetData` 接口定义
- 跨页面的功能变更需要考虑对所有使用场景的影响 