# 支付页面购买份数显示修复

## 问题描述
支付页面 `PaymentView.vue` 未能按照购买确认页面 `PurchaseConfirmView.vue` 选择的购买份数进行正确显示。

## 问题分析

### 原始问题
1. **PurchaseConfirmView.vue** 跳转到支付页面时只传递了：
   - `assetName`: 商品名称
   - `assetPrice`: 商品单价（不是总价）
   - `assetCover`: 商品封面
   - `from`: 来源页面标记

2. **PaymentView.vue** 接收到的是单价，但显示为"支付金额"，误导用户

3. **缺少信息**：
   - 购买数量 `purchaseQuantity`
   - 总金额（单价 × 数量）

## 修复方案

### 1. 修改 PurchaseConfirmView.vue 参数传递

**修改位置**: `src/views/PurchaseConfirmView.vue` 第456-472行

**修改内容**:
```typescript
// 跳转到支付页面，传递必要的参数
setTimeout(() => {
  router.push({
    name: 'payment',
    params: {
      orderNo: orderNo
    },
    query: {
      assetName: asset.value?.assetName || '',
      assetPrice: asset.value?.issuePrice?.toString() || '0',
      assetCover: asset.value?.assetCover || '',
      purchaseQuantity: purchaseQuantity.value.toString(),  // 新增：购买数量
      totalAmount: totalPrice.value.toString(),             // 新增：总金额
      from: 'purchase-confirm'
    }
  })
}, 800)
```

### 2. 修改 PaymentView.vue 接收逻辑

**修改位置**: `src/views/PaymentView.vue` 第21-27行

**修改内容**:
```typescript
// 从路由参数获取数据
const orderNo = computed(() => route.params.orderNo as string)
const assetName = computed(() => route.query.assetName as string)
const assetPrice = computed(() => route.query.assetPrice as string)
const assetCover = computed(() => route.query.assetCover as string)
const purchaseQuantity = computed(() => parseInt(route.query.purchaseQuantity as string) || 1)  // 新增
const totalAmount = computed(() => route.query.totalAmount as string)                           // 新增
```

### 3. 添加金额计算函数

**修改位置**: `src/views/PaymentView.vue` 第59-68行

**新增内容**:
```typescript
// 获取显示的支付金额（优先使用总金额，否则使用单价）
const getDisplayAmount = (): string => {
  if (totalAmount.value && totalAmount.value !== '0') {
    return formatPrice(totalAmount.value)
  }
  // 如果没有总金额，使用单价 × 数量计算
  const unitPrice = parseFloat(assetPrice.value || '0')
  const quantity = purchaseQuantity.value
  return formatPrice(unitPrice * quantity)
}
```

### 4. 更新UI显示

**修改位置**: `src/views/PaymentView.vue` 模板部分

**新增显示内容**:
- 购买数量显示
- 单价显示
- 正确的总金额显示

**模板结构**:
```vue
<div class="asset-details">
  <h2 class="asset-name">{{ assetName || '数字资产' }}</h2>
  <div class="asset-info-row">
    <div class="asset-quantity">
      <span class="quantity-label">购买数量</span>
      <span class="quantity-value">{{ purchaseQuantity }}份</span>
    </div>
    <div class="asset-unit-price">
      <span class="unit-price-label">单价</span>
      <span class="unit-price-value">¥{{ formatPrice(assetPrice) }}</span>
    </div>
  </div>
  <div class="asset-price">
    <span class="price-label">支付金额</span>
    <span class="price-value">¥{{ getDisplayAmount() }}</span>
  </div>
</div>
```

## 修复效果

### 修复前
- 只显示商品名称和单价
- 用户无法确认购买数量
- 支付金额显示错误（显示单价而非总价）

### 修复后
- 显示商品名称、购买数量、单价
- 正确显示总金额（单价 × 数量）
- 用户可以清楚确认购买信息
- 底部支付按钮显示正确的总金额

## 测试验证

### 测试步骤
1. 在购买确认页面选择不同的购买数量（如3份）
2. 点击"确认购买"创建订单
3. 跳转到支付页面
4. 验证显示信息：
   - 购买数量：3份
   - 单价：商品原价
   - 支付金额：单价 × 3

### 预期结果
- 购买数量正确显示
- 单价正确显示
- 总金额 = 单价 × 购买数量
- 底部支付按钮显示的金额与商品信息区域一致

## 兼容性处理

1. **向后兼容**: 如果没有传递 `totalAmount`，会自动计算 `单价 × 数量`
2. **默认值处理**: 如果没有传递 `purchaseQuantity`，默认为1
3. **错误处理**: 所有数值都有默认值，避免显示异常

## 响应式设计

添加了完整的响应式CSS样式：
- 大屏幕：购买数量和单价并排显示
- 小屏幕：适当调整字体大小和间距
- 超小屏幕：购买数量和单价垂直排列

这样修复后，用户在支付页面可以清楚地看到自己选择的购买数量和对应的总金额，避免了之前的混淆。
