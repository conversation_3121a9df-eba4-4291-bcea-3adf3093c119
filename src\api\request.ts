import type { ApiResponse } from './types'
import { useNotification } from '@/composables/useNotification'

/**
 * API基础URL - 从环境变量读取
 */
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://ts.sccdex.com/api'
/**
 * API基础URL
 */
// const BASE_URL = 'https://www.sccdex.com/api' // 生产环境
// const BASE_URL = 'https://ts.sccdex.com/api' // 预生产环境
// const BASE_URL = 'http://127.0.0.1:9020' // 开发环境
/**
 * HTTP请求配置
 */
interface RequestConfig extends RequestInit {
  baseURL?: string
  timeout?: number
  token?: string
}

/**
 * 请求错误类
 */
export class RequestError extends Error {
  code: number
  status?: number

  constructor(message: string, code: number, status?: number) {
    super(message)
    this.name = 'RequestError'
    this.code = code
    this.status = status
  }
}

/**
 * HTTP请求类
 */
class Request {
  private baseURL: string
  private timeout: number
  private notification = useNotification()

  constructor(baseURL: string = BASE_URL, timeout: number = 30000) {
    this.baseURL = baseURL
    this.timeout = timeout
  }

  /**
   * 获取完整URL
   */
  private getFullUrl(url: string, baseURL?: string): string {
    const base = baseURL || this.baseURL
    if (url.startsWith('http')) {
      return url
    }
    return `${base}${url.startsWith('/') ? url : '/' + url}`
  }

  /**
   * 获取Authorization头
   */
  private getAuthHeader(): Record<string, string> {
    const token = localStorage.getItem('token')
    return token ? { 'Authorization': `Bearer ${token}` } : {}
  }

  /**
   * 处理401未授权状态
   */
  private handle401Error() {

    // 记录当前页面路径（除了登录和注册页面）
    const currentPath = window.location.pathname
    if (currentPath !== '/login' && currentPath !== '/register') {
      localStorage.setItem('redirectPath', currentPath)
    }

    // 清除用户数据
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    // 延迟跳转，确保用户看到提示
    setTimeout(() => {
      // 动态导入router以避免循环依赖
      import('@/router').then(({ default: router }) => {
        router.push('/login')
      })
    }, 1000)
  }

  /**
   * 处理响应
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      let errorMessage = '网络请求失败'
      let errorCode = response.status

      try {
        const errorData = await response.json()
        errorMessage = errorData.msg || errorData.message || errorMessage
        // 新增：如果后端返回的JSON里有code=401，也处理
        if (errorData.code === 401) {
          this.handle401Error()
        }
      } catch {
        errorMessage = `HTTP ${response.status}: ${response.statusText}`
      }

      // 新增：如果HTTP状态码就是401，也处理
      if (response.status === 401) {
        this.handle401Error()
      }

      throw new RequestError(errorMessage, errorCode, response.status)
    }

    const data = await response.json()

    // 检查业务错误码
    if (data.code === 401) {
      // 处理401未授权错误
      this.handle401Error()
      throw new RequestError(data.msg || '登录已过期，请重新登录', data.code)
    }

    if (data.code !== 200) {
      throw new RequestError(data.msg || '请求失败', data.code)
    }

    return data
  }

  /**
   * 发送HTTP请求
   */
  private async request<T>(url: string, config: RequestConfig = {}): Promise<T> {
    const {
      baseURL,
      timeout = this.timeout,
      token,
      headers = {},
      ...restConfig
    } = config

    const fullUrl = this.getFullUrl(url, baseURL)

    // 构建请求头
    const isFormData = restConfig.body instanceof FormData

    const defaultHeaders: Record<string, string> = {
      'Accept': 'application/json, text/plain, */*'
    }

    // 对于非FormData请求，添加默认的Content-Type
    if (!isFormData) {
      defaultHeaders['Content-Type'] = 'application/json'
    }

    const requestHeaders: Record<string, string> = {
      ...defaultHeaders,
      ...this.getAuthHeader(),
      ...(headers as Record<string, string>)
    }

    // 如果传入了token参数，优先使用
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(fullUrl, {
        ...restConfig,
        headers: requestHeaders,
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      return await this.handleResponse<T>(response)
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof RequestError) {
        throw error
      }

      if ((error as Error).name === 'AbortError') {
        throw new RequestError('请求超时', -1)
      }

      throw new RequestError('网络连接失败', -1)
    }
  }

  /**
   * GET请求
   */
  async get<T>(url: string, params?: Record<string, any>, config?: RequestConfig): Promise<T> {
    let fullUrl = url

    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      fullUrl += `?${searchParams.toString()}`
    }

    return this.request<T>(fullUrl, {
      method: 'GET',
      ...config
    })
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    // 检查是否为FormData，如果是则不进行JSON序列化
    const isFormData = data instanceof FormData

    const requestConfig: RequestConfig = {
      method: 'POST',
      body: isFormData ? data : (data ? JSON.stringify(data) : undefined),
      ...config
    }

    // 如果是FormData，需要移除Content-Type让浏览器自动设置
    if (isFormData) {
      requestConfig.headers = {
        ...((config?.headers as Record<string, string>) || {})
      }
      // 不设置Content-Type，让浏览器自动设置multipart/form-data
    }

    return this.request<T>(url, requestConfig)
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    })
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string, config?: RequestConfig): Promise<T> {
    return this.request<T>(url, {
      method: 'DELETE',
      ...config
    })
  }
}

// 创建请求实例
export const request = new Request()

// 导出请求方法
export default request
