<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>资产详情 - 四川省数字资产发行平�?/title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            
            /* 主色系 - 基于太阳神鸟的金色演化 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 深邃科技蓝 */
            --secondary-blue: #1E3A8A;         
            --secondary-blue-light: #3B82F6;   
            --secondary-blue-dark: #1E40AF;    
            --secondary-blue-alpha: rgba(30, 58, 138, 0.1);
            
            /* 背景色系 - 现代深色调 */
            --bg-primary: #0F0F23;             
            --bg-secondary: #1A1B3A;           
            --bg-tertiary: #252759;            
            --bg-glass: rgba(37, 39, 89, 0.8); 
            --bg-card: rgba(37, 39, 89, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 27, 58, 0.9) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-blue: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            min-width: 120px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4dabf7, #1890ff);
            color: white;
            box-shadow: 0 3px 10px rgba(77, 171, 247, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 171, 247, 0.6);
            background: linear-gradient(135deg, #69c0ff, #40a9ff);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #b22222, #8b1a1a);
            color: #f8f0e0;
            box-shadow: 0 3px 10px rgba(178, 34, 34, 0.4);
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(178, 34, 34, 0.6);
        }
        
        .btn-full {
            width: 100%;
            padding: 16px 24px;
            margin: 8px 0;
        }
        
        .tag {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .tag-accent {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .tag-success {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .tag-warning {
            background: rgba(205, 133, 63, 0.2);
            color: #cd853f;
            border: 1px solid rgba(205, 133, 63, 0.4);
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn, .share-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
            padding: 8px;
        }
        
        .back-btn:hover, .share-btn:hover {
            color: #f8f0e0;
        }
        
        .asset-image {
            width: 100%;
            height: 280px;
            background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
            border-radius: 16px;
            margin: 16px 0;
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #daa520;
            font-size: 16px;
            font-weight: 600;
        }
        
        .asset-badge {
            position: absolute;
            top: 12px;
            left: 12px;
            background: #b22222;
            color: #f8f0e0;
            font-size: 12px;
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 600;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
        
        .asset-badge.zone-badge {
            background: #daa520;
            color: #2a1616;
        }
        
        .asset-info {
            padding: 16px;
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 16px;
            margin: 16px 0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .asset-title {
            font-size: 20px;
            font-weight: bold;
            color: #f8f0e0;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .asset-price {
            font-size: 24px;
            font-weight: bold;
            color: #b22222;
            margin: 12px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .original-price {
            font-size: 16px;
            color: rgba(240, 224, 208, 0.6);
            text-decoration: line-through;
        }
        
        .tags-container {
            margin: 12px 0;
        }
        
        .sale-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin: 16px 0;
            padding: 16px;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 12px;
            border: 1px solid rgba(218, 165, 32, 0.2);
        }
        
        .sale-item {
            text-align: center;
        }
        
        .sale-label {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
            margin-bottom: 4px;
        }
        
        .sale-value {
            font-size: 16px;
            font-weight: bold;
            color: #daa520;
        }
        
        .countdown-timer {
            background: linear-gradient(135deg, rgba(178, 34, 34, 0.2), rgba(218, 165, 32, 0.2));
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            border: 1px solid rgba(178, 34, 34, 0.4);
            text-align: center;
        }
        
        .countdown-title {
            font-size: 14px;
            color: #b22222;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .countdown-time {
            display: flex;
            justify-content: center;
            gap: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        
        .time-unit {
            background: rgba(178, 34, 34, 0.8);
            color: #f8f0e0;
            padding: 8px 12px;
            border-radius: 8px;
            text-align: center;
            min-width: 50px;
        }
        
        .time-number {
            font-size: 18px;
            font-weight: bold;
            display: block;
        }
        
        .time-label {
            font-size: 10px;
            margin-top: 2px;
        }
        
        .asset-details {
            margin: 20px 0;
        }
        
        .detail-section {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            margin: 12px 0;
            border: 1px solid rgba(218, 165, 32, 0.3);
            overflow: hidden;
        }
        
        .detail-header {
            padding: 16px;
            background: rgba(218, 165, 32, 0.1);
            border-bottom: 1px solid rgba(218, 165, 32, 0.2);
            font-size: 16px;
            font-weight: 600;
            color: #daa520;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .detail-content {
            padding: 16px;
            display: none;
        }
        
        .detail-content.active {
            display: block;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(218, 165, 32, 0.1);
        }
        
        .detail-item:last-child {
            border-bottom: none;
        }
        
        .detail-label {
            color: rgba(240, 224, 208, 0.7);
            font-size: 14px;
        }
        
        .detail-value {
            color: #f0e0d0;
            font-size: 14px;
            font-weight: 500;
        }
        
        .purchase-section {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 16px;
            z-index: 50;
        }
        
        .purchase-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .favorite-btn {
            background: rgba(60, 40, 30, 0.7);
            border: 1px solid rgba(218, 165, 32, 0.3);
            color: #daa520;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            min-width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .favorite-btn.favorited {
            background: rgba(178, 34, 34, 0.2);
            border-color: #b22222;
            color: #b22222;
        }
        
        .favorite-btn:hover {
            transform: scale(1.1);
        }
        
        .purchase-btn {
            flex: 1;
        }
        
        .main-content {
            padding-bottom: 100px;
        }
        
        .share-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 100;
            display: none;
            align-items: flex-end;
        }
        
        .share-content {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 20px 20px 0 0;
            padding: 24px;
            width: 100%;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .share-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 20px;
        }
        
        .share-options {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.3s;
        }
        
        .share-option:hover {
            transform: scale(1.1);
        }
        
        .share-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-bottom: 8px;
        }
        
        .wechat { background: #00C851; color: white; }
        .weibo { background: #FF6900; color: white; }
        .qq { background: #4FC3F7; color: white; }
        .link { background: #daa520; color: #2a1616; }
        
        .share-label {
            font-size: 12px;
            color: #f0e0d0;
        }
        
        .close-btn {
            width: 100%;
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px;
            border-radius: 24px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .payment-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            z-index: 100;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .payment-content {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 16px;
            padding: 24px;
            width: 90%;
            max-width: 360px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .payment-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 20px;
        }
        
        .payment-methods {
            margin: 20px 0;
        }
        
        .payment-method {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 8px 0;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid rgba(218, 165, 32, 0.2);
            transition: all 0.3s;
        }
        
        .payment-method:hover {
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
        }
        
        .payment-method.selected {
            border-color: #daa520;
            background: rgba(218, 165, 32, 0.1);
        }
        
        .payment-icon {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .unionpay { background: #1E88E5; color: white; }
        .alipay { background: #00A6FB; color: white; }
        .wechatpay { background: #00C851; color: white; }
        
        .payment-name {
            flex: 1;
            font-size: 14px;
            color: #f0e0d0;
        }
        
        .payment-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">资产详情</h1>
                <button class="share-btn" onclick="showShareModal()">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 资产图片 -->
            <div class="asset-image">
                <div>金沙太阳神鸟金饰</div>
                <div class="asset-badge">限量发行</div>
                <div class="asset-badge zone-badge" style="top: 12px; right: 12px; left: auto;">古蜀文明</div>
            </div>

            <!-- 资产基本信息 -->
            <div class="asset-info">
                <h1 class="asset-title">金沙太阳神鸟金饰数字藏品</h1>
                
                <div class="tags-container">
                    <span class="tag tag-primary">限量�?/span>
                    <span class="tag tag-accent">热门</span>
                    <span class="tag tag-success">官方认证</span>
                    <span class="tag tag-warning">收藏价�?/span>
                </div>
                
                <div class="asset-price">
                    �?99.00
                    <span class="original-price">�?99.00</span>
                </div>
                
                <!-- 销售信�?-->
                <div class="sale-info">
                    <div class="sale-item">
                        <div class="sale-label">总发行量</div>
                        <div class="sale-value">500�?/div>
                    </div>
                    <div class="sale-item">
                        <div class="sale-label">已售�?/div>
                        <div class="sale-value">347�?/div>
                    </div>
                    <div class="sale-item">
                        <div class="sale-label">剩余数量</div>
                        <div class="sale-value">153�?/div>
                    </div>
                    <div class="sale-item">
                        <div class="sale-label">销售进�?/div>
                        <div class="sale-value">69.4%</div>
                    </div>
                </div>
            </div>

            <!-- 倒计�?-->
            <div class="countdown-timer">
                <div class="countdown-title">距离销售结束还�?/div>
                <div class="countdown-time" id="countdown">
                    <div class="time-unit">
                        <span class="time-number">02</span>
                        <span class="time-label">�?/span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number">15</span>
                        <span class="time-label">�?/span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number">34</span>
                        <span class="time-label">�?/span>
                    </div>
                    <div class="time-unit">
                        <span class="time-number">26</span>
                        <span class="time-label">�?/span>
                    </div>
                </div>
            </div>

            <!-- 详细信息 -->
            <div class="asset-details">
                <!-- 藏品介绍 -->
                <div class="detail-section">
                    <div class="detail-header" onclick="toggleDetail(this)">
                        <span><i class="fas fa-info-circle"></i> 藏品介绍</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="detail-content active">
                        <p>太阳神鸟金饰，出土于金沙遗址，是古蜀文明的杰出代表作品。该金饰呈圆形，外径12.5厘米，内�?.29厘米，厚�?.02厘米，重�?0克。图案分内外两层，内层为等距分布的十二条旋转的齿状光芒；外层图案由四只鸟构成，鸟首相接，朝同一方向飞行�?/p>
                        <br>
                        <p>这件文物体现了古蜀先民对太阳和鸟的崇拜，反映了人与自然和谐统一的思想�?005年，太阳神鸟图案被确定为中国文化遗产标志�?/p>
                    </div>
                </div>

                <!-- 发行信息 -->
                <div class="detail-section">
                    <div class="detail-header" onclick="toggleDetail(this)">
                        <span><i class="fas fa-certificate"></i> 发行信息</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">发行�?/span>
                            <span class="detail-value">四川省博物院</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">发行时间</span>
                            <span class="detail-value">2024�?1�?5�?/span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">发行总量</span>
                            <span class="detail-value">500�?/span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">藏品编号</span>
                            <span class="detail-value">#SC-SX-001</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">区块�?/span>
                            <span class="detail-value">蚂蚁�?/span>
                        </div>
                    </div>
                </div>

                <!-- 技术参�?-->
                <div class="detail-section">
                    <div class="detail-header" onclick="toggleDetail(this)">
                        <span><i class="fas fa-cog"></i> 技术参�?/span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="detail-content">
                        <div class="detail-item">
                            <span class="detail-label">文件格式</span>
                            <span class="detail-value">PNG</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">分辨�?/span>
                            <span class="detail-value">2048 × 2048</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">文件大小</span>
                            <span class="detail-value">3.2 MB</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">哈希�?/span>
                            <span class="detail-value">0x7f4b3c2a...</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">版权保护</span>
                            <span class="detail-value">数字水印 + 区块链确�?/span>
                        </div>
                    </div>
                </div>

                <!-- 购买须知 -->
                <div class="detail-section">
                    <div class="detail-header" onclick="toggleDetail(this)">
                        <span><i class="fas fa-exclamation-triangle"></i> 购买须知</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="detail-content">
                        <p>1. 数字藏品一经购买，不支持退款退�?/p>
                        <p>2. 藏品仅供收藏和欣赏，不得用于商业用�?/p>
                        <p>3. 购买成功后，藏品将存储在您的个人账户�?/p>
                        <p>4. 请确保您的联系方式正确，以便接收相关通知</p>
                        <p>5. 如有问题，请联系客服�?00-800-1234</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部购买区域 -->
    <div class="purchase-section">
        <div class="container">
            <div class="purchase-actions">
                <button class="favorite-btn" id="favoriteBtn" onclick="toggleFavorite()">
                    <i class="fas fa-heart"></i>
                </button>
                <button class="btn btn-primary purchase-btn" onclick="showPaymentModal()">
                    立即购买
                </button>
            </div>
        </div>
    </div>

    <!-- 分享弹窗 -->
    <div class="share-modal" id="shareModal">
        <div class="share-content">
            <div class="share-title">分享藏品</div>
            <div class="share-options">
                <div class="share-option" onclick="shareToWeChat()">
                    <div class="share-icon wechat">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="share-label">微信</div>
                </div>
                <div class="share-option" onclick="shareToWeibo()">
                    <div class="share-icon weibo">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <div class="share-label">微博</div>
                </div>
                <div class="share-option" onclick="shareToQQ()">
                    <div class="share-icon qq">
                        <i class="fab fa-qq"></i>
                    </div>
                    <div class="share-label">QQ</div>
                </div>
                <div class="share-option" onclick="copyLink()">
                    <div class="share-icon link">
                        <i class="fas fa-link"></i>
                    </div>
                    <div class="share-label">复制链接</div>
                </div>
            </div>
            <button class="close-btn" onclick="hideShareModal()">取消</button>
        </div>
    </div>

    <!-- 支付弹窗 -->
    <div class="payment-modal" id="paymentModal">
        <div class="payment-content">
            <div class="payment-title">选择支付方式</div>
            <div style="text-align: center; margin: 16px 0;">
                <div style="font-size: 18px; color: #daa520; font-weight: bold;">�?99.00</div>
                <div style="font-size: 14px; color: rgba(240, 224, 208, 0.7);">金沙太阳神鸟金饰数字藏品</div>
            </div>
            
            <div class="payment-methods">
                <div class="payment-method selected" data-method="unionpay">
                    <div class="payment-icon unionpay">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <div class="payment-name">银联云闪�?/div>
                    <i class="fas fa-check-circle" style="color: #daa520;"></i>
                </div>
                <div class="payment-method" data-method="alipay">
                    <div class="payment-icon alipay">
                        <i class="fab fa-alipay"></i>
                    </div>
                    <div class="payment-name">支付�?/div>
                </div>
                <div class="payment-method" data-method="wechatpay">
                    <div class="payment-icon wechatpay">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div class="payment-name">微信支付</div>
                </div>
            </div>
            
            <div class="payment-actions">
                <button class="btn btn-secondary" style="flex: 1;" onclick="hidePaymentModal()">取消</button>
                <button class="btn btn-primary" style="flex: 2;" onclick="processPurchase()">确认支付</button>
            </div>
        </div>
    </div>

    <script>
        // 倒计时功�?
        function updateCountdown() {
            const endTime = new Date();
            endTime.setDate(endTime.getDate() + 2);
            endTime.setHours(endTime.getHours() + 15);
            endTime.setMinutes(endTime.getMinutes() + 34);
            endTime.setSeconds(endTime.getSeconds() + 26);

            const now = new Date();
            const diff = endTime - now;

            if (diff > 0) {
                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diff % (1000 * 60)) / 1000);

                document.querySelector('.time-unit:nth-child(1) .time-number').textContent = String(days).padStart(2, '0');
                document.querySelector('.time-unit:nth-child(2) .time-number').textContent = String(hours).padStart(2, '0');
                document.querySelector('.time-unit:nth-child(3) .time-number').textContent = String(minutes).padStart(2, '0');
                document.querySelector('.time-unit:nth-child(4) .time-number').textContent = String(seconds).padStart(2, '0');
            }
        }

        // 每秒更新倒计�?
        setInterval(updateCountdown, 1000);
        updateCountdown();

        // 详情展开收起
        function toggleDetail(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.fa-chevron-down');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('active');
                icon.style.transform = 'rotate(180deg)';
            }
        }

        // 收藏功能
        let isFavorited = false;
        function toggleFavorite() {
            const favoriteBtn = document.getElementById('favoriteBtn');
            const icon = favoriteBtn.querySelector('i');
            
            isFavorited = !isFavorited;
            
            if (isFavorited) {
                favoriteBtn.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
                alert('已添加到收藏');
            } else {
                favoriteBtn.classList.remove('favorited');
                icon.classList.remove('fas');
                icon.classList.add('far');
                alert('已取消收�?);
            }
        }

        // 分享功能
        function showShareModal() {
            document.getElementById('shareModal').style.display = 'flex';
        }

        function hideShareModal() {
            document.getElementById('shareModal').style.display = 'none';
        }

        function shareToWeChat() {
            alert('分享到微�?);
            hideShareModal();
        }

        function shareToWeibo() {
            alert('分享到微�?);
            hideShareModal();
        }

        function shareToQQ() {
            alert('分享到QQ');
            hideShareModal();
        }

        function copyLink() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('链接已复制到剪贴�?);
                hideShareModal();
            });
        }

        // 支付功能
        function showPaymentModal() {
            document.getElementById('paymentModal').style.display = 'flex';
        }

        function hidePaymentModal() {
            document.getElementById('paymentModal').style.display = 'none';
        }

        // 支付方式选择
        document.querySelectorAll('.payment-method').forEach(method => {
            method.addEventListener('click', function() {
                document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        function processPurchase() {
            const selectedMethod = document.querySelector('.payment-method.selected').getAttribute('data-method');
            
            hidePaymentModal();
            
            // 模拟支付处理
            alert('正在跳转到支付页�?..');
            
            setTimeout(() => {
                if (selectedMethod === 'unionpay') {
                    alert('银联H5支付：支付成功！\n交易单号：UP2024011512345678\n购买的藏品将在确认后添加到您的收藏中�?);
                } else if (selectedMethod === 'alipay') {
                    alert('支付宝支付成功！');
                } else if (selectedMethod === 'wechatpay') {
                    alert('微信支付成功�?);
                }
                
                // 模拟跳转到我的收�?
                // window.location.href = 'my-collection.html';
            }, 2000);
        }

        // 点击遮罩关闭弹窗
        document.getElementById('shareModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideShareModal();
            }
        });

        document.getElementById('paymentModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hidePaymentModal();
            }
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .favorite-btn').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
