{"openapi": "3.0.1", "info": {"title": "川文数服", "description": "", "version": "1.0.0"}, "tags": [{"name": "登录注册"}, {"name": "通用"}], "paths": {"/sendPhoneCode": {"get": {"summary": "发送短信验证码", "x-apifox-folder": "通用", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["通用"], "parameters": [{"name": "phone", "in": "query", "description": "手机号", "required": false, "example": "18284532826", "schema": {"type": "string"}}, {"name": "scene", "in": "query", "description": "场景类型", "required": false, "example": "login", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-225533763-run", "security": []}}, "/login": {"post": {"summary": "登录-账号密码", "x-apifox-folder": "登录注册", "x-apifox-status": "released", "deprecated": false, "description": "", "tags": ["登录注册"], "parameters": [{"name": "content-type", "in": "header", "description": "", "required": false, "example": "application/json;charset=UTF-8", "schema": {"type": "string"}}, {"name": "Accept", "in": "header", "description": "", "required": false, "example": "application/json, text/plain, */*", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}}, "x-apifox-orders": ["username", "password"], "required": ["username", "password"], "x-apifox-ignore-properties": []}, "example": {"username": "18284532826", "password": "My635276"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}, "examples": {"1": {"summary": "成功示例", "value": {"msg": "操作成功", "code": 200, "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjMxNDVhYjFhLTM4MzMtNDkzZC1hMzEzLWFhNGM1YWI4YTkyZCJ9.UdM6UQ3XsT_XMYUngi_bMHIRJXk7FIeEdaG9uR0xSt5ColIxG2YF74WL83Z6vOHCiXWW8QyrOiM-vpxiMEhnxw"}}, "2": {"summary": "异常示例", "value": {"msg": "用户不存在/密码错误", "code": 500}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-226550915-run", "security": []}}, "/register": {"post": {"summary": "注册", "x-apifox-folder": "登录注册", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["登录注册"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"username": {"type": "string", "description": "用户名"}, "password": {"type": "string", "description": "密码"}, "phone": {"type": "string", "description": "手机号"}, "smsCode": {"type": "string", "description": "手机验证码"}}, "x-apifox-orders": ["username", "password", "phone", "smsCode"], "required": ["username", "password", "phone", "smsCode"], "x-apifox-ignore-properties": []}, "example": {"username": "muyi", "password": "my635276843", "phone": "18284532826", "code": "1", "smsCode": "49", "uuid": "808544f293c6497d94aa1ede0f3e8cc7"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-228789609-run", "security": []}}, "/getInfo": {"get": {"summary": "获取用户信息", "x-apifox-folder": "登录注册", "x-apifox-status": "released", "deprecated": false, "description": "", "tags": ["登录注册"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string"}, "code": {"type": "integer"}, "permissions": {"type": "array", "items": {"type": "string"}}, "roles": {"type": "array", "items": {"type": "string"}}, "user": {"type": "object", "properties": {"createBy": {"type": "null"}, "createTime": {"type": "string"}, "updateBy": {"type": "null"}, "updateTime": {"type": "null"}, "remark": {"type": "null"}, "params": {"type": "object", "properties": {"@type": {"type": "string"}}, "required": ["@type"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["@type"]}, "userId": {"type": "integer"}, "deptId": {"type": "null"}, "userName": {"type": "string"}, "nickName": {"type": "string"}, "email": {"type": "null"}, "phonenumber": {"type": "string"}, "sex": {"type": "string"}, "avatar": {"type": "null"}, "password": {"type": "string"}, "status": {"type": "string"}, "delFlag": {"type": "string"}, "loginIp": {"type": "null"}, "loginDate": {"type": "null"}, "dept": {"type": "null"}, "roles": {"type": "array", "items": {"type": "string"}}, "roleIds": {"type": "null"}, "postIds": {"type": "null"}, "roleId": {"type": "null"}, "admin": {"type": "boolean"}}, "required": ["createBy", "createTime", "updateBy", "updateTime", "remark", "params", "userId", "deptId", "userName", "nick<PERSON><PERSON>", "email", "phonenumber", "sex", "avatar", "password", "status", "delFlag", "loginIp", "loginDate", "dept", "roles", "roleIds", "postIds", "roleId", "admin"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["createBy", "createTime", "updateBy", "updateTime", "remark", "params", "userId", "deptId", "userName", "nick<PERSON><PERSON>", "email", "phonenumber", "sex", "avatar", "password", "status", "delFlag", "loginIp", "loginDate", "dept", "roles", "roleIds", "postIds", "roleId", "admin"]}}, "required": ["msg", "code", "permissions", "roles", "user"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["msg", "code", "permissions", "roles", "user"]}, "examples": {"1": {"summary": "成功示例", "value": {"msg": "操作成功", "code": 200, "permissions": [], "roles": [], "user": {"createBy": null, "createTime": "2024-11-15 16:41:35", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "userId": 101, "deptId": null, "userName": "18284532826", "nickName": "18284532826", "email": null, "phonenumber": "18284532826", "sex": "0", "avatar": null, "password": "$2a$10$PVoA5kR42Jzr1c3UhJCOe.Vf8iODv9l/ZlCoIyfZfh3eeiszc9VbW", "status": "0", "delFlag": "0", "loginIp": "**************", "loginDate": "2025-01-14T16:24:32.000+08:00", "dept": null, "roles": [], "roleIds": null, "postIds": null, "roleId": null, "admin": false}}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-228798550-run", "security": []}}, "/login/phone": {"post": {"summary": "登录-手机号验证码", "x-apifox-folder": "登录注册", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["登录注册"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"phone": {"type": "string", "description": "手机号"}, "smsCode": {"type": "string", "description": "短信验证码"}}, "x-apifox-orders": ["phone", "smsCode"], "required": ["phone", "smsCode"], "x-apifox-ignore-properties": []}, "example": {"phone": "admin", "smsCode": "admin123"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-230534284-run", "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [{"url": "https://ts.sccdex.com", "description": "测试环境"}]}