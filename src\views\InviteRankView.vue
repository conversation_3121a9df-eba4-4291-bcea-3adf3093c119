<template>
  <AppPageHeader :title="'邀新排行榜'" @back="$router.back()" />
  <div class="invite-rank-container" :style="{ paddingTop: '40px' }">


    <main class="main-content">
      <div class="container">
        <!-- 活动信息 -->
        <section class="activity-info" v-if="activity">
          <div class="activity-cover">
            <img :src="activity.activityCover" :alt="activity.activityName" />
          </div>
        </section>

        <!-- 排行榜列表 -->
        <section class="rank-list">
          <!-- <div class="rank-header">
            <h2 class="section-title">邀新排行榜</h2>
            <div class="rank-subtitle">邀请好友参与活动，赢取丰厚奖励</div>
          </div> -->

          <!-- 加载状态 -->
          <div v-if="loading && rankList.length === 0" class="loading-state">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载排行榜中...</div>
          </div>

          <!-- 排行榜内容 -->
          <div v-else-if="rankList.length > 0" class="rank-content">
            <div
              v-for="(item, index) in rankList"
              :key="item.userId"
              class="rank-item"
              :class="{ 'top-three': index < 3 }"
            >
              <!-- 排名 -->
              <div class="rank-number" :class="`rank-${index + 1}`">
                <span v-if="index < 3" class="rank-icon">
                  <i class="fas fa-crown" v-if="index === 0"></i>
                  <i class="fas fa-medal" v-else-if="index === 1"></i>
                  <i class="fas fa-award" v-else></i>
                </span>
                <span v-else>{{ index + 1 }}</span>
              </div>

              <!-- 用户信息 -->
              <div class="user-info">
                <div class="user-avatar">
                  <img v-if="item.avatar && item.avatar.trim() !== ''" :src="item.avatar" :alt="item.nickName" />
                  <svg v-else width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="24" cy="24" r="24" fill="#d4a574"/>
                    <circle cx="24" cy="18" r="8" fill="white"/>
                    <path d="M8 40c0-8.84 7.16-16 16-16s16 7.16 16 16" fill="white"/>
                  </svg>
                </div>
                <div class="user-details">
                  <div class="user-name">{{ item.nickName }}</div>
                  <!-- <div class="user-id">ID: {{ item.userId }}</div> -->
                </div>
              </div>

              <!-- 邀请数量 -->
              <div class="invite-count">
                <div class="count-badge">
                  <span class="count-number">{{ item.inviteNum }}</span>
                  <span class="count-label">人</span>
                </div>
              </div>
            </div>

            <!-- 加载更多状态 -->
            <div v-if="loadingMore" class="loading-more">
              <div class="loading-spinner small"></div>
              <div class="loading-text">加载更多...</div>
            </div>

            <!-- 没有更多数据 -->
            <div v-else-if="noMoreData" class="no-more-data">
              <i class="fas fa-flag-checkered"></i>
              <span>数据已到底</span>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="empty-state">
            <i class="fas fa-trophy empty-icon"></i>
            <div class="empty-text">暂无排行榜数据</div>
            <div class="empty-subtitle">快来邀请好友参与活动吧！</div>
          </div>
        </section>
      </div>
    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import BottomNavigation from '@/components/BottomNavigation.vue'
import ActivityAPI, { type ActivityDetail, type InviteRankItem, type InviteRankResponse } from '@/api/activity'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const router = useRouter()
const route = useRoute()
const { success, error, info } = useNotification()

// 响应式数据
const loading = ref(false)
const loadingMore = ref(false)
const activity = ref<ActivityDetail | null>(null)
const rankList = ref<InviteRankItem[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const noMoreData = ref(false)

// 暴露ActivityAPI到模板
const { getStatusText, getStatusClass, formatDateTime } = ActivityAPI

// 计算属性
const hasMoreData = computed(() => {
  return rankList.value.length < total.value
})

// 方法
const goBack = () => {
  router.back()
}



/**
 * 加载活动详情
 */
const loadActivityDetail = async () => {
  const activityId = Number(route.params.activityId)
  if (!activityId) {
    error('活动ID不存在')
    router.back()
    return
  }

  try {
    const response = await ActivityAPI.getActivityDetail(activityId)

    if (response.code === 200 && response.data) {
      activity.value = response.data
    } else {
      error(`加载活动详情失败: ${response.msg}`)
    }
  } catch (err: any) {
    console.error('加载活动详情失败:', err)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    if (err?.code === 401 || err?.status === 401) {
      // 401错误时不设置任何数据，保持空状态
      activity.value = null
      return
    }

    error('加载活动详情失败，请稍后重试')
  }
}

/**
 * 加载邀新排行榜数据
 */
const loadInviteRank = async (isLoadMore = false) => {
  const activityId = Number(route.params.activityId)
  if (!activityId) return

  try {
    if (isLoadMore) {
      loadingMore.value = true
      currentPage.value++
    } else {
      loading.value = true
      currentPage.value = 1
      rankList.value = []
      noMoreData.value = false
    }

    const response = await ActivityAPI.getInviteRank(activityId, {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })

    if (response.code === 200) {
      if (isLoadMore) {
        // 加载更多时追加数据
        rankList.value.push(...response.rows)
      } else {
        // 首次加载时替换数据
        rankList.value = response.rows
      }

      total.value = response.total

      // 判断是否还有更多数据
      if (rankList.value.length >= response.total) {
        noMoreData.value = true
      }
    } else {
      error(response.msg || '加载排行榜失败')
    }
  } catch (err: any) {
    console.error('加载排行榜失败:', err)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    if (err?.code === 401 || err?.status === 401) {
      // 401错误时不设置任何数据，保持空状态
      rankList.value = []
      return
    }

    error(err.message || '加载排行榜失败，请稍后重试')
  } finally {
    loading.value = false
    loadingMore.value = false
  }
}

/**
 * 滚动加载更多
 */
const handleScroll = () => {
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const windowHeight = window.innerHeight
  const documentHeight = document.documentElement.scrollHeight

  // 距离底部100px时触发加载
  if (scrollTop + windowHeight >= documentHeight - 100) {
    if (!loadingMore.value && !noMoreData.value && hasMoreData.value) {
      loadInviteRank(true)
    }
  }
}

// 生命周期
onMounted(() => {
  loadActivityDetail()
  loadInviteRank()
  window.addEventListener('scroll', handleScroll)
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);

  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;

  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;

  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);

  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
}

.invite-rank-container {
  background: var(--gradient-hero);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* 顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 0;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: var(--primary-alpha);
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.text-primary {
  color: var(--primary-color);
}

/* 主要内容 */
.main-content {
  padding-bottom: 80px;
}

/* 活动信息 */
.activity-info {
  margin: 20px 0;
}

.activity-cover {
  width: 100%;
  height: 250px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.activity-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.activity-status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.status-ongoing {
  background: rgba(59, 130, 246, 0.8);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.status-upcoming {
  background: rgba(212, 165, 116, 0.8);
  color: white;
  border: 1px solid rgba(212, 165, 116, 0.5);
}

.status-ended {
  background: rgba(148, 163, 184, 0.8);
  color: white;
  border: 1px solid rgba(148, 163, 184, 0.5);
}

/* 排行榜列表 */
.rank-list {
  margin-bottom: 24px;
}

.rank-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8px;
  text-align: center;
}

.rank-subtitle {
  font-size: 14px;
  color: var(--text-tertiary);
  text-align: center;
}

/* 排行榜项目 */
.rank-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rank-item {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.rank-item:hover {
  transform: translateY(-2px);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
}

.rank-item.top-three {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

/* 排名 */
.rank-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.rank-1 {
  background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
  color: #2d2a24;
}

.rank-2 {
  background: linear-gradient(135deg, #c0c0c0 0%, #e5e5e5 100%);
  color: #2d2a24;
}

.rank-3 {
  background: linear-gradient(135deg, #cd7f32 0%, #daa520 100%);
  color: white;
}

.rank-number:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: var(--gradient-primary);
}

.rank-icon {
  font-size: 18px;
}

/* 用户信息 */
.user-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img,
.user-avatar svg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.user-id {
  font-size: 12px;
  color: var(--text-tertiary);
}

/* 邀请数量 */
.invite-count {
  text-align: center;
  flex-shrink: 0;
}

.count-badge {
  background: var(--gradient-primary);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(212, 165, 116, 0.3);
}

.count-number {
  font-size: 18px;
  font-weight: 700;
  color: white;
  line-height: 1;
}

.count-label {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 2px;
  font-weight: 500;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(212, 165, 116, 0.2);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner.small {
  width: 24px;
  height: 24px;
  border-width: 2px;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* 加载更多 */
.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  color: var(--text-tertiary);
}

.no-more-data {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: var(--text-tertiary);
  font-size: 14px;
}

.no-more-data i {
  font-size: 16px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--primary-color);
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.empty-subtitle {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container {
    padding: 0 12px;
  }

  .activity-cover {
    height: 210px;
  }

  .rank-item {
    padding: 12px;
    gap: 12px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }

  .rank-number {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .count-badge {
    padding: 6px 12px;
  }

  .count-number {
    font-size: 16px;
  }

  .count-label {
    font-size: 9px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 18px;
  }

  .user-name {
    font-size: 14px;
  }

  .activity-cover {
    height: 210px;
  }

  .count-badge {
    padding: 5px 10px;
  }

  .count-number {
    font-size: 14px;
  }

  .count-label {
    font-size: 8px;
  }
}
</style>
