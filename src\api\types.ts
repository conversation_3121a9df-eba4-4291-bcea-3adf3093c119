/**
 * API基础响应类型
 */
export interface ApiResponse<T = any> {
  msg: string
  code: number
  data?: T
}

/**
 * 登录请求参数
 */
export interface LoginRequest {
  username: string
  password: string
}

/**
 * 登录响应数据
 */
export interface LoginResponse {
  msg: string
  code: number
  token: string
}

/**
 * 手机号登录请求参数
 */
export interface PhoneLoginRequest {
  phone: string
  smsCode: string
}

/**
 * 注册请求参数
 */
export interface RegisterRequest {
  username: string
  password: string
  phone: string
  smsCode: string
}

/**
 * 发送验证码请求参数
 */
export interface SendSmsRequest {
  phone: string
  scene: 'login' | 'register' | 'identify' | 'H5resetPassword' | 'H5login' | 'H5register'
}

/**
 * 用户信息
 */
export interface UserInfo {
  createBy: null | string
  createTime: string
  updateBy: null | string
  updateTime: null | string
  remark: null | string
  params: {
    '@type': string
  }
  userId: number
  deptId: null | number
  userName: string
  nickName: string
  email: null | string
  phonenumber: string
  sex: string
  avatar: null | string
  password: string
  status: string
  delFlag: string
  loginIp: null | string
  loginDate: null | string
  dept: null | any
  roles: string[]
  roleIds: null | number[]
  postIds: null | number[]
  roleId: null | number
  admin: boolean
}

/**
 * 获取用户信息响应
 */
export interface GetUserInfoResponse {
  msg: string
  code: number
  permissions: string[]
  roles: string[]
  user: UserInfo
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  pageNum?: number
  pageSize?: number
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T> {
  list: T[]
  total: number
  pageNum: number
  pageSize: number
  pages: number
}

declare module 'qrcode'; 

