<template>
  <div class="design-system-container">
    <div class="design-system-inner">
      <div class="header">
        <h1>太阳神鸟数字藏品平台</h1>
        <p>探索金沙文化的数字艺术世界</p>
      </div>

      <!-- 产品展示区 -->
      <div class="section">
        <h2 class="section-title">
          <i class="fas fa-fire"></i>热门藏品
        </h2>
        <div class="products-grid">
          <DSProductCard
            v-for="product in products"
            :key="product.id"
            :name="product.name"
            :price="product.price"
            :tag="product.tag"
            :sale-time="product.saleTime"
            :image-placeholder="product.image"
            :tags="product.tags"
          />
        </div>
      </div>

      <!-- 活动倒计时 -->
      <div class="section">
        <h2 class="section-title">
          <i class="fas fa-clock"></i>限时活动
        </h2>
        <div class="countdown-section">
          <DSCountdownCard
            title="太阳神鸟限量版发售"
            label="距离开售还有"
          />
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="section">
        <h2 class="section-title">
          <i class="fas fa-chart-line"></i>平台数据
        </h2>
        <DSStatsCard :stats="platformStats" />
      </div>

      <!-- 操作按钮 -->
      <div class="section">
        <h2 class="section-title">
          <i class="fas fa-tools"></i>快捷操作
        </h2>
        <div class="actions">
          <DSButton 
            type="primary" 
            size="large"
            icon="fas fa-shopping-cart"
            @click="handlePurchase"
          >
            立即购买
          </DSButton>
          <DSButton 
            type="secondary"
            icon="fas fa-heart"
            @click="handleFavorite"
          >
            加入收藏
          </DSButton>
          <DSButton 
            type="accent"
            icon="fas fa-share"
            @click="handleShare"
          >
            分享好友
          </DSButton>
        </div>
      </div>

      <!-- 标签展示 -->
      <div class="section">
        <h2 class="section-title">
          <i class="fas fa-tags"></i>热门标签
        </h2>
        <div class="tags-section">
          <DSTag 
            v-for="tag in hotTags"
            :key="tag.text"
            :type="tag.type"
            :size="tag.size"
          >
            {{ tag.text }}
          </DSTag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DSButton, DSTag, DSProductCard, DSCountdownCard, DSStatsCard } from '../components/design-system'

// 产品数据
const products = ref([
  {
    id: 1,
    name: '太阳神鸟金沙面具',
    price: '￥199.00',
    tag: '限量',
    saleTime: '10:00开售',
    image: '神秘面具',
    tags: [
      { text: '文化遗产', type: 'primary' as const },
      { text: '限量发行', type: 'accent' as const }
    ]
  },
  {
    id: 2,
    name: '古蜀文明印章',
    price: '￥99.00',
    tag: '热门',
    saleTime: '12:00开售',
    image: '古蜀印章',
    tags: [
      { text: '收藏级', type: 'success' as const },
      { text: '优先购', type: 'warning' as const }
    ]
  },
  {
    id: 3,
    name: '金沙遗址全景',
    price: '￥299.00',
    tag: '珍品',
    saleTime: '14:00开售',
    image: '遗址全景',
    tags: [
      { text: '3D还原', type: 'primary' as const },
      { text: '独家授权', type: 'accent' as const }
    ]
  }
])

// 平台统计数据
const platformStats = ref([
  { value: '10,000+', label: '注册用户' },
  { value: '500+', label: '数字藏品' },
  { value: '98%', label: '用户满意度' },
  { value: '24h', label: '客服响应' }
])

// 热门标签
const hotTags = ref([
  { text: '太阳神鸟', type: 'primary' as const, size: 'large' as const },
  { text: '金沙文化', type: 'primary' as const },
  { text: '限量版', type: 'accent' as const },
  { text: '收藏级', type: 'success' as const },
  { text: '文物复刻', type: 'warning' as const },
  { text: '数字艺术', type: 'primary' as const },
  { text: '古蜀文明', type: 'primary' as const },
  { text: '3D建模', type: 'success' as const }
])

// 事件处理
const handlePurchase = () => {
  console.log('购买商品')
}

const handleFavorite = () => {
  console.log('加入收藏')
}

const handleShare = () => {
  console.log('分享商品')
}
</script>

<style scoped>
.header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  padding: var(--spacing-xxl) 0;
  border-bottom: 1px solid var(--border-primary);
  background: linear-gradient(135deg, rgba(139, 0, 0, 0.1), rgba(184, 134, 11, 0.1));
  border-radius: var(--radius-xl);
}

.header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  text-shadow: 0 2px 5px rgba(218, 165, 32, 0.3);
}

.header p {
  font-size: 18px;
  color: var(--text-tertiary);
}

.section {
  background: var(--bg-section);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xxl);
  margin-bottom: var(--spacing-xxl);
  border: 1px solid var(--border-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background-image: radial-gradient(circle at 10% 20%, rgba(218, 165, 32, 0.05) 0%, rgba(139, 0, 0, 0.05) 90%);
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--border-primary);
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: var(--spacing-lg);
  font-size: 22px;
  color: var(--text-tertiary);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-xl);
}

.countdown-section {
  display: flex;
  justify-content: center;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.tags-section {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
  
  .header h1 {
    font-size: 24px;
  }
  
  .header p {
    font-size: 16px;
  }
}
</style> 