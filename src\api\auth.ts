import request from './request'
import type {
  LoginRequest,
  LoginResponse,
  PhoneLoginRequest,
  RegisterRequest,
  SendSmsRequest,
  GetUserInfoResponse,
  ApiResponse
} from './types'

/**
 * 登录注册API服务
 */
export class AuthAPI {
  /**
   * 发送短信验证码
   */
  static async sendSmsCode(params: SendSmsRequest): Promise<ApiResponse> {
    return request.get('/sendPhoneCode', params)
  }

  /**
   * 账号密码登录
   */
  static async login(data: LoginRequest): Promise<LoginResponse> {
    return request.post<LoginResponse>('/login', data)
  }

  /**
   * 手机号验证码登录
   */
  static async phoneLogin(data: PhoneLoginRequest): Promise<LoginResponse> {
    return request.post<LoginResponse>('/login/phone', data)
  }

  /**
   * 注册
   */
  static async register(data: RegisterRequest): Promise<ApiResponse> {
    return request.post('/register', data)
  }

  /**
   * 获取用户信息
   */
  static async getUserInfo(): Promise<GetUserInfoResponse> {
    return request.get<GetUserInfoResponse>('/getInfo')
  }

  /**
   * 重置密码
   */
  static async resetPassword(data: { phone: string; code: string; password: string }): Promise<ApiResponse> {
    return request.post('/resetPassword', data)
  }

  /**
   * 重置密码（活动模块接口）
   */
  static async updatePwd(data: { phone: string; smsCode: string; password: string }): Promise<ApiResponse> {
    return request.put('/activity/activity/client/updatePwd', data)
  }

  /**
   * 登出（清除本地token）
   */
  static logout(): void {
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }

  /**
   * 检查是否已登录
   */
  static isLoggedIn(): boolean {
    return !!localStorage.getItem('token')
  }

  /**
   * 保存登录信息
   */
  static saveLoginInfo(token: string, userInfo?: any): void {
    localStorage.setItem('token', token)
    if (userInfo) {
      localStorage.setItem('userInfo', JSON.stringify(userInfo))
    }
  }

  /**
   * 获取本地用户信息
   */
  static getLocalUserInfo(): any {
    const userInfo = localStorage.getItem('userInfo')
    return userInfo ? JSON.parse(userInfo) : null
  }
}

export default AuthAPI 