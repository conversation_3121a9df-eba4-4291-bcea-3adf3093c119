import { ref, onMounted, onUnmounted, nextTick } from 'vue'

export interface KeyboardAdaptationOptions {
  /** 额外的滚动偏移量 */
  extraOffset?: number
  /** 是否启用平滑滚动 */
  smoothScroll?: boolean
  /** 滚动延迟（毫秒） */
  scrollDelay?: number
  /** 是否在键盘弹出时调整视口高度 */
  adjustViewport?: boolean
}

export function useKeyboardAdaptation(options: KeyboardAdaptationOptions = {}) {
  const {
    extraOffset = 20,
    smoothScroll = true,
    scrollDelay = 300,
    adjustViewport = true
  } = options

  const isKeyboardVisible = ref(false)
  const keyboardHeight = ref(0)
  const originalViewportHeight = ref(0)
  const currentActiveElement = ref<HTMLElement | null>(null)

  // 检测键盘是否弹出
  const detectKeyboard = () => {
    if (!originalViewportHeight.value) {
      originalViewportHeight.value = window.innerHeight
    }

    const currentHeight = window.innerHeight
    const heightDifference = originalViewportHeight.value - currentHeight
    
    // 如果高度差异超过150px，认为键盘弹出了
    if (heightDifference > 150) {
      isKeyboardVisible.value = true
      keyboardHeight.value = heightDifference
      
      if (adjustViewport) {
        adjustViewportForKeyboard()
      }
    } else {
      isKeyboardVisible.value = false
      keyboardHeight.value = 0
      
      if (adjustViewport) {
        restoreViewport()
      }
    }
  }

  // 调整视口以适应键盘
  const adjustViewportForKeyboard = () => {
    const authPage = document.querySelector('.auth-page') as HTMLElement
    if (authPage) {
      authPage.style.height = `${window.innerHeight}px`
      authPage.style.overflow = 'hidden'
    }
  }

  // 恢复原始视口
  const restoreViewport = () => {
    const authPage = document.querySelector('.auth-page') as HTMLElement
    if (authPage) {
      authPage.style.height = 'auto'
      authPage.style.overflow = 'visible'
    }
  }

  // 滚动到指定元素
  const scrollToElement = (element: HTMLElement) => {
    if (!element) return

    const elementRect = element.getBoundingClientRect()
    const viewportHeight = window.innerHeight
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    
    // 计算元素相对于文档的位置
    const elementTop = elementRect.top + scrollTop
    const elementBottom = elementTop + elementRect.height
    
    // 计算可见区域（考虑键盘高度）
    const visibleAreaBottom = viewportHeight - keyboardHeight.value
    const visibleAreaTop = scrollTop
    
    // 如果元素被键盘遮挡或不在可见区域内
    if (elementBottom > visibleAreaBottom + scrollTop || elementTop < visibleAreaTop) {
      // 计算目标滚动位置：让元素显示在可见区域的中上部
      const targetScrollTop = elementTop - (visibleAreaBottom * 0.3) - extraOffset
      
      window.scrollTo({
        top: Math.max(0, targetScrollTop),
        behavior: smoothScroll ? 'smooth' : 'auto'
      })
    }
  }

  // 处理输入框焦点事件
  const handleInputFocus = (event: FocusEvent) => {
    const target = event.target as HTMLElement
    
    if (target && (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA')) {
      currentActiveElement.value = target
      
      // 延迟滚动，等待键盘完全弹出
      setTimeout(() => {
        scrollToElement(target)
      }, scrollDelay)
    }
  }

  // 处理输入框失去焦点事件
  const handleInputBlur = () => {
    currentActiveElement.value = null
  }

  // 处理窗口大小变化
  const handleResize = () => {
    // 延迟检测，避免频繁触发
    setTimeout(detectKeyboard, 100)
  }

  // 手动触发滚动到活动元素
  const scrollToActiveElement = () => {
    if (currentActiveElement.value) {
      scrollToElement(currentActiveElement.value)
    }
  }

  // 获取安全区域高度
  const getSafeAreaHeight = () => {
    return window.innerHeight - keyboardHeight.value
  }

  // 初始化
  onMounted(() => {
    originalViewportHeight.value = window.innerHeight
    
    // 添加事件监听器
    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    document.addEventListener('focusin', handleInputFocus)
    document.addEventListener('focusout', handleInputBlur)
    
    // 设置初始样式
    const authPage = document.querySelector('.auth-page') as HTMLElement
    if (authPage) {
      authPage.style.transition = 'height 0.3s ease-out'
    }
    
    console.log('🎯 键盘适配已启用')
  })

  // 清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', handleResize)
    document.removeEventListener('focusin', handleInputFocus)
    document.removeEventListener('focusout', handleInputBlur)
    
    // 恢复原始样式
    restoreViewport()
  })

  return {
    isKeyboardVisible,
    keyboardHeight,
    currentActiveElement,
    scrollToElement,
    scrollToActiveElement,
    getSafeAreaHeight,
    detectKeyboard
  }
} 