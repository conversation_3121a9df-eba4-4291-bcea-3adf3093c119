/**
 * 系列相关API接口
 */

import request from './request'
import type { ApiResponse, PaginationParams, PaginationResponse } from './types'

// 系列详情接口响应类型
export interface SeriesDetail {
  seriesId: string
  seriesName: string
  seriesCover?: string
  seriesDesc?: string
  createTime?: string
  updateTime?: string
  status?: number
  assetCount?: number
}

// 系列详情API响应类型
export interface SeriesDetailResponse extends ApiResponse<SeriesDetail> {}

// 系列资产列表参数类型
export interface SeriesAssetListParams extends PaginationParams {
  seriesId: string
}

// 资产项类型（复用其他地方定义的AssetItem类型）
export interface AssetItem {
  assetId: string
  assetName: string
  assetCover?: string
  assetDesc?: string
  issuePrice?: number
  currentPrice?: number
  assetType?: string
  seriesId?: string
  seriesName?: string
  zoneName?: string
  issueQuantity?: number
  saleStartTime?: string
  status?: number
  createDate?: string
  // 发行方信息
  issuers?: Array<{
    issuerName: string
    issuerLogo?: string
  }>
  issuerIds?: string[]
  issuerName?: string
  issuerLogo?: string
  // 其他字段
  assetKeywords?: string
  introImages?: string
  assetLevel?: string
  individualLimit?: number
  enterpriseLimit?: number
  airdropQuantity?: number
  activityQuantity?: number
}
// 系列资产列表API响应类型
export interface SeriesAssetListResponse {
  total: number
  rows: AssetItem[]
  code: number
  msg: string
}

/**
 * 系列API服务类
 */
class SeriesAPI {
  /**
   * 获取系列详情
   * @param seriesId 系列ID
   * @returns 系列详情
   */
  static async getSeriesDetail(seriesId: string): Promise<SeriesDetailResponse> {
    return request.get(`/series/series/client/${seriesId}`, undefined, {
      headers: {
        'Authorization': '' // 不传token
      }
    })
  }

  /**
   * 获取系列资产列表
   * @param params 查询参数
   * @returns 资产列表
   */
  static async getSeriesAssetList(params: SeriesAssetListParams): Promise<SeriesAssetListResponse> {
    return request.get('/asset/asset/client/list', params, {
      headers: {
        'Authorization': '' // 不传token
      }
    })
  }
}

export default SeriesAPI 