<template>
  <div 
    class="asset-card"
    @click="$emit('click', asset)"
  >
    <div class="asset-image">
      <!-- 状态标签显示 -->
      <!-- <div 
        v-if="statusTag"
        class="asset-tag"
        :class="statusTag.type"
      >
        {{ statusTag.text }}
      </div> -->
      
      <!-- 顶部信息条：发行时间和资产类型 -->
      <div v-if="saleTime || (showAssetType && assetTypeLabel)" class="asset-top-info">
        <!-- 发行时间 - 靠左 -->
        <div v-if="saleTime" class="asset-sale-time">
          <i class="fas fa-clock"></i>
          {{ formatSaleTime(saleTime) }}
        </div>
        
        <!-- 资产类型标签 - 靠右 -->
        <div v-if="showAssetType && assetTypeLabel" class="asset-type-container">
          <span class="asset-type-tag">{{ assetTypeLabel }}</span>
        </div>
      </div>
      
      <!-- 关键字标签 - 显示在左下角 -->
      <div v-if="keywords.length > 0" class="asset-keywords">
        <span 
          v-for="(keyword, tagIndex) in keywords.slice(0, 3)" 
          :key="`${assetId}-${keyword}-${tagIndex}`"
          class="keyword-tag"
        >
          {{ keyword }}
        </span>
        <span v-if="keywords.length > 3" class="keyword-more">
          +{{ keywords.length - 3 }}
        </span>
      </div>
      
      <!-- 显示封面图片 -->
      <img 
        v-if="coverImage" 
        :src="coverImage" 
        :alt="assetName"
        class="asset-cover-image"
        @error="handleImageError"
      />
      <!-- 占位内容 -->
      <div 
        v-else
        class="asset-placeholder"
      >
        {{ assetName }}
      </div>
    </div>
    
    <div class="asset-info">
      <!-- 资产名称 -->
      <div class="asset-name">
        {{ assetName }}
      </div>
      
      <!-- 发行方信息 -->
      <div v-if="issuerInfo" class="issuer-info">
        <div class="issuer-avatar">
          <img 
            v-if="issuerInfo.logo" 
            :src="issuerInfo.logo" 
            :alt="issuerInfo.name"
            class="issuer-logo"
            @error="handleImageError"
          />
          <i v-else class="fas fa-building issuer-default-icon"></i>
        </div>
        <div class="issuer-details">
          <span v-if="showIssuerLabel" class="issuer-label">发行方:</span>
          <span class="issuer-name">{{ issuerInfo.name }}</span>
        </div>
      </div>
      
      <!-- 价格和限量信息 -->
      <div class="price-and-limit">
        <div class="asset-price">
          <span v-if="isSaleStarted">¥</span>{{ formattedPrice }}
        </div>
        <div v-if="limitedQuantity" class="limited-quantity">
          <i class="fas fa-gem"></i>
          <span class="limited-label">限量</span>
          <span class="quantity-number">{{ displayQuantity }}</span>
          <span class="quantity-unit">份</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义通用的资产接口
interface IssuerInfo {
  name?: string
  logo?: string
  issuerName?: string
  issuerLogo?: string
}

interface AssetData {
  // 基础信息
  id?: string | number
  assetId?: string | number
  assetName?: string
  name?: string
  
  // 图片信息
  assetCover?: string
  assetCoverThumbnail?: string
  assetImage?: string
  image?: string
  
  // 价格信息
  issuePrice?: number | string
  assetPrice?: number | string
  price?: number | string
  
  // 时间信息
  saleStartTime?: string
  saleTime?: string
  createTime?: string
  createDate?: string
  
  // 数量信息
  issueQuantity?: number
  totalQuantity?: number
  
  // 关键字信息
  assetKeywords?: string
  keywords?: string[]
  
  // 状态信息
  status?: number | string
  statusCd?: string
  isOnSale?: boolean
  isLimited?: boolean
  
  // 类型信息
  assetType?: string
  
  // 发行方信息
  issuers?: IssuerInfo[]
  issuerName?: string
  issuerLogo?: string
  publisherName?: string
}

// 属性定义
interface Props {
  asset: AssetData
  showAssetType?: boolean
  showIssuerLabel?: boolean
  assetTypesList?: Array<{ value: string; label: string }>
}

const props = withDefaults(defineProps<Props>(), {
  showAssetType: false,
  showIssuerLabel: true,
  assetTypesList: () => []
})

// 事件定义
defineEmits<{
  click: [asset: AssetData]
}>()

// 计算属性
const assetId = computed(() => 
  props.asset.assetId || props.asset.id || ''
)

const assetName = computed(() => 
  props.asset.assetName || props.asset.name || '未命名资产'
)

const coverImage = computed(() => {
  const image = props.asset.assetCoverThumbnail || props.asset.assetImage || props.asset.image
  if (image && (image.startsWith('http') || image.startsWith('/'))) {
    return image
  }
  return undefined
})

const saleTime = computed(() => 
  props.asset.saleStartTime || props.asset.saleTime || props.asset.createTime || props.asset.createDate
)

// 判断是否已到开售时间
const isSaleStarted = computed(() => {
  if (!props.asset.saleStartTime) return true // 没有开售时间则默认已开售
  const saleTime = new Date(props.asset.saleStartTime)
  const now = new Date()
  return saleTime <= now
})

const limitedQuantity = computed(() => 
  props.asset.issueQuantity || props.asset.totalQuantity
)

// 修改价格显示逻辑
const formattedPrice = computed(() => {
  // 如果未到开售时间，显示???
  if (!isSaleStarted.value) {
    return '¥-.-'
  }
  
  const price = props.asset.issuePrice || props.asset.assetPrice || props.asset.price || 0
  if (typeof price === 'number') {
    return price.toFixed(2)
  }
  return String(price).replace(/[¥,]/g, '') || '0.00'
})

// 修改数量显示逻辑
const displayQuantity = computed(() => {
  // 如果未到开售时间，显示???
  if (!isSaleStarted.value) {
    return '???'
  }
  
  return props.asset.issueQuantity || props.asset.totalQuantity
})

const keywords = computed(() => {
  if (props.asset.assetKeywords) {
    return props.asset.assetKeywords.split(',').map(k => k.trim()).filter(k => k)
  }
  if (props.asset.keywords && Array.isArray(props.asset.keywords)) {
    return props.asset.keywords
  }
  return []
})

const statusTag = computed(() => {
  // 根据不同的状态字段生成标签
  if (props.asset.isOnSale) {
    return { text: '热销中', type: 'on-sale' }
  }
  
  if (props.asset.isLimited) {
    return { text: '限量发行', type: 'limited' }
  }
  
  if (props.asset.status) {
    switch (props.asset.status) {
      case 1:
        return { text: '在售', type: 'on-sale' }
      case 2:
        return { text: '售罄', type: 'sold-out' }
      case 3:
        return { text: '限量', type: 'limited' }
      default:
        return { text: `状态${props.asset.status}`, type: 'default' }
    }
  }
  
  if (props.asset.statusCd) {
    switch (props.asset.statusCd) {
      case 'ON_SALE':
        return { text: '在售', type: 'on-sale' }
      case 'SOLD_OUT':
        return { text: '售罄', type: 'sold-out' }
      case 'LIMITED':
        return { text: '限量', type: 'limited' }
      default:
        return { text: props.asset.statusCd, type: 'default' }
    }
  }
  
  // 检查开售时间
  if (props.asset.saleStartTime) {
    const saleTime = new Date(props.asset.saleStartTime)
    const now = new Date()
    if (saleTime > now) {
      return { text: '预售', type: 'limited' }
    }
  }
  
  return null
})

const issuerInfo = computed(() => {
  // 优先使用issuers字段
  if (props.asset.issuers && Array.isArray(props.asset.issuers) && props.asset.issuers.length > 0) {
    const firstIssuer = props.asset.issuers[0]
    return {
      name: firstIssuer.issuerName || firstIssuer.name || '未知发行方',
      logo: firstIssuer.issuerLogo || firstIssuer.logo
    }
  }
  
  // 兼容单个发行方字段
  if (props.asset.issuerName || props.asset.publisherName) {
    return {
      name: props.asset.issuerName || props.asset.publisherName || '未知发行方',
      logo: props.asset.issuerLogo
    }
  }
  
  return null
})

const assetTypeLabel = computed(() => {
  if (!props.asset.assetType || !props.assetTypesList.length) {
    return props.asset.assetType || ''
  }
  
  const found = props.assetTypesList.find(item => item.value === props.asset.assetType)
  return found ? found.label : props.asset.assetType
})

// 方法
const formatSaleTime = (timeStr: string): string => {
  if (!timeStr) return ''
  
  try {
    const date = new Date(timeStr)
    const now = new Date()
    const isToday = date.toDateString() === now.toDateString()
    const isThisYear = date.getFullYear() === now.getFullYear()
    
    if (isToday) {
      // 今天：只显示时间
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else if (isThisYear) {
      // 今年：显示月/日 时:分
      return date.toLocaleString('zh-CN', { 
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit', 
        minute: '2-digit' 
      })
    } else {
      // 其他年份：显示年/月/日
      return date.toLocaleDateString('zh-CN', { 
        year: '2-digit',
        month: '2-digit',
        day: '2-digit'
      })
    }
  } catch (error) {
    // 解析失败时的简化处理
    if (timeStr.length >= 16) {
      return timeStr.slice(5, 16) // 提取 MM-DD HH:mm 部分
    }
    return timeStr
  }
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}
</script>

<style scoped>
/* 资产卡片样式 */
.asset-card {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  position: relative;
}

.asset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.1), transparent);
  transition: left 0.8s ease;
  z-index: 1;
  pointer-events: none;
}

.asset-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.6);
  border-color: #d4a574;
}

.asset-card:hover::before {
  left: 100%;
}

.asset-image {
  width: 100%;
  height: 140px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #242424, #2e2e2e);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d4a574;
  font-size: 13px;
  z-index: 2;
}

.asset-cover-image {
  width: 100%;
  height: auto;
  display: block;
  max-width: 100%;
}

.asset-placeholder {
  color: #d4a574;
  font-size: 12px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  padding: 20px;
  width: 100%;
}

.asset-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  color: white;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
}

.asset-tag.on-sale {
  background: #90a955;
}

.asset-tag.limited {
  background: #d4a574;
}

.asset-tag.sold-out {
  background: #a39d8e;
}

.asset-tag.default {
  background: #e63946;
}

/* 顶部信息容器：发行时间和资产类型同一行显示 */
.asset-top-info {
  position: absolute;
  top: 8px;
  left: 8px;
  right: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  z-index: 2;
}

/* 发行时间标签样式 */
.asset-sale-time {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: #f8f6f0;
  font-size: 9px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 3px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-shrink: 1;
  min-width: 0;
}

.asset-sale-time:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: #d4a574;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.asset-sale-time i {
  font-size: 8px;
  color: #e8c49a;
  flex-shrink: 0;
}

/* 资产类型标签容器 */
.asset-type-container {
  flex-shrink: 0;
}

/* 资产类型标签样式 */
.asset-type-tag {
  display: inline-block;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  font-size: 9px;
  font-weight: 600;
  padding: 3px 6px;
  border-radius: 6px;
  border: 1px solid rgba(79, 172, 254, 0.4);
  box-shadow: 0 2px 4px rgba(79, 172, 254, 0.3);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.asset-type-tag:hover {
  background: linear-gradient(135deg, #3d9cee 0%, #00e2ee 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(79, 172, 254, 0.4);
}

/* 关键字标签样式 */
.asset-keywords {
  position: absolute;
  bottom: 8px;
  left: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: calc(100% - 16px);
  z-index: 3;
}

.keyword-tag {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: #e8c49a;
  font-size: 9px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid rgba(200, 134, 13, 0.3);
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
}

.keyword-tag:hover {
  background: rgba(0, 0, 0, 0.7);
  border-color: #d4a574;
  transform: translateY(-1px);
}

.keyword-more {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  color: #a39d8e;
  font-size: 9px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.asset-info {
  padding: 12px;
  flex: 0 0 auto;
  z-index: 2;
  position: relative;
}

.asset-name {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #C8860D;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: relative;
  cursor: pointer;
}

.asset-name::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.2), transparent);
  transition: left 0.5s ease;
}

.asset-card:hover .asset-name::before {
  left: 100%;
}

/* 发行方信息样式 */
.issuer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.issuer-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(200, 134, 13, 0.3);
  flex-shrink: 0;
  overflow: hidden;
}

.issuer-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.issuer-default-icon {
  color: #d4a574;
  font-size: 10px;
}

.issuer-details {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.issuer-label {
  font-size: 10px;
  color: #a39d8e;
  font-weight: 500;
  flex-shrink: 0;
}

.issuer-name {
  font-size: 11px;
  color: #e8c49a;
  font-weight: 600;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 价格和限量信息容器 */
.price-and-limit {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
  gap: 8px;
}

.asset-price {
  font-size: 16px;
  font-weight: bold;
  color: #E8A317;
  flex-shrink: 0;
  background: linear-gradient(135deg, #E8A317 0%, #f4b942 50%, #E8A317 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  line-height: 1.2;
}

.asset-price::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.asset-price:hover::before {
  left: 100%;
}

/* 限量信息突出显示 */
.limited-quantity {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, rgba(212, 165, 116, 0.15), rgba(200, 134, 13, 0.15));
  border: 1px solid #d4a574;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(200, 134, 13, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.limited-quantity::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.limited-quantity:hover {
  background: linear-gradient(135deg, rgba(200, 134, 13, 0.2), rgba(200, 134, 13, 0.25));
  border-color: #e8c49a;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(200, 134, 13, 0.3);
}

.limited-quantity i {
  color: #d4a574;
  font-size: 10px;
  margin-right: 2px;
}

.limited-label {
  color: #b8956a;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quantity-number {
  color: #d4a574;
  font-weight: 800;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.quantity-unit {
  color: #b8956a;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .asset-info {
    padding: 10px;
  }
  
  .asset-name {
    font-size: 14px;
  }
  
  .issuer-info {
    padding: 4px 6px;
    margin-bottom: 6px;
  }
  
  .issuer-label {
    font-size: 9px;
  }
  
  .issuer-name {
    font-size: 10px;
  }
  
  .asset-price {
    font-size: 14px;
  }
  
  .limited-quantity {
    padding: 3px 6px;
    font-size: 10px;
  }
  
  .asset-type-tag {
    font-size: 9px;
    padding: 3px 6px;
  }
  
  /* 顶部信息容器移动端优化 */
  .asset-top-info {
    gap: 6px;
  }
}

@media (max-width: 480px) {
  .asset-image {
    height: 228px;
    font-size: 11px;
  }
  
  .asset-info {
    padding: 8px;
  }
  
  .asset-name {
    font-size: 13px;
    margin-bottom: 6px;
  }
  
  .issuer-info {
    padding: 3px 5px;
    margin-bottom: 5px;
    gap: 6px;
  }
  
  .issuer-avatar {
    width: 16px;
    height: 16px;
  }
  
  .issuer-label {
    font-size: 8px;
  }
  
  .issuer-name {
    font-size: 9px;
  }
  
  .asset-price {
    font-size: 13px;
  }
  
  .limited-quantity {
    padding: 2px 5px;
    font-size: 9px;
    gap: 3px;
  }
  
  .limited-quantity i {
    font-size: 8px;
  }
  
  .quantity-number {
    font-size: 10px;
  }
  
  /* 关键字标签移动端优化 */
  .keyword-tag {
    font-size: 8px;
    padding: 1px 4px;
  }
  
  .keyword-more {
    font-size: 8px;
    padding: 1px 4px;
  }
  
  /* 发行时间标签移动端优化 */
  .asset-sale-time {
    font-size: 8px;
    padding: 2px 4px;
  }
  
  .asset-sale-time i {
    font-size: 7px;
  }
  
  .asset-type-tag {
    font-size: 8px;
    padding: 2px 5px;
  }
  
  .price-and-limit {
    gap: 4px;
  }
}
</style> 