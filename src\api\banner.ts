/**
 * 轮播图相关API
 */
import request from './request'
import type { ApiResponse } from './types'

/**
 * 轮播图数据类型
 */
export interface BannerItem {
  id: number
  title: string
  description?: string
  imageUrl: string
  linkUrl?: string
  isActive: boolean
  sortOrder: number
  jumpType?: string      // 跳转类型：A-资产详情页，其他类型待定
  contentId?: string     // 内容ID：当jumpType为A时，表示资产ID
  createTime: string
  updateTime: string
}

/**
 * 轮播图API响应类型
 */
export interface BannerListResponse extends ApiResponse {
  data: BannerItem[]
}

/**
 * 轮播图API类
 */
class BannerAPI {
  /**
   * 获取轮播图列表
   * @returns 轮播图列表
   */
  async getBannerList(): Promise<BannerListResponse> {
    try {
      const response = await request.get<BannerListResponse>('/banner/banner/client/list')
      return response
    } catch (error) {
      console.error('获取轮播图列表失败:', error)
      throw error
    }
  }
}

// 导出单例
export default new BannerAPI() 