# AssetItemMore 类型定义和使用说明

## 新增类型定义

根据后端返回的数据结构，在 `src/api/exhibition.ts` 中新增了以下类型定义：

### 1. 枚举类型

```typescript
/**
 * 库存状态枚举
 */
export enum StockStatus {
  AVAILABLE = 'AVAILABLE',           // 有库存
  LOW_STOCK = 'LOW_STOCK',          // 库存不足
  OUT_OF_STOCK = 'OUT_OF_STOCK'     // 无库存
}

/**
 * 销售状态枚举
 */
export enum SaleStatus {
  ON_SALE = 'ON_SALE',              // 在售
  NOT_ON_SALE = 'NOT_ON_SALE',      // 未开售
  SOLD_OUT = 'SOLD_OUT'             // 售罄
}
```

### 2. 详细发行方信息类型

```typescript
/**
 * 详细发行方信息类型 - 对应后端完整返回结构
 */
export interface IssuerInfoMore {
  issuerId: number                   // 发行方ID
  issuerName: string                 // 发行方名称
  issuerLogo: string                 // 发行方Logo URL
  statusCd?: string | null           // 状态代码
  statusDate?: string | null         // 状态时间
  createStaff?: string | null        // 创建人
  createDate?: string | null         // 创建时间
  updateStaff?: string | null        // 修改人
  updateDate?: string | null         // 修改时间
  remark?: string | null             // 备注
}
```

### 3. 资产详细信息类型

```typescript
/**
 * 资产详细信息类型 - 对应后端完整返回结构
 */
export interface AssetItemMore {
  // 基础信息
  assetId: number                    // 资产ID
  assetName: string                  // 资产名称
  assetCover: string                 // 资产封面图URL
  assetCoverThumbnail: string        // 资产封面缩略图URL
  introImages: string                // 资产介绍图片URL
  assetDescription?: string | null   // 资产描述
  assetType: string                  // 资产类型
  isBlindBox: string                 // 是否盲盒 ("0"/"1")
  
  // 发行信息
  issueQuantity: number              // 发行数量
  individualLimit: number            // 个人限购数量
  enterpriseLimit: number            // 企业限购数量
  airdropQuantity: number            // 空投数量
  activityQuantity: number           // 活动数量
  issuePrice: number                 // 发行价格
  issueStartTime?: string | null     // 发行开始时间
  issueEndTime?: string | null       // 发行结束时间
  
  // 状态信息
  statusCd: string                   // 状态代码
  createDate: string                 // 创建时间
  
  // 发行方信息
  issuers: IssuerInfoMore[]          // 发行方信息列表
  
  // 系列和专区信息
  seriesName: string                 // 系列名称
  zoneName: string                   // 专区名称
  
  // 库存信息
  totalQuantity: number              // 总数量
  availableStock: number             // 可用库存
  soldCount: number                  // 已售数量
  stockStatus: StockStatus           // 库存状态
  stockUpdateTime: string            // 库存更新时间
  isSoldOut: boolean                 // 是否售罄
  stockPercentage: number            // 库存百分比
  
  // 购买状态
  canPurchase: boolean               // 是否可购买
  cannotPurchaseReason?: string | null // 不可购买原因
  saleStatus: SaleStatus             // 销售状态
}
```

## 使用示例

### 1. 在 Vue 组件中使用

```typescript
import { ref } from 'vue'
import { AssetItemMore, StockStatus, SaleStatus } from '@/api/exhibition'

// 定义响应式数据
const assetDetail = ref<AssetItemMore | null>(null)

// API 调用示例
const loadAssetDetail = async (assetId: number) => {
  try {
    const response = await fetch(`/api/assets/${assetId}`)
    const result = await response.json()
    
    if (result.code === 200) {
      assetDetail.value = result.data as AssetItemMore
    }
  } catch (error) {
    console.error('加载资产详情失败:', error)
  }
}

// 使用类型安全的状态判断
const isAvailable = computed(() => {
  return assetDetail.value?.stockStatus === StockStatus.AVAILABLE
})

const isOnSale = computed(() => {
  return assetDetail.value?.saleStatus === SaleStatus.ON_SALE
})
```

### 2. 在模板中使用

```vue
<template>
  <div v-if="assetDetail" class="asset-detail">
    <!-- 基础信息 -->
    <h1>{{ assetDetail.assetName }}</h1>
    <img :src="assetDetail.assetCover" :alt="assetDetail.assetName" />
    
    <!-- 发行信息 -->
    <div class="issue-info">
      <p>发行价格: ¥{{ assetDetail.issuePrice }}</p>
      <p>发行数量: {{ assetDetail.issueQuantity }}</p>
      <p>个人限购: {{ assetDetail.individualLimit }}份</p>
      <p>企业限购: {{ assetDetail.enterpriseLimit }}份</p>
    </div>
    
    <!-- 库存信息 -->
    <div class="stock-info">
      <p>总数量: {{ assetDetail.totalQuantity }}</p>
      <p>可用库存: {{ assetDetail.availableStock }}</p>
      <p>已售数量: {{ assetDetail.soldCount }}</p>
      <p>库存百分比: {{ assetDetail.stockPercentage }}%</p>
      
      <!-- 库存状态显示 -->
      <div class="stock-status" :class="getStockStatusClass(assetDetail.stockStatus)">
        {{ getStockStatusText(assetDetail.stockStatus) }}
      </div>
    </div>
    
    <!-- 发行方信息 -->
    <div class="issuers">
      <h3>发行方</h3>
      <div v-for="issuer in assetDetail.issuers" :key="issuer.issuerId" class="issuer">
        <img :src="issuer.issuerLogo" :alt="issuer.issuerName" />
        <span>{{ issuer.issuerName }}</span>
      </div>
    </div>
    
    <!-- 购买按钮 -->
    <button 
      v-if="assetDetail.canPurchase" 
      @click="handlePurchase"
      class="purchase-btn"
    >
      立即购买
    </button>
    <div v-else class="cannot-purchase">
      {{ assetDetail.cannotPurchaseReason || '暂不可购买' }}
    </div>
  </div>
</template>
```

### 3. 辅助函数示例

```typescript
import { StockStatus, SaleStatus } from '@/api/exhibition'

// 获取库存状态样式类
const getStockStatusClass = (status: StockStatus): string => {
  switch (status) {
    case StockStatus.AVAILABLE:
      return 'status-available'
    case StockStatus.LOW_STOCK:
      return 'status-low-stock'
    case StockStatus.OUT_OF_STOCK:
      return 'status-out-of-stock'
    default:
      return 'status-unknown'
  }
}

// 获取库存状态文本
const getStockStatusText = (status: StockStatus): string => {
  switch (status) {
    case StockStatus.AVAILABLE:
      return '有库存'
    case StockStatus.LOW_STOCK:
      return '库存不足'
    case StockStatus.OUT_OF_STOCK:
      return '无库存'
    default:
      return '未知状态'
  }
}

// 获取销售状态文本
const getSaleStatusText = (status: SaleStatus): string => {
  switch (status) {
    case SaleStatus.ON_SALE:
      return '在售中'
    case SaleStatus.NOT_ON_SALE:
      return '未开售'
    case SaleStatus.SOLD_OUT:
      return '已售罄'
    default:
      return '未知状态'
  }
}
```

## 类型安全优势

1. **编译时检查**: TypeScript 会在编译时检查类型错误
2. **智能提示**: IDE 提供完整的属性提示和自动补全
3. **重构安全**: 重命名属性时会自动更新所有引用
4. **文档化**: 类型定义本身就是最好的文档

## 与现有 AssetItem 的区别

- `AssetItem`: 通用的资产类型，包含兼容字段，适用于列表展示
- `AssetItemMore`: 详细的资产类型，严格对应后端返回结构，适用于详情页面

两个类型可以根据不同场景选择使用，确保类型安全和代码可维护性。
