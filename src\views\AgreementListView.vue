<template>
  <AppPageHeader :title="'协议与政策'" @back="$router.back()" />
  <div class="agreement-list-container" :style="{ paddingTop: '35px' }">
    <div class="agreement-list">
      <div class="agreement-item" @click="goTo('/privacy-policy')">
        <!-- <i class="fas fa-user-secret agreement-icon"></i> -->
        <span>隐私政策</span>
        <i class="fas fa-chevron-right right-arrow"></i>
      </div>
      <div class="agreement-item" @click="goTo('/user-agreement')">
        <!-- <i class="fas fa-file-alt agreement-icon"></i> -->
        <span>用户协议</span>
        <i class="fas fa-chevron-right right-arrow"></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import AppPageHeader from '@/components/AppPageHeader.vue'
const router = useRouter()
function goTo(path: string) {
  router.push(path)
}
</script>

<style scoped>
.agreement-list-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 24px 16px 80px;
  background: var(--gradient-bg, #232323);
  min-height: 100vh;
}
.agreement-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 24px;
}
.agreement-item {
  display: flex;
  align-items: center;
  background: var(--gradient-card, #242424);
  border-radius: 12px;
  padding: 18px 20px;
  color: #f8f6f0;
  font-size: 17px;
  cursor: pointer;
  transition: background 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.agreement-item:hover {
  background: #2e2e2e;
}
.agreement-icon {
  font-size: 22px;
  margin-right: 16px;
  color: #d4a574;
}
.right-arrow {
  margin-left: auto;
  color: #e8c49a;
  font-size: 16px;
}
</style> 