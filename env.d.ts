/// <reference types="vite/client" />

declare module 'three/examples/jsm/loaders/GLTFLoader.js' {
  export class GLTFLoader {
    load(
      url: string,
      onLoad: (gltf: any) => void,
      onProgress?: (progress: ProgressEvent) => void,
      onError?: (error: ErrorEvent) => void
    ): void
  }
}

declare module 'three/examples/jsm/controls/OrbitControls.js' {
  import { Camera, EventDispatcher, MOUSE, Object3D, TOUCH, Vector3 } from 'three'
  
  export class OrbitControls extends EventDispatcher {
    constructor(object: Camera, domElement?: HTMLElement)
    
    object: Camera
    domElement: HTMLElement | undefined
    
    enabled: boolean
    target: Vector3
    
    enableDamping: boolean
    dampingFactor: number
    
    enableZoom: boolean
    zoomSpeed: number
    
    enableRotate: boolean
    rotateSpeed: number
    
    enablePan: boolean
    panSpeed: number
    
    autoRotate: boolean
    autoRotateSpeed: number
    
    minDistance: number
    maxDistance: number
    
    minPolarAngle: number
    maxPolarAngle: number
    
    minAzimuthAngle: number
    maxAzimuthAngle: number
    
    enableKeys: boolean
    keys: { LEFT: string; UP: string; RIGHT: string; BOTTOM: string }
    
    mouseButtons: { LEFT: MOUSE; MIDDLE: MOUSE; RIGHT: MOUSE }
    touches: { ONE: TOUCH; TWO: TOUCH }
    
    update(): boolean
    dispose(): void
    reset(): void
    
    saveState(): void
    
    getPolarAngle(): number
    getAzimuthalAngle(): number
    getDistance(): number
  }
}
