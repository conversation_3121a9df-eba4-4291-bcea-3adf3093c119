<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
import { useRoute } from 'vue-router'
import { computed, ref, onMounted } from 'vue'
import NotificationContainer from '@/components/NotificationContainer.vue'

const route = useRoute()

// 检查是否是认证页面
const isAuthPage = computed(() => {
  return route.path === '/login' || route.path === '/register'
})

// Stagewise toolbar configuration (disabled to avoid 404 errors)
const stagewiseConfig = {
  plugins: [] as any[]
}

// Development mode check
const isDevelopment = import.meta.env.MODE === 'development'

// StagewiseToolbar component reference (disabled)
const StagewiseToolbar = ref<any>(null)

// Stagewise toolbar disabled to prevent API connection issues
// If you need to re-enable it, uncomment the code below and ensure Stagewise service is running
/*
onMounted(async () => {
  if (isDevelopment) {
    try {
      const toolbarModule = await import('@stagewise/toolbar-vue')
      StagewiseToolbar.value = toolbarModule.StagewiseToolbar
      
      // Try to load Vue plugin if available
      try {
        const pluginModule = await import('@stagewise-plugins/vue')
        if (pluginModule.default) {
          stagewiseConfig.plugins = [pluginModule.default]
        }
      } catch (pluginError) {
        console.warn('Vue plugin not available:', pluginError)
      }
    } catch (error) {
      console.warn('Failed to load Stagewise toolbar:', error)
    }
  }
})
*/
</script>

<template>
  <!-- Stagewise Toolbar (Disabled to prevent API connection issues) -->
  <!-- 
  <component 
    v-if="isDevelopment && StagewiseToolbar" 
    :is="StagewiseToolbar" 
    :config="stagewiseConfig" 
  />
  -->
  
  <!-- 认证页面直接显示RouterView，不显示导航 -->
  <div v-if="isAuthPage" class="auth-layout">
    <RouterView />
  </div>
  
  <!-- 普通页面显示完整的App布局 -->
  <div v-else class="app-layout">
    <main class="main-content">
      <RouterView />
    </main>
  </div>
  
  <!-- 全局通知容器 -->
  <NotificationContainer />
</template>

<style scoped>
.auth-layout {
  /* 认证页面占满全屏，但允许滚动 */
  width: 100%;
  min-height: 100vh;
  z-index: 1000;
  overflow-y: auto;
}

.app-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #0F0F15 0%, #1A1A25 50%, #252530 100%);
  color: #F8FAFC;
}

header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(37, 37, 48, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 2rem;
  background: linear-gradient(135deg, #C8860D, #E8A317);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.site-title {
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(135deg, #C8860D, #E8A317);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.nav-link {
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  color: #94A3B8;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #F8FAFC;
  background: rgba(255, 255, 255, 0.1);
}

.nav-link.router-link-active {
  color: #E8A317;
  background: rgba(200, 134, 13, 0.1);
}

.auth-btn {
  background: linear-gradient(135deg, #7C3AED, #A855F7);
  color: white !important;
  font-weight: 600;
}

.auth-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(124, 58, 237, 0.3);
}

.register-btn {
  background: linear-gradient(135deg, #C8860D, #E8A317);
  color: white !important;
  font-weight: 600;
}

.register-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.3);
}

.main-content {
  min-height: 100vh;
  padding: 0;
}

@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 1rem;
    padding: 1rem;
  }
  
  .site-title {
    font-size: 1.2rem;
  }
  
  .main-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }
  
  .nav-link {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}
</style>
