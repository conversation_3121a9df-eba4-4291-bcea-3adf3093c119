<template>
  <AppPageHeader :title="'我的权益券'" @back="$router.back()" />
  <div class="coupon-list-page" :style="{ paddingTop: '40px' }">

    <main class="main-content">
      <div v-if="loading" class="loading">加载中...</div>
      <div v-else-if="couponList.length === 0" class="empty">暂无权益券</div>
      <div v-else class="coupon-list">
        <div v-for="coupon in couponList" :key="coupon.couponCode" class="coupon-card" :class="statusClass(coupon)">
          <div class="coupon-image-area">
            <img v-if="coupon.couponCover" :src="coupon.couponCover" class="coupon-cover" :alt="coupon.couponName" />
            <div v-else class="cover-placeholder"><i class="fas fa-ticket-alt"></i></div>
            <div class="coupon-info-float">
              <div class="coupon-title">{{ coupon.couponName }}</div>
              <div class="coupon-code">编号：{{ coupon.couponCode }}</div>
              <div class="coupon-meta">
                <span class="coupon-date">有效期：{{ formatDate(coupon.startTime) }} ~ {{ formatDate(coupon.endTime) }}</span>
                <span class="coupon-status">{{ statusText(coupon) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { listUserCoupon, type CouponListItem } from '@/api/coupon'
import AppPageHeader from '@/components/AppPageHeader.vue'

const router = useRouter()
const loading = ref(false)
const couponList = ref<CouponListItem[]>([])

const goBack = () => router.back()

const formatDate = (date: string) => {
  if (!date) return ''
  return date.split(' ')[0]
}

const statusText = (coupon: CouponListItem) => {
  // if (coupon.isUsed === '1' || coupon.isUsed === 1) return '已使用'
  if (coupon.endTime < new Date().toISOString()) return '已过期'
  return '未使用'
}

const statusClass = (coupon: CouponListItem) => {
  // if (coupon.isUsed === '1' || coupon.isUsed === 1) return 'used'
  if (coupon.endTime < new Date().toISOString()) return 'expired'
  return 'active'
}

const loadCoupons = async () => {
  loading.value = true
  try {
    const res = await listUserCoupon({ pageNum: 1, pageSize: 20 })
    if (Array.isArray(res.data)) {
      couponList.value = res.data
    } else {
      couponList.value = []
    }
  } catch (err) {
    couponList.value = []
    console.error('加载权益券失败', err)
  } finally {
    loading.value = false
  }
}

onMounted(loadCoupons)
</script>

<style scoped>
.coupon-list-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #232323 0%, #181818 100%);
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 16px 10px 8px;
  background: #232323;
  border-bottom: 1px solid #2e2e2e;
}
.back-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255,255,255,0.08);
  border: none;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.2s;
}
.back-btn:hover {
  background: #d4a574;
  color: #232323;
}
.page-title {
  font-size: 20px;
  font-weight: 700;
  letter-spacing: 1px;
}
.header-placeholder {
  width: 40px;
}
.main-content {
  padding: 18px 12px 32px 12px;
}
.loading, .empty {
  text-align: center;
  color: #c4c0b1;
  font-size: 16px;
  margin-top: 60px;
}
.coupon-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.coupon-card {
  background: transparent;
  border-radius: 18px;
  box-shadow: 0 4px 18px rgba(212,165,116,0.08);
  overflow: hidden;
  border: none;
  min-height: 180px;
  position: relative;
  padding: 0;
}
.coupon-card.active {
  box-shadow: 0 4px 18px rgba(212,165,116,0.18);
}
.coupon-card.used {
  opacity: 0.6;
  filter: grayscale(0.2);
}
.coupon-card.expired {
  opacity: 0.5;
  filter: grayscale(0.5);
}
.coupon-image-area {
  position: relative;
  width: 100%;
  height: 180px;
  background: #181818;
  display: flex;
  align-items: stretch;
  justify-content: center;
  border-radius: 18px;
  overflow: hidden;
}
.coupon-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}
.cover-placeholder {
  width: 100%;
  height: 100%;
  background: #232323;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d4a574;
  font-size: 48px;
}
.coupon-info-float {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 18px 18px 14px 18px;
  background: linear-gradient(0deg, rgba(30,30,30,0.92) 80%, rgba(30,30,30,0.5) 40%, rgba(30,30,30,0.0) 0%);
  color: #fff;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  gap: 6px;
}
.coupon-title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 2px;
  color: #fff;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}
.coupon-code {
  font-size: 13px;
  color: #d4a574;
  margin-bottom: 2px;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}
.coupon-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #e0ddd4;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18);
}
.coupon-status {
  font-weight: 600;
  font-size: 13px;
  margin-left: 8px;
}
@media (max-width: 600px) {
  .coupon-card {
    min-height: 120px;
    border-radius: 12px;
  }
  .coupon-image-area {
    height: 215px;
    border-radius: 12px;
  }
  .coupon-info-float {
    padding: 10px 10px 8px 10px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }
  .coupon-title {
    font-size: 15px;
  }
}
</style> 