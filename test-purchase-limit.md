# 购买确认页面限购功能测试

## 功能说明
修改了 `PurchaseConfirmView.vue` 页面，使其能够根据用户身份（个人/企业）和商品的限购设置来动态显示限购数量。

## 主要修改内容

### 1. 添加用户身份相关状态
```typescript
const userIdentity = ref<'person' | 'enterprise' | null>(null) // 用户身份类型
const userIdentityLoading = ref(false) // 用户身份加载状态
```

### 2. 添加动态限购数量计算
```typescript
const purchaseLimit = computed(() => {
  if (!asset.value) return 10 // 默认限购数量
  
  // 如果用户身份未加载完成，使用默认值
  if (userIdentity.value === null) return 10
  
  // 根据用户身份返回对应的限购数量
  if (userIdentity.value === 'enterprise') {
    const limit = asset.value.enterpriseLimit || 10
    console.log('企业用户限购数量:', limit)
    return limit
  } else {
    const limit = asset.value.individualLimit || 10
    console.log('个人用户限购数量:', limit)
    return limit
  }
})
```

### 3. 添加获取用户身份方法
```typescript
const getUserIdentity = async () => {
  try {
    userIdentityLoading.value = true
    const response = await UserAPI.getVerificationStatus()
    
    if (response.code === 200 && response.data) {
      userIdentity.value = response.data.identifyType || 'person'
      console.log('用户身份类型:', userIdentity.value)
    } else {
      // 如果获取失败，默认为个人用户
      userIdentity.value = 'person'
      console.warn('获取用户身份失败，默认为个人用户')
    }
  } catch (err: unknown) {
    console.error('获取用户身份失败:', err)
    // 发生错误时，默认为个人用户
    userIdentity.value = 'person'
  } finally {
    userIdentityLoading.value = false
  }
}
```

### 4. 修改模板中的硬编码值
- 输入框最大值：`:max="purchaseLimit"`
- 增加按钮禁用条件：`:disabled="purchaseQuantity >= purchaseLimit"`
- 提示文字：动态显示用户身份和限购数量

### 5. 修改验证逻辑
所有涉及硬编码数量10的地方都改为使用动态的 `purchaseLimit.value`

## 测试场景

### 场景1：个人用户
1. 用户身份为 'person'
2. 商品 `individualLimit` 为 5
3. 预期：限购数量显示为5，最多可购买5份

### 场景2：企业用户
1. 用户身份为 'enterprise'
2. 商品 `enterpriseLimit` 为 20
3. 预期：限购数量显示为20，最多可购买20份

### 场景3：未设置限购数量
1. 商品未设置 `individualLimit` 或 `enterpriseLimit`
2. 预期：使用默认限购数量10

### 场景4：用户身份获取失败
1. API调用失败或返回错误
2. 预期：默认为个人用户，使用个人限购数量

## 验证方法

1. 打开浏览器开发者工具的控制台
2. 访问购买确认页面
3. 查看控制台输出的用户身份和限购数量信息
4. 测试数量选择器的最大值限制
5. 验证提示文字是否正确显示用户身份和限购数量

## 注意事项

1. 页面加载时会同时获取商品详情和用户身份信息
2. 如果用户身份获取失败，会默认为个人用户
3. 如果商品未设置限购数量，会使用默认值10
4. 所有的限购逻辑都是响应式的，会根据数据变化自动更新UI
