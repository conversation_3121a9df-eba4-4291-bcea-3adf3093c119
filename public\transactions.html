<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>交易记录 - 四川省数字资产发行平�?/title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sichuan-red': '#d4313b',
                        'sichuan-gold': '#daa520',
                        'sichuan-dark': '#1a1a1a',
                        'sichuan-gray': '#2a2a2a'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0f0f0;
        }
        .glass-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-sichuan-dark to-sichuan-gray">
    <!-- 顶部导航�?-->
    <header class="sticky top-0 z-50 backdrop-blur-lg bg-black/30 border-b border-white/10">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="profile.html" class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <h1 class="text-lg font-bold text-white">交易记录</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-filter text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-md mx-auto px-4 pb-20">
        <!-- 交易统计 -->
        <section class="py-6">
            <div class="glass-card rounded-2xl p-6">
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-green-400 mb-1">15</div>
                        <div class="text-white/60 text-sm">成功交易</div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-sichuan-gold mb-1">�?,890</div>
                        <div class="text-white/60 text-sm">总消�?/div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-blue-400 mb-1">3</div>
                        <div class="text-white/60 text-sm">转让次数</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选选项 -->
        <section class="mb-6">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="whitespace-nowrap px-4 py-2 bg-sichuan-gold text-white rounded-full text-sm font-medium">
                    全部交易
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    购买记录
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    转让记录
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    退款记�?
                </button>
            </div>
        </section>

        <!-- 交易记录列表 -->
        <section class="space-y-4">
            <!-- 交易记录1 - 购买 -->
            <div class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">太阳神鸟金饰</h3>
                                <p class="text-white/60 text-sm">购买 #0892</p>
                            </div>
                            <div class="text-right">
                                <div class="text-green-400 font-bold">-�?99.00</div>
                                <div class="text-green-400 text-xs">交易成功</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>订单�? 202401150001</span>
                            <span>2024-01-15 10:30</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded-full hover:bg-gray-600 transition-colors">
                                申请转让
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录2 - 转让 -->
            <div class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-exchange-alt text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">三星堆青铜面�?/h3>
                                <p class="text-white/60 text-sm">转让�?user***123</p>
                            </div>
                            <div class="text-right">
                                <div class="text-orange-400 font-bold">+�?50.00</div>
                                <div class="text-orange-400 text-xs">转让成功</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>转让单号: T202401140002</span>
                            <span>2024-01-14 16:20</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <button class="text-xs bg-purple-500 text-white px-3 py-1 rounded-full hover:bg-purple-600 transition-colors">
                                评价买家
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录3 - 购买 -->
            <div class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">金沙遗址全景</h3>
                                <p class="text-white/60 text-sm">购买 #0156</p>
                            </div>
                            <div class="text-right">
                                <div class="text-green-400 font-bold">-�?99.00</div>
                                <div class="text-green-400 text-xs">交易成功</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>订单�? 202401130003</span>
                            <span>2024-01-13 14:15</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded-full hover:bg-gray-600 transition-colors">
                                申请转让
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录4 - 退�?-->
            <div class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-undo text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">蜀锦织品纹�?/h3>
                                <p class="text-white/60 text-sm">申请退�?/p>
                            </div>
                            <div class="text-right">
                                <div class="text-red-400 font-bold">+�?9.00</div>
                                <div class="text-red-400 text-xs">退款成�?/div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>退款单�? R202401120004</span>
                            <span>2024-01-12 09:45</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <span class="text-xs text-white/50">退款原�? 质量问题</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录5 - 购买 -->
            <div class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-shopping-cart text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">古蜀玉器收藏</h3>
                                <p class="text-white/60 text-sm">购买 #0067</p>
                            </div>
                            <div class="text-right">
                                <div class="text-green-400 font-bold">-�?59.00</div>
                                <div class="text-green-400 text-xs">交易成功</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>订单�? 202401100005</span>
                            <span>2024-01-10 11:20</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded-full hover:bg-gray-600 transition-colors">
                                申请转让
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 交易记录6 - 待确�?-->
            <div class="glass-card rounded-2xl p-4 hover-scale opacity-75">
                <div class="flex items-start space-x-3">
                    <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-white font-semibold">巴蜀印章系列</h3>
                                <p class="text-white/60 text-sm">等待确认</p>
                            </div>
                            <div class="text-right">
                                <div class="text-yellow-400 font-bold">-�?29.00</div>
                                <div class="text-yellow-400 text-xs">处理�?/div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between text-white/50 text-xs">
                            <span>订单�? 202401090006</span>
                            <span>2024-01-09 15:30</span>
                        </div>
                        <div class="mt-3 flex items-center space-x-2">
                            <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors">
                                查看详情
                            </button>
                            <button class="text-xs bg-red-500 text-white px-3 py-1 rounded-full hover:bg-red-600 transition-colors">
                                取消订单
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 月度汇�?-->
        <section class="mt-8">
            <h3 class="text-white font-semibold mb-4">本月汇�?/h3>
            <div class="glass-card rounded-2xl p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-white/70">购买次数</span>
                        <span class="text-white font-semibold">12�?/span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-white/70">转让收入</span>
                        <span class="text-green-400 font-semibold">+�?50.00</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-white/70">消费支出</span>
                        <span class="text-red-400 font-semibold">-�?,240.00</span>
                    </div>
                    <div class="border-t border-white/20 pt-4">
                        <div class="flex items-center justify-between">
                            <span class="text-white font-semibold">净支出</span>
                            <span class="text-sichuan-red font-bold text-lg">-�?90.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 加载更多 -->
        <section class="mt-8 text-center">
            <button class="glass-card px-8 py-3 rounded-full text-white/70 hover:text-white transition-colors">
                <i class="fas fa-spinner mr-2"></i>查看更多交易
            </button>
        </section>
    </main>

    <!-- 底部导航�?-->
    <nav class="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-lg bg-black/30 border-t border-white/10">
        <div class="max-w-md mx-auto px-4 py-2">
            <div class="flex items-center justify-around">
                <a href="index.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">首页</span>
                </a>
                <a href="market.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-store text-lg mb-1"></i>
                    <span class="text-xs">市场</span>
                </a>
                <a href="collection.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-gem text-lg mb-1"></i>
                    <span class="text-xs">收藏</span>
                </a>
                <a href="profile.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-user text-lg mb-1"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 筛选功�?
        document.querySelectorAll('section:nth-child(3) button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有active状�?
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.classList.remove('bg-sichuan-gold', 'text-white');
                    btn.classList.add('glass-card', 'text-white/70');
                });
                
                // 添加active状�?
                this.classList.remove('glass-card', 'text-white/70');
                this.classList.add('bg-sichuan-gold', 'text-white');
            });
        });

        // 查看详情按钮
        document.querySelectorAll('button:contains("查看详情")').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                alert('跳转到交易详情页�?);
            });
        });

        // 申请转让按钮
        document.querySelectorAll('button:contains("申请转让")').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                const itemName = this.closest('.glass-card').querySelector('h3').textContent;
                if (confirm(`确定要转�?${itemName}"吗？`)) {
                    alert('转让申请已提�?);
                }
            });
        });

        // 取消订单按钮
        document.querySelectorAll('button:contains("取消订单")').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                if (confirm('确定要取消此订单吗？')) {
                    alert('订单已取�?);
                    this.closest('.glass-card').style.opacity = '0.5';
                }
            });
        });

        // 评价买家按钮
        document.querySelectorAll('button:contains("评价买家")').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                alert('跳转到评价页�?);
            });
        });

        // 搜索功能
        document.querySelector('.fa-search').parentElement.addEventListener('click', function() {
            const query = prompt('请输入搜索关键词�?);
            if (query) {
                alert(`搜索交易记录�?{query}`);
            }
        });

        // 筛选功�?
        document.querySelector('.fa-filter').parentElement.addEventListener('click', function() {
            alert('打开筛选选项');
        });

        // 查看更多交易
        document.querySelector('button:contains("查看更多交易")').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加载�?..';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-spinner mr-2"></i>查看更多交易';
                alert('已加载更多交易记�?);
            }, 2000);
        });
    </script>
</body>
</html> 
