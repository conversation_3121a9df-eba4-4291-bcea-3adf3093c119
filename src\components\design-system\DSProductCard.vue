<template>
  <div class="product-card">
    <div class="product-image">
      <slot name="image">
        <div>{{ imagePlaceholder }}</div>
      </slot>
      <div v-if="tag" class="product-tag">{{ tag }}</div>
    </div>
    <div class="product-info">
      <div class="product-name">{{ name }}</div>
      <div v-if="tags.length > 0" class="tags">
        <DSTag v-for="tagItem in tags" :key="tagItem.text" :type="tagItem.type">
          {{ tagItem.text }}
        </DSTag>
      </div>
      <div class="price">
        {{ price }}
        <span v-if="saleTime" class="sale-time">{{ saleTime }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DSTag from './DSTag.vue'

interface TagItem {
  text: string
  type?: 'primary' | 'accent' | 'success' | 'warning'
}

interface Props {
  name: string
  price: string
  tag?: string
  saleTime?: string
  imagePlaceholder?: string
  tags?: TagItem[]
}

const props = withDefaults(defineProps<Props>(), {
  imagePlaceholder: '产品图片',
  tags: () => []
})
</script>

<style scoped>
.product-card {
  background: linear-gradient(145deg, var(--bg-card), var(--bg-card-hover));
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-card);
  transition: all 0.3s ease;
  border: 1px solid var(--border-primary);
  max-width: 100%;
  margin: 0 auto;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-hover);
  border-color: var(--border-hover);
}

.product-image {
  width: 100%;
  height: 140px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-tertiary);
  font-size: 13px;
}

.product-tag {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  background: var(--accent-color);
  color: var(--text-primary);
  font-size: 11px;
  padding: 3px var(--spacing-sm);
  border-radius: var(--radius-xxl);
  font-weight: 500;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.product-info {
  padding: var(--spacing-md);
}

.product-name {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  line-height: 1.4;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.price {
  font-size: 16px;
  font-weight: bold;
  color: var(--accent-color);
  display: flex;
  align-items: center;
  margin-top: var(--spacing-xs);
}

.sale-time {
  font-size: 11px;
  color: var(--text-tertiary);
  background: rgba(100, 80, 60, 0.3);
  padding: 3px var(--spacing-sm);
  border-radius: var(--radius-lg);
  margin-left: auto;
}
</style> 