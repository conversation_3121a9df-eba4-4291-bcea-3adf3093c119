<template>
  <div v-if="visible" class="prize-result-modal-overlay" @click="handleOverlayClick">
    <div class="prize-result-modal" @click.stop>
      <!-- 弹窗头部 -->
      <div class="modal-header">
        <h2 class="modal-title">
          <i class="fas fa-trophy"></i>
          中奖结果
        </h2>
        <button class="close-btn" @click="$emit('close')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="modal-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <div class="loading-text">查询中奖结果中...</div>
        </div>

        <!-- 中奖结果 -->
        <div v-else-if="prizeResults.length > 0" class="prize-results">
          <div class="result-summary">
            <div class="congratulations">
              <i class="fas fa-star"></i>
              恭喜您中奖了！
            </div>
            <div class="prize-count">共获得 {{ prizeResults.length }} 个奖品</div>
          </div>

          <div class="prize-list">
            <div
              v-for="prize in prizeResults"
              :key="prize.couponCodeId"
              class="prize-item"
            >
              <div class="prize-cover">
                <img :src="prize.couponCover || '/images/default-prize.jpg'" :alt="prize.couponName" />
                <!-- <div class="prize-status" :class="getStatusClass(prize.statusCd)">
                  {{ getStatusText(prize.statusCd) }}
                </div> -->
              </div>

              <div class="prize-info">
                <h3 class="prize-name">{{ prize.couponName }}</h3>
                <p class="prize-description">{{ prize.description }}</p>

                <div class="prize-details">
                  <div class="detail-item">
                    <span class="detail-label">奖品编号：</span>
                    <span class="detail-value">{{ prize.couponCode }}</span>
                  </div>

                  <!-- <div class="detail-item">
                    <span class="detail-label">获得时间：</span>
                    <span class="detail-value">{{ formatDateTime(prize.generateTime) }}</span>
                  </div> -->

                  <div class="detail-item">
                    <span class="detail-label">有效期：</span>
                    <span class="detail-value">{{ formatDateTime(prize.startTime) }} 至 {{ formatDateTime(prize.endTime) }}</span>
                  </div>

                  <div v-if="prize.isUsed === '1'" class="detail-item">
                    <span class="detail-label">使用时间：</span>
                    <span class="detail-value">{{ formatDateTime(prize.useTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 未中奖状态 -->
        <div v-else class="no-prize-state">
          <div class="no-prize-icon">
            <i class="fas fa-heart-broken"></i>
          </div>
          <div class="no-prize-text">很遗憾，您没有中奖</div>
          <div class="no-prize-subtitle">下次活动再接再厉！</div>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="modal-footer">
        <button class="confirm-btn" @click="$emit('close')">
          确定
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useNotification } from '@/composables/useNotification'
import ActivityAPI, { type PrizeResultItem } from '@/api/activity'

// Props
interface Props {
  visible: boolean
  activityId?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  activityId: 0
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// 响应式数据
const loading = ref(false)
const prizeResults = ref<PrizeResultItem[]>([])

// 通知
const { error } = useNotification()

// 方法
const handleOverlayClick = () => {
  emit('close')
}

/**
 * 获取状态文本
 */
const getStatusText = (statusCd: string): string => {
  const statusMap: Record<string, string> = {
    'VALID': '有效',
    'USED': '已使用',
    'EXPIRED': '已过期',
    'INVALID': '无效'
  }
  return statusMap[statusCd] || '未知状态'
}

/**
 * 获取状态样式类
 */
const getStatusClass = (statusCd: string): string => {
  const statusClassMap: Record<string, string> = {
    'VALID': 'status-valid',
    'USED': 'status-used',
    'EXPIRED': 'status-expired',
    'INVALID': 'status-invalid'
  }
  return statusClassMap[statusCd] || 'status-unknown'
}

/**
 * 格式化时间
 */
const formatDateTime = (datetime: string): string => {
  if (!datetime) return ''
  return ActivityAPI.formatDateTime(datetime)
}

/**
 * 加载中奖结果
 */
const loadPrizeResult = async () => {
  if (!props.activityId) return

  try {
    loading.value = true
    const response = await ActivityAPI.getPrizeResult(props.activityId)

    if (response.code === 200) {
      prizeResults.value = response.data || []
    } else {
      error(response.msg || '查询中奖结果失败')
    }
  } catch (err: any) {
    console.error('查询中奖结果失败:', err)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    if (err?.code === 401 || err?.status === 401) {
      // 401错误时不设置任何数据，保持空状态
      prizeResults.value = []
      return
    }

    error(err.message || '查询中奖结果失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 监听弹窗显示状态
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.activityId) {
    loadPrizeResult()
  }
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);

  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;

  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;

  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);

  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
}

/* 弹窗遮罩 */
.prize-result-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

/* 弹窗主体 */
.prize-result-modal {
  background: var(--gradient-card);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 90vw;
  max-height: 90vh;
  width: 400px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 弹窗头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.modal-title i {
  color: var(--primary-color);
  font-size: 20px;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.close-btn:hover {
  background: var(--primary-alpha);
  color: var(--primary-color);
}

/* 弹窗内容 */
.modal-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(212, 165, 116, 0.2);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* 中奖结果 */
.prize-results {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.result-summary {
  text-align: center;
  padding: 20px;
  background: var(--gradient-primary);
  border-radius: 12px;
  color: white;
}

.congratulations {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.congratulations i {
  color: #ffd700;
  font-size: 24px;
}

.prize-count {
  font-size: 14px;
  opacity: 0.9;
}

/* 奖品列表 */
.prize-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.prize-item {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 16px;
}

.prize-cover {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.prize-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.prize-status {
  position: absolute;
  top: 4px;
  right: 4px;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 600;
  color: white;
}

.status-valid {
  background: var(--success-color);
}

.status-used {
  background: var(--info-color);
}

.status-expired {
  background: var(--warning-color);
}

.status-invalid {
  background: var(--error-color);
}

/* 奖品信息 */
.prize-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.prize-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.3;
}

.prize-description {
  font-size: 12px;
  color: var(--text-tertiary);
  margin: 0;
  line-height: 1.4;
}

.prize-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  font-size: 11px;
  line-height: 1.4;
}

.detail-label {
  color: var(--text-muted);
  min-width: 60px;
  flex-shrink: 0;
}

.detail-value {
  color: var(--text-secondary);
  flex: 1;
  word-break: break-all;
}

/* 未中奖状态 */
.no-prize-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
}

.no-prize-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-muted);
}

.no-prize-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.no-prize-subtitle {
  font-size: 14px;
  color: var(--text-tertiary);
}

/* 弹窗底部 */
.modal-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.confirm-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.confirm-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .prize-result-modal {
    width: 95vw;
    max-height: 85vh;
  }

  .prize-item {
    flex-direction: column;
    gap: 12px;
  }

  .prize-cover {
    width: 100%;
    height: 120px;
  }

  .prize-details {
    gap: 6px;
  }

  .detail-item {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .modal-header {
    padding: 16px;
  }

  .modal-content {
    padding: 16px;
  }

  .modal-footer {
    padding: 16px;
  }

  .congratulations {
    font-size: 18px;
  }

  .prize-name {
    font-size: 14px;
  }
}
</style>
