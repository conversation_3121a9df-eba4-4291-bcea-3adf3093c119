<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>凌云数资</title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            
            /* 主色系 - 基于太阳神鸟的金色演化 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 时尚科技紫 */
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            /* 背景色系 - 现代深色调 */
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #fff;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #e0e0e0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #daa520;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 24px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            min-width: 120px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            background: var(--gradient-primary);
        }
        
        .btn-secondary {
            background: rgba(40, 40, 40, 0.7);
            color: #e0e0e0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(60, 60, 60, 0.7);
            border-color: #daa520;
        }
        
        .btn-small {
            padding: 6px 16px;
            font-size: 13px;
            min-width: auto;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
            border: 1px solid var(--primary-gold);
        }
        
        .tag-accent {
            background: rgba(220, 38, 38, 0.1);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }
        
        .tag-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }
        
        .tag-warning {
            background: rgba(234, 88, 12, 0.1);
            color: var(--accent-orange);
            border: 1px solid var(--accent-orange);
        }
        
        .product-card {
            background: var(--gradient-card);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-gold);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-gold);
            font-size: 13px;
        }
        
        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--accent-red);
            color: white;
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            z-index: 2;
            box-shadow: var(--shadow-sm);
        }
        
        .product-tag.on-sale {
            background: var(--accent-green);
        }
        
        .product-tag.limited {
            background: var(--primary-gold);
        }
        
        .product-tag.sold-out {
            background: var(--neutral-600);
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--neutral-100);
            line-height: 1.4;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-gold);
            margin: 4px 0;
        }
        
        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }
        
        .sale-time {
            font-size: 11px;
            color: var(--neutral-400);
            background: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .product-stats {
            font-size: 11px;
            color: var(--neutral-400);
        }
        
        /* 保持price类名兼容性 */
        .price {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-gold);
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-gold);
        }
        
        .nav-buttons {
            display: flex;
            gap: 12px;
        }
        
        .nav-btn {
            background: none;
            border: none;
            color: var(--neutral-100);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .nav-btn:hover {
            color: var(--primary-gold-light);
        }
        
        .search-section {
            padding: 20px 0;
        }
        
        .search-bar {
            display: flex;
            background: rgba(30, 30, 30, 0.8);
            border-radius: 24px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .search-dropdown {
            background: none;
            border: none;
            color: var(--neutral-100);
            padding: 12px 16px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            outline: none;
            font-size: 14px;
        }
        
        .search-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--neutral-100);
            padding: 12px 16px;
            outline: none;
            font-size: 14px;
        }
        
        .search-input::placeholder {
            color: var(--neutral-400);
        }
        
        .search-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .search-btn:hover {
            background: var(--gradient-primary);
        }
        
        .banner-section {
            margin: 20px 0;
        }
        
        .banner-slider {
            background: linear-gradient(135deg, #1a2b3c, #0d1a26);
            border-radius: 16px;
            padding: 30px 20px;
            text-align: center;
            border: 1px solid rgba(218, 165, 32, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .banner-slider::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(218, 165, 32, 0.1) 0%, transparent 70%);
            z-index: 0;
        }
        
        .banner-content {
            position: relative;
            z-index: 1;
        }
        
        .section {
            margin: 30px 0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-gold-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-title i {
            color: var(--primary-gold);
        }
        
        .section-more {
            color: var(--primary-gold);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s;
        }
        
        .section-more:hover {
            color: var(--primary-gold-light);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--neutral-400);
            transition: color 0.3s;
            padding: 8px 4px;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-item:hover {
            color: var(--primary-gold-light);
        }
        
        .nav-icon {
            font-size: 18px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 12px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        /* 响应式调整 */
        @media (max-width: 380px) {
            .products-grid {
                gap: 12px;
            }
            
            .product-image {
                height: 140px;
            }
            
            .container {
                padding: 0 8px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-bar">
                <div class="logo">
                    <i class="fas fa-mountain-sun"></i>
                    <span>川蜀数资</span>
                </div>
                <div class="nav-buttons">
                    <button class="nav-btn">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="nav-btn">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="nav-btn">
                        <i class="fas fa-user-circle"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 搜索栏 -->
            <section class="search-section">
                <div class="search-bar">
                    <select class="search-dropdown">
                        <option>名称</option>
                        <option>关键字</option>
                        <option>发行方</option>
                    </select>
                    <input type="text" class="search-input" placeholder="搜索数字资产...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- Banner轮播 -->
            <section class="banner-section">
                <div class="banner-slider">
                    <div class="banner-content">
                        <h2 class="text-large text-primary">太阳神鸟限量发行</h2>
                        <p class="text-normal">古蜀文明数字藏品，限量1000份</p>
                        <button class="btn btn-primary" style="margin-top: 12px;">立即查看</button>
                    </div>
                </div>
            </section>

            <!-- 即将发售 -->
            <section class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        即将发售
                    </h3>
                    <a href="exhibition.html" class="section-more">查看全部 →</a>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag limited">即将开售</div>
                            太阳神鸟
                        </div>
                        <div class="product-info">
                            <div class="product-name">金沙太阳神鸟</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">限量发行</span>
                                <span class="tag tag-accent">稀有</span>
                            </div>
                            <div class="product-price">¥299.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2024.01.10</div>
                                <div class="product-stats">限量 1000 份</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag on-sale">预售中</div>
                            青铜面具
                        </div>
                        <div class="product-info">
                            <div class="product-name">三星堆青铜面具</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">考古文物</span>
                                <span class="tag tag-accent">热门</span>
                            </div>
                            <div class="product-price">¥199.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2024.01.11</div>
                                <div class="product-stats">已售 245/500</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag">精品</div>
                            金沙遗址
                        </div>
                        <div class="product-info">
                            <div class="product-name">金沙遗址3D全景</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">3D模型</span>
                                <span class="tag tag-warning">预约</span>
                            </div>
                            <div class="product-price">¥399.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2024.01.15</div>
                                <div class="product-stats">预约 156/200</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            蜀锦纹样
                        </div>
                        <div class="product-info">
                            <div class="product-name">蜀锦织品纹样</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">传统工艺</span>
                                <span class="tag tag-success">在售</span>
                            </div>
                            <div class="product-price">¥89.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2024.01.18</div>
                                <div class="product-stats">已售 567/800</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 热门推荐 -->
            <section class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-fire"></i>
                        热门推荐
                    </h3>
                    <a href="exhibition.html" class="section-more">查看更多 →</a>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag on-sale">热销</div>
                            古蜀玉器
                        </div>
                        <div class="product-info">
                            <div class="product-name">古蜀玉器收藏</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">文物复刻</span>
                                <span class="tag tag-accent">精品</span>
                            </div>
                            <div class="product-price">¥159.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2023.12.28</div>
                                <div class="product-stats">已售 567/850</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag">珍藏</div>
                            巴蜀印章
                        </div>
                        <div class="product-info">
                            <div class="product-name">巴蜀印章系列</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">历史文物</span>
                                <span class="tag tag-success">在售</span>
                            </div>
                            <div class="product-price">¥129.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2023.12.25</div>
                                <div class="product-stats">已售 315/700</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag limited">联名</div>
                            文物复刻
                        </div>
                        <div class="product-info">
                            <div class="product-name">博物院联名款</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-accent">限定版</span>
                                <span class="tag tag-warning">稀有</span>
                            </div>
                            <div class="product-price">¥299.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2023.12.30</div>
                                <div class="product-stats">已售 89/300</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div class="product-tag on-sale">音频</div>
                            数字音乐
                        </div>
                        <div class="product-info">
                            <div class="product-name">古蜀乐器演奏</div>
                            <div style="margin: 4px 0;">
                                <span class="tag tag-primary">音乐文化</span>
                                <span class="tag tag-success">热门</span>
                            </div>
                            <div class="product-price">¥59.00</div>
                            <div class="product-meta">
                                <div class="sale-time">2023.12.20</div>
                                <div class="product-stats">已售 1204/1500</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item active">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 搜索功能
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchType = document.querySelector('.search-dropdown').value;
            const searchText = document.querySelector('.search-input').value;
            
            if (searchText.trim()) {
                alert(`按${searchType}搜索: ${searchText}`);
                // 这里可以跳转到搜索结果页面
            }
        });

        // 搜索框回车事件
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // 产品卡片点击事件
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                const productName = this.querySelector('.product-name').textContent;
                alert(`查看${productName}详情`);
                // 这里可以跳转到详情页面
                // window.location.href = 'asset-detail.html?name=' + encodeURIComponent(productName);
            });
        });

        // 添加点击效果
        document.querySelectorAll('.product-card, .btn, .nav-btn').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 导航按钮点击事件
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon.classList.contains('fa-user-circle')) {
                    window.location.href = 'login.html';
                } else if (icon.classList.contains('fa-bell')) {
                    window.location.href = 'notifications.html';
                } else if (icon.classList.contains('fa-search')) {
                    alert('打开搜索页面');
                }
            });
        });

        // Banner 按钮点击事件
        document.querySelector('.banner-slider .btn').addEventListener('click', function() {
            window.location.href = 'exhibition.html';
        });
    </script>
</body>
</html> 
