# 凌云数资 H5 - 巴蜀文化数字藏品平台

<div align="center">
  <img src="https://img.shields.io/badge/Vue-3.5.13-4FC08D?style=for-the-badge&logo=vue.js&logoColor=white" alt="Vue 3">
  <img src="https://img.shields.io/badge/TypeScript-5.8.0-3178C6?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript">
  <img src="https://img.shields.io/badge/Vite-6.2.4-646CFF?style=for-the-badge&logo=vite&logoColor=white" alt="Vite">
  <img src="https://img.shields.io/badge/Pinia-3.0.1-FFD859?style=for-the-badge&logo=pinia&logoColor=black" alt="Pinia">
</div>

## 📱 项目简介

凌云数资是一个专注于**巴蜀文化数字藏品**的移动端H5应用，基于Vue 3 + TypeScript开发。平台以**太阳神鸟**为设计核心，融合古蜀文明元素，为用户提供数字资产的展示、发行、预售和交易服务。

### 🌟 核心特色

- 🏛️ **巴蜀文化主题**：以太阳神鸟、三星堆、金沙遗址等古蜀文明为设计灵感
- 🎨 **太阳神鸟设计系统**：深色金黄配色方案，现代化UI设计
- 📱 **移动端专属**：完美适配手机浏览器，流畅的触屏体验
- 💎 **数字藏品展示**：支持资产展示、详情查看、收藏等功能
- 🔐 **完整用户系统**：登录注册、实名认证、个人中心
- 🛒 **预售交易功能**：即将发售、热门推荐、专区分类

## 🛠️ 技术架构

### 核心技术栈
```
前端框架：Vue 3.5.13 (Composition API)
开发语言：TypeScript 5.8.0
构建工具：Vite 6.2.4
状态管理：Pinia 3.0.1
路由管理：Vue Router 4.5.0
UI图标：FontAwesome 6.7.2
```

### 开发工具链
```
代码检查：ESLint + Oxlint
代码格式：Prettier 3.5.3
单元测试：Vitest 3.1.1
E2E测试：Playwright 1.51.1
构建优化：npm-run-all2
```

## 📁 项目结构

```
cwsf-issue-h5/
├── public/                          # 静态资源和HTML原型
│   ├── index.html                   # 主页原型
│   ├── exhibition.html              # 资产页面原型
│   ├── presale.html                 # 预售页面原型
│   ├── login.html                   # 登录页面原型
│   ├── profile.html                 # 个人中心原型
│   ├── asset-detail.html            # 资产详情原型
│   ├── activities.html              # 活动页面原型
│   ├── zones.html                   # 专区页面原型
│   └── images/                      # 图片资源
│       └── lingyun-banner.svg       # 平台Banner图
├── src/
│   ├── api/                         # API接口层
│   │   ├── auth.ts                  # 用户认证接口
│   │   ├── user.ts                  # 用户管理接口
│   │   ├── exhibition.ts            # 资产相关接口
│   │   ├── banner.ts                # 轮播图接口
│   │   ├── recommendation.ts        # 推荐资产接口
│   │   ├── presale.ts               # 预售接口
│   │   ├── search.ts                # 搜索接口
│   │   ├── zone.ts                  # 专区接口
│   │   ├── series.ts                # 系列接口
│   │   ├── ocr.ts                   # OCR识别接口
│   │   ├── upload.ts                # 文件上传接口
│   │   ├── dictionary.ts            # 字典数据接口
│   │   ├── pay.ts                   # 支付相关接口
│   │   ├── request.ts               # HTTP请求封装
│   │   └── types.ts                 # API类型定义
│   ├── assets/                      # 静态资源
│   │   ├── styles/                  # 样式文件
│   │   │   ├── design-system.css    # 太阳神鸟设计系统
│   │   │   ├── auth.css             # 登录注册样式
│   │   │   └── fontawesome-fix.css  # 图标修复样式
│   │   ├── base.css                 # 基础样式
│   │   ├── main.css                 # 主样式文件
│   │   └── logo.svg                 # 应用Logo
│   ├── components/                  # 组件库
│   │   ├── design-system/           # 设计系统组件
│   │   │   ├── DSButton.vue         # 按钮组件
│   │   │   ├── DSTag.vue            # 标签组件
│   │   │   ├── DSProductCard.vue    # 产品卡片组件
│   │   │   ├── DSCountdownCard.vue  # 倒计时卡片组件
│   │   │   └── DSStatsCard.vue      # 统计卡片组件
│   │   ├── BottomNavigation.vue     # 底部导航组件
│   │   ├── SearchBox.vue            # 搜索框组件
│   │   ├── AssetCard.vue            # 资产卡片组件
│   │   ├── CustomSelect.vue         # 自定义选择器
│   │   ├── CascaderSelect.vue       # 级联选择器
│   │   └── NotificationContainer.vue # 通知容器组件
│   ├── composables/                 # 组合式函数
│   │   ├── useNotification.ts       # 通知管理
│   │   └── useForceScroll.ts        # 滚动控制
│   ├── views/                       # 页面组件
│   │   ├── HomeView.vue             # 首页 (已完成API集成)
│   │   ├── LoginView.vue            # 登录页面
│   │   ├── RegisterView.vue         # 注册页面
│   │   ├── ProfileView.vue          # 个人中心
│   │   ├── VerificationView.vue     # 实名认证
│   │   ├── ExhibitionView.vue       # 数字资产页面 (已完成API集成)
│   │   ├── AssetDetailView.vue      # 资产详情页面 (已完成API集成)
│   │   ├── PresaleView.vue          # 预售专区
│   │   ├── ActivitiesView.vue       # 活动列表
│   │   ├── ZonesView.vue            # 专区列表
│   │   ├── SeriesView.vue           # 系列详情
│   │   ├── ZoneView.vue             # 专区详情
│   │   ├── SearchResultsView.vue    # 搜索结果
│   │   ├── PaymentView.vue          # 支付页面 (新增)
│   │   ├── MyOrdersView.vue         # 我的订单页面 (新增)
│   │   └── DesignSystemView.vue     # 设计系统展示
│   ├── router/                      # 路由配置
│   │   └── index.ts                 # 路由定义
│   ├── stores/                      # 状态管理
│   │   └── counter.ts               # 示例store
│   ├── App.vue                      # 根组件
│   └── main.ts                      # 应用入口
├── README-DesignSystem.md           # 设计系统文档
├── 字符编码问题修复报告.md           # 编码问题修复报告
├── package.json                     # 项目配置
├── vite.config.ts                   # Vite构建配置
└── tsconfig.json                    # TypeScript配置
```

## 🎨 太阳神鸟设计系统

### 🌅 深色金黄主题配色

基于中国文化传媒风格设计的专业深色主题，营造高端文化氛围：

```css
/* 主色系 - 优雅金黄 */
--primary-color: #d4a574;           /* 主色：优雅金黄 */
--primary-dark: #b8956a;            /* 深金黄 */
--primary-light: #e8c49a;           /* 浅金黄 */

/* 辅助色系 - 温暖橙色 */
--accent-color: #f4a261;            /* 温暖橙色 */
--accent-dark: #e76f51;             /* 深橙红 */
--accent-light: #f9c74f;            /* 浅橙黄 */

/* 背景色系 - 深色渐变 */
--bg-primary: #1a1a1a;              /* 主背景：深灰色 */
--bg-secondary: #242424;            /* 次背景：中灰色 */
--bg-tertiary: #2e2e2e;             /* 三级背景：浅灰色 */

/* 文字色系 - 高对比度 */
--text-primary: #f8f6f0;            /* 主要文字：米白色 */
--text-secondary: #e0ddd4;          /* 次要文字：浅米色 */
--text-tertiary: #c4c0b1;           /* 三级文字：暖灰色 */
```

### 🧩 核心组件

- **DSButton**: 多类型按钮组件（主要、次要、强调、轮廓、幽灵）
- **DSTag**: 多样式标签组件（主色、强调、成功、警告、错误、信息）
- **DSProductCard**: 产品展示卡片，支持图片、价格、标签等
- **DSCountdownCard**: 倒计时卡片，支持自定义时间和样式
- **DSStatsCard**: 统计数据展示卡片
- **BottomNavigation**: 底部导航组件，支持路由自动高亮

## 🚀 核心功能

### ✅ 已完成模块

#### 🏠 首页系统
- **智能轮播图**：接入banner API，支持图片轮播和智能跳转
- **热门推荐**：接入推荐API，展示热门资产
- **即将发售**：接入预售API，展示即将发售的资产
- **搜索功能**：支持全局搜索，跳转到搜索结果页
- **底部导航**：统一的导航组件，支持5个主要页面

#### 🔐 用户认证系统
- **登录页面**：支持密码登录 + 短信验证码登录
- **注册页面**：手机号注册 + 短信验证
- **实名认证**：个人认证 + 企业认证，支持OCR身份证/营业执照识别
- **个人中心**：用户信息展示、资产管理、账户设置

#### 💎 资产展示系统
- **资产列表**：接入资产API，支持搜索、筛选、排序
- **资产详情**：完整的资产详情页，包含封面、介绍、发行方信息
- **发售倒计时**：实时倒计时功能，倒计时结束自动显示购买按钮
- **智能筛选系统**：现代化筛选设计，顶部横向标签+展开式选项，支持专区、系列、类型多维度筛选
- **网格布局展示**：筛选选项采用网格布局，提供更直观的选择体验
- **系列筛选**：接入系列API，支持按系列筛选资产
- **专区筛选**：接入专区API，支持按专区筛选资产

#### 🛒 预售交易系统
- **预售专区**：展示即将发售的数字藏品
- **倒计时功能**：支持商品发售倒计时
- **分类筛选**：支持按类型、价格、时间筛选
- **商品详情**：完整的商品信息展示

#### 💳 支付系统
- **订单创建**：支持数字资产下单功能
- **支付页面**：专业的支付确认页面，展示资产信息
- **多种支付方式**：支持支付宝H5、微信H5支付
- **订单状态检查**：自动检查订单状态，处理异常情况
- **支付流程**：完整的支付流程，从下单到支付确认
- **订单列表**：支持订单查询、筛选和继续支付功能，优化头部导航居中和筛选布局

#### 🎯 专区系统
- **专区列表**：展示不同文化主题的专区
- **专区详情**：每个专区的详细介绍和资产
- **热门专区**：推荐热门文化专区

#### 🔍 搜索系统
- **全局搜索**：支持资产名称搜索
- **搜索结果**：展示搜索结果，支持筛选

#### 🏆 活动邀新系统
- **邀新海报生成**：支持生成活动邀请海报，包含QR码
- **邀新排行榜**：展示活动邀请排行榜，支持分页加载
- **上拉加载更多**：排行榜支持无限滚动加载
- **活动状态管理**：根据活动时间和参与状态显示不同按钮
- **中奖结果查询**：开奖后查看个人中奖结果，支持弹窗展示

### 🔧 技术亮点

#### API接口集成
```typescript
// 轮播图API - 自动获取激活状态轮播图
BannerAPI.getBannerList()

// 热门推荐API - 获取推荐资产
RecommendationAPI.getHotRecommendations(page, size)

// 即将发售API - 获取预售资产
PresaleAPI.getPresaleList(size)

// 资产列表API - 支持搜索和筛选
ExhibitionAPI.getAssetList({
  assetName: '搜索关键词',
  seriesId: '系列ID',
  zoneId: '专区ID'
})

// 资产详情API - 获取详细资产信息
ExhibitionAPI.getAssetDetail(assetId)

// 支付相关API - 完整的支付流程
PayAPI.addOrder({
  buyQuantity: 1,
  tradeType: 'issueBuy',
  assetId: 'asset123'
})

// 订单状态检查API
PayAPI.getOrderStatus(orderNo)

// 发起支付API
PayAPI.pay(orderNo, 'alipayH5')

// 订单列表API - 支持筛选和分页
PayAPI.getOrderList({
  assetName: '资产名称',
  statusCd: '状态代码',
  pageNum: 1,
  pageSize: 20
})

// 订单状态字典API
DictionaryAPI.getOrderStatusTypes()

// 邀新排行榜API - 支持分页加载
ActivityAPI.getInviteRank(activityId, {
  pageNum: 1,
  pageSize: 20
})

// 参与活动API
ActivityAPI.joinActivity(activityId)

// 中奖结果查询API
ActivityAPI.getPrizeResult(activityId)
```

#### 智能数据处理
- **多格式兼容**：自动适配不同的API响应格式
- **降级机制**：API失败时自动使用默认数据
- **加载状态**：优雅的加载动画和状态管理
- **错误处理**：完整的错误捕获和用户提示

#### 发售倒计时系统
```typescript
// 实时倒计时计算
const countdown = computed(() => {
  if (!asset.value?.saleStartTime || isSaleStarted.value) return null
  const saleTime = new Date(asset.value.saleStartTime)
  const diff = saleTime.getTime() - currentTime.value.getTime()
  
  return {
    days: Math.floor(diff / (1000 * 60 * 60 * 24)),
    hours: Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
    minutes: Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60)),
    seconds: Math.floor((diff % (1000 * 60)) / 1000)
  }
})
```

## 🚧 已知问题

### 字符编码问题
部分HTML原型文件存在字符编码问题，主要表现为：
- 中文字符显示为乱码（如："凌云数资" → "四川省数字资产发行平?"）
- JavaScript字符串未正确终止
- 影响页面正常显示

**解决方案**：
1. ✅ `index.html` 已完全修复
2. 🔄 其他HTML原型文件需要逐个修复
3. 📝 建议使用Vue组件替代HTML原型

详见：[字符编码问题修复报告.md](./字符编码问题修复报告.md)

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 9.0.0 或 yarn >= 1.22.0

### 安装和运行
```bash
# 1. 克隆项目
git clone <项目地址>
cd cwsf-issue-h5

# 2. 安装依赖
npm install

# 3. 启动开发服务器
npm run dev

# 4. 构建生产版本
npm run build

# 5. 预览生产构建
npm run preview
```

### 开发命令
```bash
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run preview      # 预览生产构建
npm run test:unit    # 运行单元测试
npm run test:e2e     # 运行E2E测试
npm run lint         # 代码检查
npm run format       # 代码格式化
npm run type-check   # TypeScript类型检查
```

## 📱 页面路由

```typescript
// 主要页面路由
const routes = [
  { path: '/', component: 'HomeView' },              // 首页
  { path: '/login', component: 'LoginView' },        // 登录
  { path: '/register', component: 'RegisterView' },  // 注册
  { path: '/profile', component: 'ProfileView' },    // 个人中心
  { path: '/verification', component: 'VerificationView' }, // 实名认证
  { path: '/exhibition', component: 'ExhibitionView' },     // 数字资产
  { path: '/asset/:assetId', component: 'AssetDetailView' }, // 资产详情
  { path: '/presale', component: 'PresaleView' },    // 预售专区
  { path: '/activities', component: 'ActivitiesView' }, // 活动列表
  { path: '/zones', component: 'ZonesView' },        // 专区列表
  { path: '/search', component: 'SearchResultsView' }, // 搜索结果
  { path: '/payment/:orderNo', component: 'PaymentView' }, // 支付页面
  { path: '/my-orders', component: 'MyOrdersView' },   // 我的订单
  { path: '/activity/:activityId/invite-rank', component: 'InviteRankView' } // 邀新排行榜
]
```

## 🔧 配置说明

### Vite配置
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [vue(), vueJsx()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  }
})
```

### API请求配置
```typescript
// src/api/request.ts
const baseURL = 'https://api.example.com'  // 配置API基础URL
const timeout = 10000                       // 请求超时时间

// 请求拦截器 - 自动添加token
request.interceptors.request.use(config => {
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})
```

## 📊 项目状态

### 完成度统计
- ✅ **核心页面**：100% (12/12页面完成)
- ✅ **API集成**：100% (17/17接口完成)
- ✅ **组件库**：100% (设计系统完整)
- ✅ **用户系统**：95% (认证流程完整)
- ✅ **交易功能**：85% (支付系统基本完成)
- ✅ **活动系统**：100% (邀新和中奖功能完整)

### 技术债务
1. **字符编码修复**：HTML原型文件需要重新编码
2. **单元测试**：需要补充组件单元测试
3. **E2E测试**：需要完善端到端测试用例
4. **API文档**：需要补充完整的API文档

## 🎯 开发规范

### 代码风格
- 使用Vue 3 Composition API + `<script setup>`语法
- 遵循TypeScript严格模式
- 组件命名使用PascalCase
- 文件命名使用kebab-case
- CSS使用变量进行主题管理

### 组件开发示例
```vue
<script setup lang="ts">
import { ref, computed } from 'vue'

// Props定义
interface Props {
  title: string
  price?: number
}
const props = withDefaults(defineProps<Props>(), {
  price: 0
})

// 响应式数据
const isLoading = ref(false)

// 计算属性
const displayPrice = computed(() => 
  props.price > 0 ? `￥${props.price.toFixed(2)}` : '免费'
)
</script>

<template>
  <div class="product-card">
    <h3>{{ title }}</h3>
    <p class="price">{{ displayPrice }}</p>
  </div>
</template>

<style scoped>
.product-card {
  background: var(--gradient-card);
  padding: var(--spacing-lg);
  border-radius: var(--radius-xl);
}
</style>
```

## 🌟 未来规划

### v1.1.0 - 交易功能完善
- [x] 支付系统集成 (已完成)
- [ ] 订单管理功能
- [ ] 钱包余额管理
- [ ] 交易记录查询
- [ ] 支付结果页面
- [ ] 退款功能

### v1.2.0 - 社交功能
- [ ] 用户关注系统
- [ ] 评论点赞功能
- [ ] 分享传播功能
- [ ] 消息通知系统

### v2.0.0 - 高级功能
- [ ] AR/VR资产展示
- [ ] 区块链技术集成
- [ ] AI智能推荐
- [ ] 数据分析面板

## 🤝 贡献指南

1. Fork 项目到你的GitHub
2. 创建功能分支：`git checkout -b feature/新功能`
3. 提交更改：`git commit -m 'feat: 添加新功能'`
4. 推送分支：`git push origin feature/新功能`
5. 创建Pull Request

### 提交规范
```
feat: 新功能
fix: 修复问题  
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或工具变动
```

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🆘 技术支持

如果你在使用过程中遇到问题，可以通过以下方式获取帮助：

1. **查看文档**：仔细阅读本README和设计系统文档
2. **检查Issues**：查看是否有类似问题已被提出
3. **提交Issue**：详细描述问题，包含错误信息和复现步骤
4. **代码Review**：欢迎提交PR改进项目

---

<div align="center">
  <p><strong>凌云数资 - 传承巴蜀文化，创新数字未来</strong></p>
  <p>由 ❤️ 和 Vue.js 驱动 | © 2024 凌云数资. All rights reserved.</p>
</div> 