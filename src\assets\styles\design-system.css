/* 太阳神鸟设计系统 - 通用样式 */

:root {
  /* 新配色方案 - 基于参考图片的深色金黄主题 */
  
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;           /* 主色：优雅金黄 */
  --primary-dark: #b8956a;            /* 主色深色：深金黄 */
  --primary-light: #e8c49a;           /* 主色浅色：浅金黄 */
  --primary-alpha: rgba(212, 165, 116, 0.15); /* 主色透明 */
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;            /* 辅助色：温暖橙色 */
  --accent-dark: #e76f51;             /* 辅助色深色：深橙红 */
  --accent-light: #f9c74f;            /* 辅助色浅色：浅橙黄 */
  --accent-alpha: rgba(244, 162, 97, 0.15); /* 辅助色透明 */
  
  /* 功能色系 - 深色主题适配 */
  --success-color: #90a955;           /* 成功色：橄榄绿 */
  --warning-color: #f9844a;           /* 警告色：暖橙 */
  --error-color: #e63946;             /* 错误色：红色 */
  --info-color: #457b9d;              /* 信息色：蓝色 */
  
  /* 文字颜色系统 - 深色主题 */
  --text-primary: #f8f6f0;            /* 主要文字：米白色 */
  --text-secondary: #e0ddd4;          /* 次要文字：浅米色 */
  --text-tertiary: #c4c0b1;           /* 三级文字：暖灰色 */
  --text-muted: #a39d8e;              /* 弱化文字：深暖灰 */
  --text-inverse: #2d2a24;            /* 反色文字：深棕色 */
  
  /* 背景色系统 - 深色渐变 */
  --bg-primary: #1a1a1a;              /* 主背景：深灰色 */
  --bg-secondary: #242424;            /* 次背景：中灰色 */
  --bg-tertiary: #2e2e2e;             /* 三级背景：浅灰色 */
  --bg-card: rgba(42, 42, 42, 0.9);   /* 卡片背景：半透明深灰 */
  --bg-card-hover: rgba(50, 50, 50, 0.95); /* 卡片悬浮：半透明中灰 */
  --bg-section: rgba(26, 26, 26, 0.8); /* 区块背景：半透明深灰 */
  
  /* 浅色模式背景（可选） */
  --bg-light-primary: #faf8f5;        /* 浅色主背景 */
  --bg-light-secondary: #f5f2ed;      /* 浅色次背景 */
  --bg-light-tertiary: #efebe3;       /* 浅色三级背景 */
  --bg-light-card: #ffffff;           /* 浅色卡片背景 */
  
  /* 边框和分割线 */
  --border-primary: rgba(212, 165, 116, 0.3);   /* 主边框 */
  --border-secondary: rgba(244, 162, 97, 0.25); /* 次边框 */
  --border-light: rgba(248, 246, 240, 0.1);     /* 浅边框 */
  --border-hover: rgba(212, 165, 116, 0.5);     /* 悬浮边框 */
  --divider: rgba(248, 246, 240, 0.08);         /* 分割线 */
  
  /* 阴影系统 - 深色主题适配 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.7);
  --shadow-card: 0 4px 12px rgba(0, 0, 0, 0.3);
  --shadow-hover: 0 6px 20px rgba(212, 165, 116, 0.2);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-warm: linear-gradient(135deg, var(--accent-light) 0%, var(--primary-color) 50%, var(--accent-color) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 特殊效果渐变 */
  --gradient-gold-shine: linear-gradient(90deg, transparent 0%, rgba(212, 165, 116, 0.6) 50%, transparent 100%);
  --gradient-overlay: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-xxl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;
  
  /* 圆角系统 */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-xxl: 20px;
  --radius-3xl: 24px;
  --radius-round: 50px;
  --radius-full: 50%;
  
  /* 动画时长 */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  --transition-smooth: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 字体系统 */
  --font-family-base: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 30px;
  --font-size-4xl: 36px;
  
  /* 字体权重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-black: 900;
  
  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* 基础重置 */
.design-system * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-family-base);
  -webkit-tap-highlight-color: transparent;
}

/* 容器样式 */
.design-system-container {
  background: var(--gradient-bg);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  min-height: 100vh;
  padding: var(--spacing-md);
}

.design-system-inner {
  max-width: 100%;
  margin: 0 auto;
  padding: var(--spacing-md);
}

/* 文字样式 */
.text-large {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: var(--spacing-sm) 0;
}

.text-medium {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: var(--spacing-sm) 0;
}

.text-normal {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0;
}

.text-small {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin: var(--spacing-xs) 0;
}

.text-primary {
  color: var(--primary-color);
}

.text-accent {
  color: var(--accent-color);
}

.text-success {
  color: var(--success-color);
}

.text-warning {
  color: var(--warning-color);
}

.text-error {
  color: var(--error-color);
}

.text-info {
  color: var(--info-color);
}

/* 特殊文字效果 */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-normal) var(--transition-smooth);
  border: none;
  margin: var(--spacing-xs);
  min-width: 120px;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold-shine);
  transition: left var(--transition-slow) var(--transition-smooth);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
  border: 1px solid var(--primary-color);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow);
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
}

.btn-secondary {
  background: var(--bg-card);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--bg-card-hover);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  color: var(--text-primary);
}

.btn-accent {
  background: var(--gradient-accent);
  color: var(--text-inverse);
  box-shadow: 0 3px 10px rgba(244, 162, 97, 0.3);
}

.btn-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 162, 97, 0.4);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  backdrop-filter: blur(10px);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  box-shadow: var(--shadow-primary);
}

.btn-ghost {
  background: rgba(212, 165, 116, 0.1);
  color: var(--primary-color);
  border: 1px solid rgba(212, 165, 116, 0.2);
  backdrop-filter: blur(10px);
}

.btn-ghost:hover {
  background: rgba(212, 165, 116, 0.2);
  border-color: var(--primary-color);
}

.btn-small {
  padding: 8px 16px;
  font-size: var(--font-size-xs);
  min-width: 80px;
}

.btn-large {
  padding: 16px 32px;
  font-size: var(--font-size-base);
  min-width: 140px;
}

.btn-icon {
  gap: var(--spacing-xs);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin: 2px;
  transition: all var(--transition-fast) var(--transition-smooth);
  backdrop-filter: blur(10px);
}

.tag-primary {
  background: var(--primary-alpha);
  color: var(--primary-color);
  border: 1px solid rgba(212, 165, 116, 0.3);
}

.tag-accent {
  background: var(--accent-alpha);
  color: var(--accent-color);
  border: 1px solid rgba(244, 162, 97, 0.3);
}

.tag-success {
  background: rgba(144, 169, 85, 0.15);
  color: var(--success-color);
  border: 1px solid rgba(144, 169, 85, 0.3);
}

.tag-warning {
  background: rgba(249, 132, 74, 0.15);
  color: var(--warning-color);
  border: 1px solid rgba(249, 132, 74, 0.3);
}

.tag-error {
  background: rgba(230, 57, 70, 0.15);
  color: var(--error-color);
  border: 1px solid rgba(230, 57, 70, 0.3);
}

.tag-info {
  background: rgba(69, 123, 157, 0.15);
  color: var(--info-color);
  border: 1px solid rgba(69, 123, 157, 0.3);
}

.tag-large {
  padding: 8px 16px;
  font-size: var(--font-size-sm);
  border-radius: var(--radius-xl);
}

.tag:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* 卡片样式 */
.card {
  background: var(--gradient-card);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal) var(--transition-smooth);
  box-shadow: var(--shadow-card);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-hover);
  border-color: var(--border-hover);
}

.card:hover::before {
  opacity: 1;
}

.card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
}

.card-title i {
  margin-right: var(--spacing-sm);
  color: var(--accent-color);
  font-size: var(--font-size-lg);
}

/* 布局样式 */
.grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

.btn-group {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-sm) 0;
}

.example-area {
  padding: var(--spacing-xl);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin: var(--spacing-md) 0;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px dashed var(--border-primary);
  position: relative;
  overflow: hidden;
}

.example-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-overlay);
  pointer-events: none;
}

.code {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-md);
  width: 100%;
  overflow-x: auto;
  border: 1px solid var(--border-light);
  white-space: pre-wrap;
  line-height: var(--line-height-relaxed);
}

/* 分割线 */
.divider {
  height: 32px;
  width: 1px;
  background: var(--divider);
  margin: 0 var(--spacing-sm);
}

.divider-horizontal {
  height: 1px;
  width: 100%;
  background: var(--divider);
  margin: var(--spacing-md) 0;
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-card);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--transition-normal) var(--transition-smooth);
  backdrop-filter: blur(10px);
}

.input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-alpha);
  background: rgba(42, 42, 42, 0.95);
}

.input::placeholder {
  color: var(--text-muted);
}

/* 特殊效果 */
.glass-effect {
  backdrop-filter: blur(20px);
  background: rgba(42, 42, 42, 0.8);
  border: 1px solid rgba(248, 246, 240, 0.1);
}

.glow-effect {
  box-shadow: 0 0 20px rgba(212, 165, 116, 0.3);
}

.shine-effect {
  position: relative;
  overflow: hidden;
}

.shine-effect::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-gold-shine);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: -100%; }
  100% { left: 100%; }
}

/* 响应式 */
@media (min-width: 768px) {
  .grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-xl);
  }
  
  .design-system-container {
    padding: var(--spacing-xl);
  }
}

@media (min-width: 1024px) {
  .design-system-inner {
    max-width: 1200px;
  }
  
  .grid {
    gap: var(--spacing-xxl);
  }
}

@media (min-width: 1440px) {
  .design-system-inner {
    max-width: 1400px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* 深色主题强制应用 */
body {
  background: var(--bg-primary);
  color: var(--text-secondary);
} 