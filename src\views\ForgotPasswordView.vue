<template>
  <div class="auth-page">
    <div class="auth-container">
      <!-- 页面头部 -->
      <div class="auth-header">
        <button class="back-btn" @click="goBack">
          ←
        </button>
        <h1 style="font-size: 18px; font-weight: 600; color: var(--neutral-100);">忘记密码</h1>
      </div>

      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="welcome-text">凌云数资</div>
        <div class="subtitle">重置密码</div>
      </div>

      <!-- 重置密码表单 -->
      <div class="auth-form">
        <!-- 步骤一：验证手机号 -->
        <div v-if="currentStep === 1" class="form-content active">
          <div class="step-indicator">
            <span class="step-number">1</span>
            <span class="step-title">验证身份</span>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-mobile"></i>
              手机号码
            </label>
            <div class="input-wrapper">
              <input
                type="tel"
                class="form-input"
                placeholder="请输入已注册的手机号"
                maxlength="11"
                v-model="form.phoneNumber"
                @input="formatPhoneNumber"
                required
              >
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-shield"></i>
              短信验证码
            </label>
            <div class="sms-container">
              <input
                type="text"
                class="form-input"
                placeholder="请输入验证码"
                maxlength="6"
                v-model="form.smsCode"
                @input="formatSmsCode"
                required
              >
              <button
                type="button"
                class="sms-btn"
                :disabled="smsCountdown > 0"
                @click="sendSMS"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
              </button>
            </div>
          </div>

          <button
            type="button"
            class="btn btn-primary"
            :disabled="isProcessing"
            @click="verifyPhone"
          >
            <span v-if="isProcessing"><i class="fas fa-spinner fa-spin"></i></span>
            <span v-else><i class="fas fa-arrow-right"></i></span>
            {{ isProcessing ? '验证中...' : '下一步' }}
          </button>
        </div>

        <!-- 步骤二：设置新密码 -->
        <div v-if="currentStep === 2" class="form-content active">
          <div class="step-indicator">
            <span class="step-number">2</span>
            <span class="step-title">设置新密码</span>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-lock"></i>
              新密码
            </label>
            <div class="input-wrapper">
              <input
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                :class="{
                  'input-error': form.newPassword && !passwordValidationValid,
                  'input-success': form.newPassword && passwordValidationValid
                }"
                placeholder="请输入新密码（8-16位，包含大小写字母和数字）"
                v-model="form.newPassword"
                @input="onPasswordInput"
                required
              >
              <button
                type="button"
                class="password-toggle"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>

            <!-- 密码校验提示 -->
            <div
              v-if="form.newPassword && passwordValidationMessage"
              class="validation-message"
              :class="{
                'validation-success': passwordValidationValid,
                'validation-error': !passwordValidationValid
              }"
            >
              <span v-if="passwordValidationValid">✅</span>
              <span v-else>❌</span>
              {{ passwordValidationMessage }}
            </div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-lock"></i>
              确认密码
            </label>
            <div class="input-wrapper">
              <input
                :type="showConfirmPassword ? 'text' : 'password'"
                class="form-input"
                :class="{
                  'input-error': form.confirmPassword && !passwordMatchValid,
                  'input-success': form.confirmPassword && passwordMatchValid
                }"
                placeholder="请再次输入密码"
                v-model="form.confirmPassword"
                @input="onConfirmPasswordInput"
                required
              >
              <button
                type="button"
                class="password-toggle"
                @click="toggleConfirmPassword"
              >
                <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>

            <!-- 密码匹配提示 -->
            <div
              v-if="form.confirmPassword && passwordMatchMessage"
              class="validation-message"
              :class="{
                'validation-success': passwordMatchValid,
                'validation-error': !passwordMatchValid
              }"
            >
              <span v-if="passwordMatchValid">✅</span>
              <span v-else">❌</span>
              {{ passwordMatchMessage }}
            </div>
          </div>

          <button
            type="button"
            class="btn btn-primary"
            :disabled="isProcessing || !canSubmit"
            @click="resetPassword"
          >
            <span v-if="isProcessing"><i class="fas fa-spinner fa-spin"></i></span>
            <span v-else><i class="fas fa-check"></i></span>
            {{ isProcessing ? '重置中...' : '完成重置' }}
          </button>
        </div>
      </div>

      <!-- 返回登录提示 -->
      <div class="prompt-card">
        <p>想起密码了？</p>
        <router-link to="/login">返回登录 →</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useForceScroll } from '@/composables/useForceScroll'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import { RequestError } from '@/api/request'
import { validatePhoneNumber as validatePhone, detectVirtualOperator, getVirtualOperatorInfo } from '@/utils/phoneValidator'

// 响应式数据
const router = useRouter()
const { enableScrolling } = useForceScroll()
const { success, error, info } = useNotification()

const currentStep = ref(1)
const smsCountdown = ref(0)
const isProcessing = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 密码校验状态
const passwordValidationMessage = ref('')
const passwordValidationValid = ref(false)
const passwordMatchMessage = ref('')
const passwordMatchValid = ref(false)

// 表单数据
const form = reactive({
  phoneNumber: '',
  smsCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 倒计时定时器
let countdownTimer: number | null = null

// 计算属性
const canSubmit = computed(() => {
  return form.newPassword &&
         form.confirmPassword &&
         passwordValidationValid.value &&
         passwordMatchValid.value
})

/**
 * 格式化手机号输入
 */
const formatPhoneNumber = () => {
  form.phoneNumber = form.phoneNumber.replace(/\D/g, '')
}

/**
 * 格式化验证码输入
 */
const formatSmsCode = () => {
  form.smsCode = form.smsCode.replace(/\D/g, '')
}

/**
 * 验证密码强度
 */
const validatePasswordStrength = (password: string): { isValid: boolean; message: string } => {
  if (!password) {
    return { isValid: false, message: '请输入密码' }
  }

  if (password.length < 8 || password.length > 16) {
    return { isValid: false, message: '密码长度必须为8-16位字符' }
  }

  if (!/[A-Z]/.test(password)) {
    return { isValid: false, message: '密码必须包含大写字母' }
  }

  if (!/[a-z]/.test(password)) {
    return { isValid: false, message: '密码必须包含小写字母' }
  }

  if (!/[0-9]/.test(password)) {
    return { isValid: false, message: '密码必须包含数字' }
  }

  return { isValid: true, message: '密码强度符合要求' }
}

/**
 * 密码输入处理
 */
const onPasswordInput = () => {
  const validation = validatePasswordStrength(form.newPassword)
  passwordValidationMessage.value = validation.message
  passwordValidationValid.value = validation.isValid

  // 如果确认密码已填写，重新检查匹配
  if (form.confirmPassword) {
    onConfirmPasswordInput()
  }
}

/**
 * 确认密码输入处理
 */
const onConfirmPasswordInput = () => {
  if (!form.confirmPassword) {
    passwordMatchMessage.value = ''
    passwordMatchValid.value = false
    return
  }

  if (form.newPassword === form.confirmPassword) {
    passwordMatchMessage.value = '密码匹配'
    passwordMatchValid.value = true
  } else {
    passwordMatchMessage.value = '两次输入的密码不一致'
    passwordMatchValid.value = false
  }
}

/**
 * 发送短信验证码（包含虚拟运营商检测）
 */
const sendSMS = async () => {
  if (!form.phoneNumber) {
    error('请先输入手机号码')
    return
  }

  // 验证手机号（包含虚拟运营商检测）
  const result = validatePhone(form.phoneNumber, false) // 不允许虚拟运营商
  if (!result.isValid) {
    // 如果是虚拟运营商，显示详细提示
    if (result.virtualInfo?.isVirtual) {
      const detailInfo = getVirtualOperatorInfo(form.phoneNumber)
      error(`${result.error}\n\n${detailInfo}`, 8000)
    } else {
      error(result.error || '手机号码验证失败')
    }
    return
  }

  if (smsCountdown.value > 0) return

  try {
    await AuthAPI.sendSmsCode({
      phone: form.phoneNumber,
      scene: 'H5resetPassword'
    })

    success(`验证码已发送到手机号 ${form.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}`)

    // 开始倒计时
    smsCountdown.value = 60
    countdownTimer = setInterval(() => {
      if (smsCountdown.value <= 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      } else {
        smsCountdown.value--
      }
    }, 1000)
  } catch (err) {
    console.error('发送验证码失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('发送验证码失败，请稍后重试')
    }
  }
}

/**
 * 验证手机号（包含虚拟运营商检测）
 */
const verifyPhone = async () => {
  const result = validatePhone(form.phoneNumber, false) // 不允许虚拟运营商
  if (!result.isValid) {
    error(result.error || '手机号码验证失败')
    return
  }

  if (!form.smsCode || form.smsCode.length !== 6) {
    error('请输入6位验证码')
    return
  }

  isProcessing.value = true

  try {
    // 模拟验证验证码（实际项目中需要调用真实API）
    if (form.smsCode === '123456') {
      success('验证成功！请设置新密码')
      currentStep.value = 2
    } else {
      throw new Error('验证码错误')
    }
  } catch (err) {
    console.error('验证失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('验证失败，请检查验证码')
    }
  } finally {
    isProcessing.value = false
  }
}

/**
 * 重置密码
 */
const resetPassword = async () => {
  if (!canSubmit.value) {
    error('请检查密码输入')
    return
  }

  isProcessing.value = true

  try {
    // 模拟重置密码（实际项目中需要调用真实API）
    console.log('重置密码:', {
      phone: form.phoneNumber,
      smsCode: form.smsCode,
      newPassword: form.newPassword
    })

    success('密码重置成功！请使用新密码登录')

    // 跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 2000)
  } catch (err) {
    console.error('重置密码失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('重置密码失败，请稍后重试')
    }
  } finally {
    isProcessing.value = false
  }
}

/**
 * 切换密码显示状态
 */
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

/**
 * 切换确认密码显示状态
 */
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

// 生命周期钩子
onMounted(() => {
  enableScrolling()

  // 自动填入缓存中的手机号
  const localUserInfo = AuthAPI.getLocalUserInfo()
  if (localUserInfo && localUserInfo.phonenumber) {
    form.phoneNumber = localUserInfo.phonenumber
  }

  info('请验证手机号后设置新密码')
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<style scoped>
@import '@/assets/styles/auth.css';

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(212, 165, 116, 0.1);
  border: 1px solid rgba(212, 165, 116, 0.2);
  border-radius: 12px;
}

.step-number {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 14px;
}

.step-title {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
}
</style>
