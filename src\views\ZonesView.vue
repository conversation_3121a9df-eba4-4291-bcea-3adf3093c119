<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 路由
const router = useRouter()

// 主要专区数据
const mainZones = ref([
  {
    id: 'sanxingdui',
    title: '三星堆文明专区',
    description: '神秘的古蜀文明，青铜器与黄金面具的奇幻世界。收集来自三星堆遗址的珍贵数字藏品。',
    icon: 'fas fa-mask',
    gradient: 'linear-gradient(135deg, var(--primary-gold-dark), var(--primary-gold))',
    stats: {
      items: 89,
      collectors: '2.3k',
      rating: 4.8
    },
    features: ['考古发现', '青铜器', '3D模型', '限量版'],
    followed: false
  },
  {
    id: 'jinsha',
    title: '金沙遗址专区',
    description: '太阳神鸟的故乡，古蜀王国的政治经济中心。感受金沙文化的璀璨光芒。',
    icon: 'fas fa-sun',
    gradient: 'linear-gradient(135deg, var(--secondary-purple-dark), var(--secondary-purple))',
    stats: {
      items: 156,
      collectors: '4.1k',
      rating: 4.9
    },
    features: ['太阳神鸟', '黄金制品', '象牙雕刻', '精品收藏'],
    followed: false
  },
  {
    id: 'shujin',
    title: '蜀锦文化专区',
    description: '千年织造工艺，丝绸之路的起点。体验蜀锦的精湛技艺和文化内涵。',
    icon: 'fas fa-cut',
    gradient: 'linear-gradient(135deg, var(--accent-orange), var(--primary-gold-light))',
    stats: {
      items: 67,
      collectors: '1.8k',
      rating: 4.7
    },
    features: ['传统工艺', '纹样设计', '非遗文化', '匠心制作'],
    followed: false
  }
])

// 特色专区数据
const specialZones = ref([
  {
    id: 'chuanju',
    title: '川剧变脸',
    description: '传统戏曲艺术，变脸绝技数字化典藏',
    icon: 'fas fa-theater-masks'
  },
  {
    id: 'food',
    title: '川菜文化',
    description: '麻辣鲜香，川菜技艺与文化传承',
    icon: 'fas fa-pepper-hot'
  },
  {
    id: 'panda',
    title: '熊猫故乡',
    description: '国宝大熊猫的可爱形象与保护故事',
    icon: 'fas fa-paw'
  },
  {
    id: 'landscape',
    title: '巴山蜀水',
    description: '四川名山大川的壮美景色',
    icon: 'fas fa-mountain'
  },
  {
    id: 'tea',
    title: '茶马古道',
    description: '古代贸易路线，茶文化的历史印记',
    icon: 'fas fa-leaf'
  },
  {
    id: 'poetry',
    title: '诗词文化',
    description: '李白杜甫故里，诗歌文化的发源地',
    icon: 'fas fa-feather-alt'
  }
])

// 方法


const enterZone = (zoneId: string) => {
  console.log(`进入专区: ${zoneId}`)
  // TODO: 跳转到专区详情页面
  // router.push(`/zone/${zoneId}`)
}

const toggleFollow = (zone: any) => {
  zone.followed = !zone.followed
  console.log(`${zone.followed ? '关注' : '取消关注'}专区: ${zone.title}`)
  // TODO: 实现关注功能
}

const enterSpecialZone = (zoneId: string) => {
  console.log(`进入特色专区: ${zoneId}`)
  // TODO: 跳转到特色专区详情页面
}
</script>

<template>
  <div class="zones-container">

    <main class="main-content">
      <div class="container">
        <!-- 标题区域 -->
        <section class="hero-section">
          <h2 class="hero-title">四川文化专区</h2>
          <p class="hero-subtitle">探索巴蜀文明，收藏数字珍品</p>
        </section>

        <!-- 主要专区 -->
        <section class="zones-grid">
          <div 
            v-for="zone in mainZones" 
            :key="zone.id"
            class="zone-card"
          >
            <div class="zone-banner" :style="{ background: zone.gradient }">
              <div class="zone-icon">
                <i :class="zone.icon"></i>
              </div>
            </div>
            <div class="zone-info">
              <h3 class="zone-title">{{ zone.title }}</h3>
              <p class="zone-description">{{ zone.description }}</p>
              
              <div class="zone-stats">
                <div class="stat-item">
                  <div class="stat-value">{{ zone.stats.items }}</div>
                  <div class="stat-label">藏品</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ zone.stats.collectors }}</div>
                  <div class="stat-label">收藏者</div>
                </div>
                <div class="stat-item">
                  <div class="stat-value">{{ zone.stats.rating }}</div>
                  <div class="stat-label">评分</div>
                </div>
              </div>
              
              <div class="zone-features">
                <span 
                  v-for="feature in zone.features" 
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </span>
              </div>
              
              <div class="zone-actions">
                <button class="btn btn-primary" @click="enterZone(zone.id)">
                  <i class="fas fa-store"></i> 进入专区
                </button>
                <button 
                  class="btn btn-secondary"
                  :class="{ followed: zone.followed }"
                  @click="toggleFollow(zone)"
                >
                  <i :class="zone.followed ? 'fas fa-heart' : 'far fa-heart'"></i> 
                  {{ zone.followed ? '已关注' : '关注' }}
                </button>
              </div>
            </div>
          </div>
        </section>

        <!-- 特色专区 -->
        <section class="special-zones">
          <h3 class="section-title">特色专区</h3>
          <div class="special-grid">
            <div 
              v-for="zone in specialZones" 
              :key="zone.id"
              class="special-card"
              @click="enterSpecialZone(zone.id)"
            >
              <div class="special-icon">
                <i :class="zone.icon"></i>
              </div>
              <div class="special-title">{{ zone.title }}</div>
              <div class="special-desc">{{ zone.description }}</div>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
      <div class="container">
        <div class="nav-items">
          <router-link to="/" class="nav-item" :class="{ active: $route.name === 'home' }">
            <i class="nav-icon fas fa-home"></i>
            <span class="nav-label">首页</span>
          </router-link>
          <router-link to="/exhibition" class="nav-item" :class="{ active: $route.name === 'exhibition' }">
            <i class="nav-icon fas fa-store"></i>
            <span class="nav-label">资产</span>
          </router-link>
          <router-link to="/activities" class="nav-item" :class="{ active: $route.name === 'activities' }">
            <i class="nav-icon fas fa-calendar-star"></i>
            <span class="nav-label">活动</span>
          </router-link>
          <router-link to="/profile" class="nav-item" :class="{ active: $route.name === 'profile' }">
            <i class="nav-icon fas fa-user"></i>
            <span class="nav-label">我的</span>
          </router-link>
        </div>
      </div>
    </nav>
  </div>
</template>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.zones-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}



/* 标题区域 */
.hero-section {
  text-align: center;
  padding: 40px 0 30px;
}

.hero-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--primary-gold-light);
  margin-bottom: 8px;
}

.hero-subtitle {
  font-size: 16px;
  color: var(--neutral-400);
}

/* 主要专区 */
.zones-grid {
  margin: 30px 0;
}

.zone-card {
  background: var(--gradient-card);
  border-radius: 20px;
  overflow: hidden;
  margin-bottom: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.zone-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-gold);
}

.zone-banner {
  height: 120px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zone-icon {
  font-size: 48px;
  color: white;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.zone-info {
  padding: 20px;
}

.zone-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--neutral-100);
  margin-bottom: 8px;
}

.zone-description {
  font-size: 14px;
  color: var(--neutral-400);
  line-height: 1.6;
  margin-bottom: 16px;
}

.zone-stats {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-gold);
}

.stat-label {
  font-size: 12px;
  color: var(--neutral-400);
  margin-top: 2px;
}

.zone-features {
  margin-bottom: 20px;
}

.feature-tag {
  display: inline-block;
  background: var(--primary-gold-alpha);
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 12px;
  margin: 2px 4px 2px 0;
}

.zone-actions {
  display: flex;
  gap: 12px;
}

.btn {
  flex: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
  text-decoration: none;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: rgba(40, 40, 40, 0.7);
  color: var(--neutral-200);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
  background: rgba(60, 60, 60, 0.7);
  border-color: var(--primary-gold);
}

.btn-secondary.followed {
  background: var(--gradient-secondary);
  color: white;
  border-color: var(--secondary-purple);
}

/* 特色专区 */
.special-zones {
  margin: 40px 0;
  padding-bottom: 65px;
}

.section-title {
  font-size: 20px;
  font-weight: bold;
  color: var(--primary-gold-light);
  margin-bottom: 20px;
  text-align: center;
}

.special-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.special-card {
  background: var(--gradient-card);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.special-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-gold);
}

.special-icon {
  font-size: 32px;
  color: var(--primary-gold);
  margin-bottom: 12px;
}

.special-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 8px;
}

.special-desc {
  font-size: 12px;
  color: var(--neutral-400);
  line-height: 1.4;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 0;
  z-index: 50;
}

.nav-items {
  display: flex;
  justify-content: space-around;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--neutral-400);
  transition: color 0.3s;
  padding: 8px 4px;
}

.nav-item.active {
  color: var(--primary-gold);
}

.nav-item:hover {
  color: var(--primary-gold-light);
}

.nav-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.nav-label {
  font-size: 12px;
}

.main-content {
  padding: 20px 0 80px 0;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .hero-title {
    font-size: 24px;
  }
  
  .zone-info {
    padding: 16px;
  }
  
  .zone-stats {
    gap: 16px;
  }
  
  .special-grid {
    gap: 12px;
  }
  
  .special-card {
    padding: 16px;
  }
}
</style> 