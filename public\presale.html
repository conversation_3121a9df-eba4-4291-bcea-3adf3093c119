<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>预售专区 - 凌云数资</title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: var(--primary-gold-light);
        }
        
        .search-section {
            margin: 20px 0;
        }
        
        .search-container {
            display: flex;
            background: var(--gradient-card);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .search-dropdown {
            background: var(--primary-gold-alpha);
            border: none;
            color: var(--primary-gold);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--neutral-100);
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: var(--neutral-400);
        }
        
        .search-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .search-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-gold);
        }
        
        .notification-bar {
            background: var(--gradient-card);
            border-radius: 12px;
            padding: 12px;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .presale-section {
            margin: 24px 0;
        }
        
        .presale-card {
            background: var(--gradient-card);
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }
        
        .presale-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-gold);
        }
        
        .presale-header {
            display: flex;
            padding: 16px;
            gap: 12px;
        }
        
        .presale-image {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-gold);
            font-size: 12px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .presale-info {
            flex: 1;
            min-width: 0;
        }
        
        .presale-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 6px;
        }
        
        .presale-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }
        
        .presale-price {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-gold);
        }
        
        .presale-quantity {
            font-size: 12px;
            color: var(--neutral-400);
        }
        
        .countdown-card {
            background: linear-gradient(135deg, var(--primary-gold-dark), var(--primary-gold));
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            margin: 16px 0;
        }
        
        .countdown-title {
            font-size: 14px;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }
        
        .countdown-timer {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 8px 0;
            gap: 8px;
        }
        
        .countdown-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .presale-actions {
            padding: 16px;
            display: flex;
            gap: 12px;
        }
        
        .reserve-btn {
            flex: 1;
            background: var(--gradient-primary);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .reserve-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-gold);
        }
        
        .reserve-btn.reserved {
            background: var(--accent-green);
        }
        
        .btn {
            display: inline-block;
            padding: 10px 24px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            min-width: 120px;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(200, 134, 13, 0.4);
        }
        
        .btn-secondary {
            background: var(--gradient-card);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-gold);
        }
        
        .tag {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
            border: 1px solid var(--primary-gold);
        }
        
        .tag-accent {
            background: rgba(220, 38, 38, 0.1);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }
        
        .tag-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }
        
        .tag-warning {
            background: rgba(234, 88, 12, 0.1);
            color: var(--accent-orange);
            border: 1px solid var(--accent-orange);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: var(--neutral-400);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .text-warning {
            color: var(--accent-orange);
        }
        
        .text-small {
            font-size: 12px;
            color: var(--neutral-400);
        }
        
        /* 响应式调整 */
        @media (max-width: 480px) {
            .container {
                padding: 0 12px;
            }
            
            .presale-header {
                padding: 12px;
            }
            
            .presale-image {
                width: 70px;
                height: 70px;
            }
            
            .presale-actions {
                padding: 12px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 style="font-size: 18px; font-weight: 600; color: var(--primary-gold);">预售专区</h1>
                <button class="back-btn" onclick="showNotifications()">
                    <i class="fas fa-bell"></i>
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-container">
                    <select class="search-dropdown">
                        <option>名称</option>
                        <option>关键字</option>
                        <option>发行方</option>
                    </select>
                    <input type="text" class="search-input" placeholder="搜索预售资产...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 提醒横幅 -->
            <div class="notification-bar">
                <i class="fas fa-bell text-warning"></i>
                <span class="text-small">预约成功后，我们将在开售前5分钟提醒您</span>
            </div>

            <!-- 预售列表 -->
            <section class="presale-section">
                <!-- 金沙太阳神鸟 -->
                <div class="presale-card">
                    <div class="presale-header">
                        <div class="presale-image">太阳神鸟</div>
                        <div class="presale-info">
                            <div class="presale-title">金沙太阳神鸟金箔</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-accent">限量发行</span>
                                <span class="tag tag-warning">即将开售</span>
                            </div>
                            <div class="presale-details">
                                <div class="presale-price">¥99.00</div>
                                <div class="presale-quantity">限量1000件</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="countdown-card">
                        <div class="countdown-title">开售倒计时</div>
                        <div class="countdown-timer">
                            <span id="hours1">02</span>:<span id="minutes1">15</span>:<span id="seconds1">30</span>
                        </div>
                        <div class="countdown-label">2024年1月10日12:00 开售</div>
                    </div>
                    
                    <div class="presale-actions">
                        <button class="reserve-btn" data-asset="金沙太阳神鸟金箔">
                            <i class="fas fa-bell"></i> 预约提醒
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 三星堆青铜面具 -->
                <div class="presale-card">
                    <div class="presale-header">
                        <div class="presale-image">青铜面具</div>
                        <div class="presale-info">
                            <div class="presale-title">三星堆青铜面具</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">考古发现</span>
                                <span class="tag tag-success">预售中</span>
                            </div>
                            <div class="presale-details">
                                <div class="presale-price">¥199.00</div>
                                <div class="presale-quantity">限量500件</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="countdown-card">
                        <div class="countdown-title">开售倒计时</div>
                        <div class="countdown-timer">
                            <span id="hours2">26</span>:<span id="minutes2">45</span>:<span id="seconds2">12</span>
                        </div>
                        <div class="countdown-label">2024年1月11日14:00 开售</div>
                    </div>
                    
                    <div class="presale-actions">
                        <button class="reserve-btn" data-asset="三星堆青铜面具">
                            <i class="fas fa-bell"></i> 预约提醒
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 金沙遗址3D模型 -->
                <div class="presale-card">
                    <div class="presale-header">
                        <div class="presale-image">3D模型</div>
                        <div class="presale-info">
                            <div class="presale-title">金沙遗址3D全景</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">3D模型</span>
                                <span class="tag tag-warning">下周开售</span>
                            </div>
                            <div class="presale-details">
                                <div class="presale-price">¥399.00</div>
                                <div class="presale-quantity">限量200件</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="countdown-card">
                        <div class="countdown-title">开售倒计时</div>
                        <div class="countdown-timer">
                            <span id="hours3">120</span>:<span id="minutes3">30</span>:<span id="seconds3">45</span>
                        </div>
                        <div class="countdown-label">2024年1月15日10:00 开售</div>
                    </div>
                    
                    <div class="presale-actions">
                        <button class="reserve-btn" data-asset="金沙遗址3D全景">
                            <i class="fas fa-bell"></i> 预约提醒
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 蜀锦织品纹样集 -->
                <div class="presale-card">
                    <div class="presale-header">
                        <div class="presale-image">蜀锦纹样</div>
                        <div class="presale-info">
                            <div class="presale-title">蜀锦织品纹样集</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">传统工艺</span>
                                <span class="tag tag-success">预售中</span>
                            </div>
                            <div class="presale-details">
                                <div class="presale-price">¥89.00</div>
                                <div class="presale-quantity">限量800件</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="countdown-card">
                        <div class="countdown-title">开售倒计时</div>
                        <div class="countdown-timer">
                            <span id="hours4">168</span>:<span id="minutes4">15</span>:<span id="seconds4">20</span>
                        </div>
                        <div class="countdown-label">2024年1月18日16:00 开售</div>
                    </div>
                    
                    <div class="presale-actions">
                        <button class="reserve-btn" data-asset="蜀锦织品纹样集">
                            <i class="fas fa-bell"></i> 预约提醒
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> 详情
                        </button>
                    </div>
                </div>

                <!-- 古蜀文明音乐 -->
                <div class="presale-card">
                    <div class="presale-header">
                        <div class="presale-image">古蜀音乐</div>
                        <div class="presale-info">
                            <div class="presale-title">古蜀文明音乐</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">音频</span>
                                <span class="tag tag-warning">月底开售</span>
                            </div>
                            <div class="presale-details">
                                <div class="presale-price">¥59.00</div>
                                <div class="presale-quantity">限量1500件</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="countdown-card">
                        <div class="countdown-title">开售倒计时</div>
                        <div class="countdown-timer">
                            <span id="hours5">264</span>:<span id="minutes5">00</span>:<span id="seconds5">00</span>
                        </div>
                        <div class="countdown-label">2024年1月20日20:00 开售</div>
                    </div>
                    
                    <div class="presale-actions">
                        <button class="reserve-btn" data-asset="古蜀文明音乐">
                            <i class="fas fa-bell"></i> 预约提醒
                        </button>
                        <button class="btn btn-secondary">
                            <i class="fas fa-info-circle"></i> 详情
                        </button>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item active">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 搜索功能
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchType = document.querySelector('.search-dropdown').value;
            const searchText = document.querySelector('.search-input').value;
            
            if (searchText.trim()) {
                alert(`${searchType}搜索预售资产: ${searchText}`);
            }
        });

        // 搜索框回车事件
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // 预约提醒功能
        document.querySelectorAll('.reserve-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const assetName = this.getAttribute('data-asset');
                
                if (this.classList.contains('reserved')) {
                    // 取消预约
                    this.classList.remove('reserved');
                    this.innerHTML = '<i class="fas fa-bell"></i> 预约提醒';
                    alert(`已取消${assetName}的预约提醒`);
                } else {
                    // 添加预约
                    this.classList.add('reserved');
                    this.innerHTML = '<i class="fas fa-check"></i> 已预约';
                    alert(`预约成功！我们将在${assetName}开售前5分钟提醒您`);
                }
            });
        });

        // 详情按钮
        document.querySelectorAll('.btn-secondary').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('跳转到资产详情页');
                // window.location.href = 'asset-detail.html';
            });
        });

        // 简单的倒计时更新（模拟）
        function updateCountdowns() {
            // 这里可以实现真实的倒计时逻辑
            // 为了演示，我们只是模拟更新
            const countdowns = document.querySelectorAll('.countdown-timer span');
            countdowns.forEach(span => {
                if (span.id && span.id.includes('seconds')) {
                    let seconds = parseInt(span.textContent);
                    if (seconds > 0) {
                        span.textContent = seconds - 1;
                    } else {
                        span.textContent = '59';
                        // 这里应该更新分钟
                    }
                }
            });
        }

        // 每秒更新倒计时
        setInterval(updateCountdowns, 1000);

        // 显示通知
        function showNotifications() {
            alert('打开消息通知');
        }

        // 卡片点击事件
        document.querySelectorAll('.presale-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是按钮，不触发卡片点击
                if (e.target.closest('button')) {
                    return;
                }
                
                // 否则跳转到详情页
                alert('跳转到资产详情页');
            });
        });

        // 添加点击效果
        document.querySelectorAll('.presale-card, .btn, .reserve-btn').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
