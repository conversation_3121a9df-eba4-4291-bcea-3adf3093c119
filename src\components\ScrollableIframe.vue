<template>
  <div class="iframe-container">
    <iframe
      ref="iframeRef"
      :src="iframeSrc"
      frameborder="0"
      class="scrollable-iframe"
      @load="onIframeLoad"
    ></iframe>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

interface Props {
  content: string
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '页面内容'
})

const iframeRef = ref<HTMLIFrameElement>()
const iframeSrc = ref('')

// 创建iframe内容
const createIframeContent = () => {
  const scriptTag = 'script'
  const scriptContent = `
        // 确保滚动正常工作
        document.body.style.overflowY = 'auto';
        document.documentElement.style.overflowY = 'auto';
        
        // 调试信息
        console.log('Iframe内容加载完成');
        console.log('页面高度:', document.body.scrollHeight);
        console.log('窗口高度:', window.innerHeight);
        
        // 测试滚动
        setTimeout(() => {
            window.scrollTo({ top: 100, behavior: 'smooth' });
            console.log('Iframe滚动测试执行');
        }, 1000);
    `
  
  const htmlContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${props.title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            overflow-y: auto !important;
            height: auto;
            min-height: 100vh;
            padding: 20px;
        }
        
        .content-wrapper {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="content-wrapper">
        ${props.content}
    </div>
    
    <${scriptTag}>
${scriptContent}
    </${scriptTag}>
</body>
</html>`

  const blob = new Blob([htmlContent], { type: 'text/html' })
  return URL.createObjectURL(blob)
}

const onIframeLoad = () => {
  console.log('📄 Iframe加载完成')
}

onMounted(() => {
  iframeSrc.value = createIframeContent()
})
</script>

<style scoped>
.iframe-container {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.scrollable-iframe {
  width: 100%;
  height: 100%;
  border: none;
  overflow-y: auto;
}
</style> 