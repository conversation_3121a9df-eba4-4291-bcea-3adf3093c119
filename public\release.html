<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>发行专区 - 四川省数字资产发行平�?/title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sichuan-red': '#d4313b',
                        'sichuan-gold': '#daa520',
                        'sichuan-dark': '#1a1a1a',
                        'sichuan-gray': '#2a2a2a'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0f0f0;
        }
        .glass-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
        .countdown-text {
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 10px rgba(218, 165, 32, 0.7);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-sichuan-dark to-sichuan-gray">
    <!-- 顶部导航�?-->
    <header class="sticky top-0 z-50 backdrop-blur-lg bg-black/30 border-b border-white/10">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <h1 class="text-lg font-bold text-white">发行专区</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-calendar text-lg"></i>
                    </button>
                    <button class="text-white/70 hover:text-white transition-colors relative">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="absolute -top-1 -right-1 w-2 h-2 bg-sichuan-red rounded-full"></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-md mx-auto px-4 pb-20">
        <!-- 热门发行横幅 -->
        <section class="py-6">
            <div class="glass-card rounded-2xl overflow-hidden bg-gradient-to-r from-sichuan-red/30 to-sichuan-gold/30">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop" 
                         alt="热门发行" class="w-full h-40 object-cover opacity-80">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div class="absolute bottom-4 left-4 right-4">
                        <h2 class="text-xl font-bold text-white mb-2">太阳神鸟限量�?/h2>
                        <p class="text-white/80 text-sm mb-3">古蜀文明巅峰之作，全球限�?000�?/p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-xs bg-sichuan-red text-white px-2 py-1 rounded-full">限量发行</span>
                                <span class="text-xs bg-sichuan-gold text-white px-2 py-1 rounded-full">预售�?/span>
                            </div>
                            <button class="bg-white text-black px-4 py-2 rounded-full text-sm font-medium hover:bg-white/90 transition-colors">
                                立即预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 发行状态导�?-->
        <section class="mb-6">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="whitespace-nowrap px-4 py-2 bg-sichuan-gold text-white rounded-full text-sm font-medium">
                    全部发行
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    预售�?
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    即将开�?
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    正在抢购
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    已售�?
                </button>
            </div>
        </section>

        <!-- 倒计时发�?-->
        <section class="mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-white font-semibold flex items-center">
                    <i class="fas fa-clock text-sichuan-gold mr-2"></i>
                    限时抢购
                </h3>
                <span class="text-sichuan-gold text-sm">距离结束</span>
            </div>
            
            <div class="glass-card rounded-2xl p-6 bg-gradient-to-r from-sichuan-red/20 to-orange-500/20">
                <div class="text-center mb-6">
                    <h4 class="text-white font-bold text-lg mb-2">三星堆青铜神�?/h4>
                    <p class="text-white/70 text-sm mb-4">神秘的古蜀文明青铜艺术</p>
                    
                    <!-- 倒计时显�?-->
                    <div class="flex justify-center items-center space-x-2 mb-4">
                        <div class="text-center">
                            <div class="countdown-text text-2xl font-bold text-sichuan-gold bg-black/40 px-3 py-2 rounded-lg">02</div>
                            <div class="text-xs text-white/60 mt-1">�?/div>
                        </div>
                        <div class="text-sichuan-gold text-xl">:</div>
                        <div class="text-center">
                            <div class="countdown-text text-2xl font-bold text-sichuan-gold bg-black/40 px-3 py-2 rounded-lg">15</div>
                            <div class="text-xs text-white/60 mt-1">�?/div>
                        </div>
                        <div class="text-sichuan-gold text-xl">:</div>
                        <div class="text-center">
                            <div class="countdown-text text-2xl font-bold text-sichuan-gold bg-black/40 px-3 py-2 rounded-lg">32</div>
                            <div class="text-xs text-white/60 mt-1">�?/div>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-white/60 text-sm">限量500�?/span>
                        <span class="text-sichuan-red font-bold text-lg">�?99</span>
                    </div>
                    
                    <!-- 进度�?-->
                    <div class="w-full bg-white/20 rounded-full h-2 mb-4">
                        <div class="bg-sichuan-gold h-2 rounded-full" style="width: 68%"></div>
                    </div>
                    <div class="text-white/60 text-sm mb-4">已售 340/500</div>
                    
                    <button class="w-full bg-sichuan-red text-white py-3 rounded-full font-medium hover:bg-sichuan-red/80 transition-colors">
                        <i class="fas fa-shopping-cart mr-2"></i>
                        立即抢购
                    </button>
                </div>
            </div>
        </section>

        <!-- 发行列表 -->
        <section>
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-white font-semibold">最新发�?/h3>
                <button class="text-sichuan-gold text-sm">
                    <i class="fas fa-filter mr-1"></i>筛�?
                </button>
            </div>
            
            <div class="space-y-4">
                <!-- 发行卡片1 - 预售�?-->
                <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                    <div class="flex">
                        <div class="w-24 h-24 bg-gradient-to-br from-sichuan-gold/20 to-yellow-500/20 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=150&fit=crop" 
                                 alt="金沙面具" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-white font-semibold">金沙太阳神鸟面具</h4>
                                <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">预售�?/span>
                            </div>
                            <p class="text-white/60 text-sm mb-2">限量1000�?�?发行价格 �?99</p>
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-white/50">
                                    <i class="fas fa-clock mr-1"></i>
                                    1�?0�?10:00 开�?
                                </div>
                                <button class="bg-sichuan-gold text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-sichuan-gold/80 transition-colors">
                                    预约提醒
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发行卡片2 - 即将开�?-->
                <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                    <div class="flex">
                        <div class="w-24 h-24 bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=150&fit=crop" 
                                 alt="蜀�? class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-white font-semibold">蜀锦云纹系�?/h4>
                                <span class="text-xs bg-yellow-500 text-white px-2 py-1 rounded-full">即将开�?/span>
                            </div>
                            <p class="text-white/60 text-sm mb-2">限量800�?�?发行价格 �?59</p>
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-white/50">
                                    <i class="fas fa-clock mr-1"></i>
                                    1�?2�?14:00 开�?
                                </div>
                                <button class="bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-yellow-500/80 transition-colors">
                                    设置提醒
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发行卡片3 - 热售�?-->
                <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                    <div class="flex">
                        <div class="w-24 h-24 bg-gradient-to-br from-red-500/20 to-pink-500/20 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=150&fit=crop" 
                                 alt="古蜀玉器" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-white font-semibold">古蜀玉器典藏</h4>
                                <span class="text-xs bg-sichuan-red text-white px-2 py-1 rounded-full">热售�?/span>
                            </div>
                            <p class="text-white/60 text-sm mb-2">限量600�?�?发行价格 �?19</p>
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-white/50">
                                    剩余 87/600 �?
                                </div>
                                <button class="bg-sichuan-red text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-sichuan-red/80 transition-colors">
                                    立即购买
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发行卡片4 - 已售�?-->
                <div class="glass-card rounded-2xl overflow-hidden hover-scale opacity-75">
                    <div class="flex">
                        <div class="w-24 h-24 bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=150&fit=crop" 
                                 alt="巴蜀印章" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-white font-semibold">巴蜀印章收藏</h4>
                                <span class="text-xs bg-gray-500 text-white px-2 py-1 rounded-full">已售�?/span>
                            </div>
                            <p class="text-white/60 text-sm mb-2">限量300�?�?发行价格 �?29</p>
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-white/50">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    已完�?
                                </div>
                                <button class="bg-gray-500 text-white px-3 py-1 rounded-full text-xs cursor-not-allowed">
                                    已售�?
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 发行卡片5 - 预约�?-->
                <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                    <div class="flex">
                        <div class="w-24 h-24 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=150&fit=crop" 
                                 alt="文物复刻" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1 p-4">
                            <div class="flex items-start justify-between mb-2">
                                <h4 class="text-white font-semibold">四川博物院联�?/h4>
                                <span class="text-xs bg-purple-500 text-white px-2 py-1 rounded-full">预约�?/span>
                            </div>
                            <p class="text-white/60 text-sm mb-2">限量2000�?�?发行价格 �?99</p>
                            <div class="flex items-center justify-between">
                                <div class="text-xs text-white/50">
                                    <i class="fas fa-users mr-1"></i>
                                    已预�?1,234 �?
                                </div>
                                <button class="bg-purple-500 text-white px-3 py-1 rounded-full text-xs font-medium hover:bg-purple-500/80 transition-colors">
                                    立即预约
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 我的预约 -->
        <section class="mt-8">
            <div class="glass-card rounded-2xl p-6">
                <h3 class="text-white font-semibold mb-4 flex items-center">
                    <i class="fas fa-bookmark text-sichuan-gold mr-2"></i>
                    我的预约 (3)
                </h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div>
                            <div class="text-white font-medium text-sm">太阳神鸟限量�?/div>
                            <div class="text-white/60 text-xs">1�?0�?10:00 开�?/div>
                        </div>
                        <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">已提�?/span>
                    </div>
                    <div class="text-center">
                        <a href="#" class="text-sichuan-gold text-sm">查看全部预约 �?/a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航�?-->
    <nav class="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-lg bg-black/30 border-t border-white/10">
        <div class="max-w-md mx-auto px-4 py-2">
            <div class="flex items-center justify-around">
                <a href="index.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">首页</span>
                </a>
                <a href="market.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-store text-lg mb-1"></i>
                    <span class="text-xs">市场</span>
                </a>
                <a href="collection.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-gem text-lg mb-1"></i>
                    <span class="text-xs">收藏</span>
                </a>
                <a href="profile.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-user text-lg mb-1"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 倒计时功�?
        function updateCountdown() {
            const now = new Date().getTime();
            const endTime = now + (2 * 3600 + 15 * 60 + 32) * 1000; // 2小时15�?2秒后
            
            const distance = endTime - now;
            
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);
            
            document.querySelectorAll('.countdown-text')[0].textContent = hours.toString().padStart(2, '0');
            document.querySelectorAll('.countdown-text')[1].textContent = minutes.toString().padStart(2, '0');
            document.querySelectorAll('.countdown-text')[2].textContent = seconds.toString().padStart(2, '0');
        }
        
        // 每秒更新倒计�?
        setInterval(updateCountdown, 1000);
        updateCountdown();

        // 分类筛�?
        document.querySelectorAll('section:nth-child(3) button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有active状�?
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.classList.remove('bg-sichuan-gold', 'text-white');
                    btn.classList.add('glass-card', 'text-white/70');
                });
                
                // 添加active状�?
                this.classList.remove('glass-card', 'text-white/70');
                this.classList.add('bg-sichuan-gold', 'text-white');
            });
        });

        // 预约按钮
        document.querySelectorAll('button:contains("预约")').forEach(button => {
            button.addEventListener('click', function() {
                const text = this.textContent.trim();
                if (text.includes('预约')) {
                    this.textContent = '已预�?;
                    this.classList.remove('bg-sichuan-gold', 'bg-yellow-500', 'bg-purple-500');
                    this.classList.add('bg-green-500');
                    alert('预约成功，我们将在开售前提醒�?);
                }
            });
        });

        // 立即购买按钮
        document.querySelectorAll('button:contains("立即购买")').forEach(button => {
            button.addEventListener('click', function() {
                alert('跳转到购买页�?);
            });
        });

        // 立即抢购按钮
        document.querySelectorAll('button:contains("立即抢购")').forEach(button => {
            button.addEventListener('click', function() {
                alert('跳转到抢购页�?);
            });
        });
    </script>
</body>
</html> 
