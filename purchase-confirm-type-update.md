# PurchaseConfirmView 类型更新记录

## 修改概述

将 `PurchaseConfirmView.vue` 页面中的 `AssetItem` 类型替换为 `AssetItemMore` 类型，并将库存显示从 `asset.issueQuantity` 更改为 `asset.availableStock`。

## 具体修改内容

### 1. 导入语句修改

**修改前**:
```typescript
import type { AssetItem } from '@/api/exhibition'
```

**修改后**:
```typescript
import type { AssetItemMore } from '@/api/exhibition'
```

### 2. 响应式数据类型修改

**修改前**:
```typescript
const asset = ref<AssetItem | null>(null)
```

**修改后**:
```typescript
const asset = ref<AssetItemMore | null>(null)
```

### 3. API响应类型修改

**修改前**:
```typescript
const response = await getAssetDetail(assetId.value) as {
  code: number
  msg: string
  data: AssetItem | null
}
```

**修改后**:
```typescript
const response = await getAssetDetail(assetId.value) as {
  code: number
  msg: string
  data: AssetItemMore | null
}
```

### 4. 模板中库存字段修改

**修改前**:
```vue
<div v-if="asset.issueQuantity" class="asset-stock">
  <span class="stock-label">库存：</span>
  <span class="stock-value">{{ asset.issueQuantity }}份</span>
</div>
```

**修改后**:
```vue
<div v-if="asset.availableStock" class="asset-stock">
  <span class="stock-label">库存：</span>
  <span class="stock-value">{{ asset.availableStock }}份</span>
</div>
```

## 修改原因和优势

### 1. 类型安全性提升

- `AssetItemMore` 提供了更完整和准确的类型定义
- 严格对应后端返回的数据结构
- 减少类型转换错误的可能性

### 2. 数据语义更准确

- `issueQuantity`: 发行数量（总的发行量）
- `availableStock`: 可用库存（当前可购买的数量）

使用 `availableStock` 更准确地反映了用户可以购买的实际库存数量。

### 3. 与后端数据结构一致

`AssetItemMore` 类型完全匹配后端返回的数据结构，包括：
- 基础信息字段
- 发行信息字段
- 库存信息字段
- 购买状态字段

## 影响范围

### 直接影响
- `PurchaseConfirmView.vue` 页面的类型定义
- 库存显示逻辑

### 无影响
- 其他页面和组件
- 现有的业务逻辑
- 用户界面显示效果

## 验证要点

### 1. 类型检查
- TypeScript 编译无错误
- IDE 智能提示正常工作

### 2. 功能验证
- 页面正常加载资产详情
- 库存数量正确显示
- 购买功能正常工作

### 3. 数据验证
- 确认后端返回的数据包含 `availableStock` 字段
- 验证 `availableStock` 值的准确性

## 后续建议

### 1. 统一类型使用
建议在其他相关页面也逐步使用 `AssetItemMore` 类型，特别是：
- 资产详情页面
- 支付确认页面
- 其他需要完整资产信息的页面

### 2. API接口优化
确保 `getAssetDetail` API 返回的数据结构与 `AssetItemMore` 类型完全匹配。

### 3. 文档更新
更新相关的API文档和类型定义文档，确保团队成员了解新的类型结构。

## 测试建议

1. **单元测试**: 验证类型定义的正确性
2. **集成测试**: 测试页面加载和数据显示
3. **端到端测试**: 验证完整的购买流程

通过这次类型更新，`PurchaseConfirmView.vue` 页面现在使用了更准确的类型定义和更合适的库存字段，提高了代码的类型安全性和数据准确性。
