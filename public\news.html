<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>新闻资讯 - 四川省数字资产发行平�?/title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sichuan-red': '#d4313b',
                        'sichuan-gold': '#daa520',
                        'sichuan-dark': '#1a1a1a',
                        'sichuan-gray': '#2a2a2a'
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0f0f0;
        }
        .glass-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-sichuan-dark to-sichuan-gray">
    <!-- 顶部导航�?-->
    <header class="sticky top-0 z-50 backdrop-blur-lg bg-black/30 border-b border-white/10">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <h1 class="text-lg font-bold text-white">新闻资讯</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-bookmark text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-md mx-auto px-4 pb-20">
        <!-- 重要公告 -->
        <section class="py-6">
            <div class="glass-card rounded-2xl p-4 bg-gradient-to-r from-sichuan-red/20 to-orange-500/20 border-sichuan-red/30">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-sichuan-red rounded-full flex items-center justify-center">
                        <i class="fas fa-bullhorn text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-white font-semibold text-sm mb-1">重要公告</h3>
                        <p class="text-white/80 text-xs">平台将于1�?0日进行系统升级维�?/p>
                    </div>
                    <i class="fas fa-chevron-right text-white/50"></i>
                </div>
            </div>
        </section>

        <!-- 分类导航 -->
        <section class="mb-6">
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="whitespace-nowrap px-4 py-2 bg-sichuan-gold text-white rounded-full text-sm font-medium">
                    全部资讯
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    平台动�?
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    行业资讯
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    文化解读
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    政策解读
                </button>
            </div>
        </section>

        <!-- 推荐文章 -->
        <section class="mb-8">
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop" 
                         alt="推荐文章" class="w-full h-48 object-cover">
                    <div class="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div class="absolute top-3 left-3">
                        <span class="text-xs bg-sichuan-red text-white px-2 py-1 rounded-full">头条</span>
                    </div>
                    <div class="absolute bottom-4 left-4 right-4">
                        <h2 class="text-white font-bold text-lg mb-2 leading-tight">三星堆遗址最新考古发现：数字化重现古蜀文明辉煌</h2>
                        <div class="flex items-center justify-between text-white/70 text-xs">
                            <div class="flex items-center space-x-4">
                                <span><i class="fas fa-eye mr-1"></i>12.5�?/span>
                                <span><i class="fas fa-thumbs-up mr-1"></i>3.2�?/span>
                            </div>
                            <span>2小时�?/span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 文章列表 -->
        <section class="space-y-4">
            <!-- 文章1 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-blue-500 text-white px-2 py-1 rounded-full">平台动�?/span>
                            <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">置顶</span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">四川数字资产平台正式启动，打造西部数字文化新高地</h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>8.9�?/span>
                                <span><i class="fas fa-comment mr-1"></i>234</span>
                            </div>
                            <span>5小时�?/span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 文章2 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-purple-500 text-white px-2 py-1 rounded-full">文化解读</span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">深度解析：太阳神鸟背后的古蜀文明密码</h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>5.6�?/span>
                                <span><i class="fas fa-comment mr-1"></i>167</span>
                            </div>
                            <span>8小时�?/span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 文章3 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-orange-500 text-white px-2 py-1 rounded-full">行业资讯</span>
                            <span class="text-xs bg-sichuan-red text-white px-2 py-1 rounded-full">热门</span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">数字藏品市场迎来新机遇，文化IP成为投资热点</h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>12.3�?/span>
                                <span><i class="fas fa-comment mr-1"></i>456</span>
                            </div>
                            <span>12小时�?/span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 文章4 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-indigo-500 text-white px-2 py-1 rounded-full">政策解读</span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">国家文物局发布数字文物保护新规�?/h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>3.8�?/span>
                                <span><i class="fas fa-comment mr-1"></i>89</span>
                            </div>
                            <span>1天前</span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 文章5 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">平台动�?/span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">金沙遗址博物馆与平台达成战略合作</h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>6.7�?/span>
                                <span><i class="fas fa-comment mr-1"></i>203</span>
                            </div>
                            <span>2天前</span>
                        </div>
                    </div>
                </div>
            </article>

            <!-- 文章6 -->
            <article class="glass-card rounded-2xl p-4 hover-scale">
                <div class="flex space-x-3">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=120&fit=crop" 
                         alt="文章配图" class="w-20 h-16 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-xs bg-cyan-500 text-white px-2 py-1 rounded-full">文化解读</span>
                        </div>
                        <h3 class="text-white font-semibold text-sm mb-2 leading-tight">蜀锦技艺传承千年，数字化让非遗焕发新生</h3>
                        <div class="flex items-center justify-between text-white/60 text-xs">
                            <div class="flex items-center space-x-3">
                                <span><i class="fas fa-eye mr-1"></i>4.2�?/span>
                                <span><i class="fas fa-comment mr-1"></i>156</span>
                            </div>
                            <span>3天前</span>
                        </div>
                    </div>
                </div>
            </article>
        </section>

        <!-- 加载更多 -->
        <section class="mt-8 text-center">
            <button class="glass-card px-8 py-3 rounded-full text-white/70 hover:text-white transition-colors">
                <i class="fas fa-spinner mr-2"></i>加载更多
            </button>
        </section>
    </main>

    <!-- 底部导航�?-->
    <nav class="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-lg bg-black/30 border-t border-white/10">
        <div class="max-w-md mx-auto px-4 py-2">
            <div class="flex items-center justify-around">
                <a href="index.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">首页</span>
                </a>
                <a href="market.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-store text-lg mb-1"></i>
                    <span class="text-xs">市场</span>
                </a>
                <a href="collection.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-gem text-lg mb-1"></i>
                    <span class="text-xs">收藏</span>
                </a>
                <a href="profile.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-user text-lg mb-1"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 分类筛�?
        document.querySelectorAll('section:nth-child(3) button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有active状�?
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.classList.remove('bg-sichuan-gold', 'text-white');
                    btn.classList.add('glass-card', 'text-white/70');
                });
                
                // 添加active状�?
                this.classList.remove('glass-card', 'text-white/70');
                this.classList.add('bg-sichuan-gold', 'text-white');
            });
        });

        // 文章点击
        document.querySelectorAll('article').forEach(article => {
            article.addEventListener('click', function() {
                alert('跳转到文章详情页�?);
            });
        });

        // 搜索功能
        document.querySelector('.fa-search').parentElement.addEventListener('click', function() {
            const query = prompt('请输入搜索关键词�?);
            if (query) {
                alert(`搜索�?{query}`);
            }
        });

        // 收藏功能
        document.querySelector('.fa-bookmark').parentElement.addEventListener('click', function() {
            alert('我的收藏');
        });

        // 加载更多
        document.querySelector('button:contains("加载更多")').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>加载�?..';
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-spinner mr-2"></i>加载更多';
                alert('已加载更多内�?);
            }, 2000);
        });
    </script>
</body>
</html> 
