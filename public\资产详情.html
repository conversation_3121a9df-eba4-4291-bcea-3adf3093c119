<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>赛柏蕉子：神蕉侠侣系列盲�?- 详情</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #e0e0e0;
            font-size: 14px;
            line-height: 1.6;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 480px;
            margin: 0 auto;
            padding-bottom: 90px;
        }
        
        /* 顶部导航 */
        .app-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 15px 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(15, 15, 15, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #222;
        }
        
        .back-btn {
            color: #fff;
            font-size: 20px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .app-title {
            font-size: 18px;
            font-weight: 700;
            color: #fff;
            text-align: center;
            flex: 1;
        }
        
        .share-btn {
            color: #fff;
            font-size: 18px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }
        
        /* 商品主图 */
        .product-hero {
            position: relative;
            height: 320px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #1c1c1c, #2a2a2a);
            border-bottom: 1px solid #333;
        }
        
        .product-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transform: scale(0.9);
            transition: transform 0.5s ease;
        }
        
        .limited-tag {
            position: absolute;
            top: 20px;
            right: 20px;
            background: #ff4d4f;
            color: #fff;
            font-size: 14px;
            padding: 6px 15px;
            border-radius: 20px;
            font-weight: 500;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
        }
        
        /* 商品信息 */
        .product-info {
            padding: 20px 15px;
        }
        
        .product-title {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 10px;
            line-height: 1.3;
        }
        
        .product-subtitle {
            font-size: 16px;
            color: #aaa;
            margin-bottom: 15px;
        }
        
        .product-stats {
            display: flex;
            justify-content: space-between;
            background: rgba(40, 40, 40, 0.7);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }
        
        .stat-item {
            text-align: center;
            flex: 1;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 13px;
            color: #8a8a8a;
        }
        
        .divider {
            height: 40px;
            width: 1px;
            background: #333;
            margin: 0 10px;
        }
        
        /* 内容区域 */
        .content-section {
            margin-bottom: 30px;
            padding: 0 15px;
        }
        
        .section-header {
            font-size: 20px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #4dabf7;
            display: flex;
            align-items: center;
        }
        
        .section-header i {
            margin-right: 10px;
            color: #4dabf7;
        }
        
        .content-card {
            background: rgba(30, 30, 30, 0.8);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid #333;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .story-item {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #333;
        }
        
        .story-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .story-label {
            font-weight: bold;
            color: #4dabf7;
            min-width: 80px;
        }
        
        .story-content {
            flex: 1;
            color: #ccc;
        }
        
        .feature-list {
            list-style-type: none;
        }
        
        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px dashed #333;
            display: flex;
            align-items: flex-start;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "�?;
            color: #4dabf7;
            font-weight: bold;
            margin-right: 10px;
            font-size: 18px;
        }
        
        /* 倒计�?*/
        .countdown-card {
            background: linear-gradient(135deg, #1a2b3c, #0d1a26);
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            margin: 20px 15px;
            border: 1px solid rgba(77, 171, 247, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .countdown-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(77, 171, 247, 0.1) 0%, transparent 70%);
            z-index: 0;
        }
        
        .countdown-title {
            font-size: 18px;
            font-weight: bold;
            color: #4dabf7;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .countdown-timer {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28px;
            font-weight: bold;
            color: #fff;
            position: relative;
            z-index: 1;
            letter-spacing: 2px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .countdown-timer span {
            display: inline-block;
            min-width: 44px;
            text-align: center;
            background: rgba(0, 30, 60, 0.7);
            padding: 8px;
            border-radius: 8px;
            margin: 0 5px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .countdown-label {
            font-size: 14px;
            color: #4dabf7;
            margin-top: 5px;
            position: relative;
            z-index: 1;
        }
        
        /* 购买按钮 */
        .purchase-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(20, 20, 20, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid #333;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .price-info {
            flex: 1;
        }
        
        .price-label {
            font-size: 14px;
            color: #8a8a8a;
            margin-bottom: 5px;
        }
        
        .price {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
        }
        
        .buy-btn {
            background: linear-gradient(135deg, #4dabf7, #1890ff);
            color: white;
            border: none;
            padding: 16px 40px;
            border-radius: 30px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(77, 171, 247, 0.4);
            transition: all 0.3s;
        }
        
        .buy-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(77, 171, 247, 0.6);
        }
        
        /* 响应式调�?*/
        @media (max-width: 380px) {
            .product-hero {
                height: 280px;
            }
            
            .product-title {
                font-size: 22px;
            }
            
            .countdown-timer {
                font-size: 24px;
            }
            
            .countdown-timer span {
                min-width: 36px;
                padding: 6px;
            }
            
            .buy-btn {
                padding: 14px 30px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="app-bar">
            <div class="back-btn">
                <i class="fas fa-arrow-left"></i>
            </div>
            <div class="app-title">商品详情</div>
            <div class="share-btn">
                <i class="fas fa-share-alt"></i>
            </div>
        </div>
        
        <!-- 商品主图 -->
        <div class="product-hero">
            <img src="https://images.unsplash.com/photo-1620336655052-b57986f5a26a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="赛柏蕉子：神蕉侠侣系列盲�? class="product-image">
            <div class="limited-tag">限量5000�?/div>
        </div>
        
        <!-- 商品信息 -->
        <div class="product-info">
            <h1 class="product-title">赛柏蕉子：神蕉侠侣系列盲�?/h1>
            <div class="product-subtitle">购买即可体验精彩内容</div>
            
            <div class="product-stats">
                <div class="stat-item">
                    <div class="stat-value">5000�?/div>
                    <div class="stat-label">限量发行</div>
                </div>
                <div class="divider"></div>
                <div class="stat-item">
                    <div class="stat-value">�?0.00</div>
                    <div class="stat-label">价格</div>
                </div>
                <div class="divider"></div>
                <div class="stat-item">
                    <div class="stat-value">12000+</div>
                    <div class="stat-label">关注</div>
                </div>
            </div>
        </div>
        
        <!-- 倒计�?-->
        <div class="countdown-card">
            <div class="countdown-title">优先购开始提�?/div>
            <div class="countdown-timer">
                <span>19</span>:<span>57</span>:<span>19</span>
            </div>
            <div class="countdown-label">即将开�?/div>
        </div>
        
        <!-- 作品故事 -->
        <div class="content-section">
            <div class="section-header">
                <i class="fas fa-book-open"></i>
                作品故事
            </div>
            
            <div class="content-card">
                <div class="story-item">
                    <div class="story-label">出品�?/div>
                    <div class="story-content">小海�?| MOUNTAINSEA</div>
                </div>
                
                <div class="story-item">
                    <div class="story-label">创作理念</div>
                    <div class="story-content">赛柏蕉子系列融合了赛博朋克美学与东方元素，创造出未来感十足的香蕉人角色。神蕉侠侣作为系列首作，讲述了在数字世界中一对香蕉侠侣守护正义的故事�?/div>
                </div>
                
                <div class="story-item">
                    <div class="story-label">系列内容</div>
                    <div class="story-content">本系列包�?款常规款�?款隐藏款，每款角色都有独特的设计背景故事和数字资产�?/div>
                </div>
            </div>
        </div>
        
        <!-- 优先购信�?-->
        <div class="content-section">
            <div class="section-header">
                <i class="fas fa-crown"></i>
                优先�?            </div>
            
            <div class="content-card">
                <ul class="feature-list">
                    <li>
                        <div>
                            <strong>限量4500�?/strong>
                            <p style="color: #aaa; margin-top: 5px;">优先购专属限量版本，包含特殊数字权益</p>
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>规则说明</strong>
                            <p style="color: #aaa; margin-top: 5px;">优先购仅限订阅用户参与，每位用户限购2�?/p>
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>特权内容</strong>
                            <p style="color: #aaa; margin-top: 5px;">优先购用户将获得专属数字收藏证书和未来空投权�?/p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 预告信息 -->
        <div class="content-section">
            <div class="section-header">
                <i class="fas fa-calendar-alt"></i>
                发售预告
            </div>
            
            <div class="content-card">
                <ul class="feature-list">
                    <li>
                        <div>
                            <strong>第一轮发�?/strong>
                            <p style="color: #aaa; margin-top: 5px;">限量4000份，06�?5�?11:00开�?/p>
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>第二轮发�?/strong>
                            <p style="color: #aaa; margin-top: 5px;">限量1000份，06�?6�?14:00开�?/p>
                        </div>
                    </li>
                    <li>
                        <div>
                            <strong>隐藏款概�?/strong>
                            <p style="color: #aaa; margin-top: 5px;">每盒包含1个角色，隐藏款概率为1:96</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 购买按钮 -->
        <div class="purchase-bar">
            <div class="price-info">
                <div class="price-label">当前价格</div>
                <div class="price">�?0.00</div>
            </div>
            <button class="buy-btn">立即购买</button>
        </div>
    </div>

    <script>
        // 倒计时效�?        function updateCountdown() {
            const timerElement = document.querySelector('.countdown-timer');
            const spans = timerElement.querySelectorAll('span');
            
            // 设置目标时间�?9:57:19
            const now = new Date();
            const targetTime = new Date(now);
            targetTime.setHours(19, 57, 19, 0);
            
            // 如果目标时间已过，则设置为明天的同一时间
            if (now > targetTime) {
                targetTime.setDate(targetTime.getDate() + 1);
            }
            
            // 计算时间�?            const diff = targetTime - now;
            
            if (diff <= 0) {
                timerElement.innerHTML = '<span>00</span>:<span>00</span>:<span>00</span>';
                return;
            }
            
            // 计算小时、分钟、秒
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            // 更新显示
            spans[0].textContent = hours.toString().padStart(2, '0');
            spans[1].textContent = minutes.toString().padStart(2, '0');
            spans[2].textContent = seconds.toString().padStart(2, '0');
        }
        
        // 返回按钮功能
        document.querySelector('.back-btn').addEventListener('click', function() {
            alert('返回上一�?);
        });
        
        // 分享按钮功能
        document.querySelector('.share-btn').addEventListener('click', function() {
            alert('分享商品');
        });
        
        // 购买按钮功能
        document.querySelector('.buy-btn').addEventListener('click', function() {
            alert('开始购买流�?);
        });
        
        // 初始调用并设置定时器
        updateCountdown();
        setInterval(updateCountdown, 1000);
    </script>
</body>
</html>
