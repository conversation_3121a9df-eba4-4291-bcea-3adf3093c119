<template>
  <nav class="bottom-nav bottom-nav-fixed">
    <div class="container">
      <div class="nav-items">
        <router-link 
          to="/" 
          class="nav-item" 
          :class="{ active: $route.name === 'home' }"
        >
          <div class="nav-icon-wrapper">
            <i class="nav-icon fas fa-home"></i>
          </div>
          <span class="nav-label">首页</span>
        </router-link>
        
        <router-link 
          to="/exhibition" 
          class="nav-item" 
          :class="{ active: $route.name === 'exhibition' }"
        >
          <div class="nav-icon-wrapper">
            <i class="nav-icon fas fa-store"></i>
          </div>
          <span class="nav-label">资产</span>
        </router-link>
        
        <!-- <router-link 
          to="/activities" 
          class="nav-item" 
          :class="{ active: $route.name === 'activities' }"
        >
          <div class="nav-icon-wrapper">
            <i class="nav-icon fas fa-calendar-alt activity-icon"></i>
          </div>
          <span class="nav-label">活动</span>
        </router-link> -->
        
        <router-link 
          to="/profile" 
          class="nav-item" 
          :class="{ active: $route.name === 'profile' }"
        >
          <div class="nav-icon-wrapper">
            <i class="nav-icon fas fa-user"></i>
          </div>
          <span class="nav-label">我的</span>
        </router-link>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
// 底部导航组件 - 统一的导航栏
// 根据当前路由自动高亮对应的导航项
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
.bottom-nav {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
  
  /* 底部导航样式 */
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px); /* Safari兼容性 */
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 4px 0;
  z-index: 9999 !important; /* 使用更高的z-index确保在最顶层 */
  box-shadow: var(--shadow-md);
  /* 确保在所有设备上固定定位正常工作 */
  transform: translateZ(0); /* 强制硬件加速 */
  /* 额外保障样式 */
  max-width: 100vw;
  min-height: 65px;
  box-sizing: border-box;
  /* 防止在某些设备上底部导航闪烁或位置不固定 */
  will-change: transform;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  -webkit-perspective: 1000;
  -webkit-backface-visibility: hidden;
  /* 强制创建层叠上下文，确保固定定位 */
  contain: layout;
  isolation: isolate;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

.nav-items {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--neutral-400);
  transition: all 0.3s ease;
  padding: 6px 4px;
  border-radius: 12px;
  position: relative;
  min-width: 60px;
}

.nav-item:hover {
  color: var(--primary-gold-light);
  background: var(--primary-gold-alpha);
  transform: translateY(-1px);
}

.nav-item.active {
  color: var(--primary-gold);
  background: var(--primary-gold-alpha);
  box-shadow: var(--shadow-gold);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background: var(--gradient-primary);
  border-radius: 1px;
}

/* 原始图标样式已移至统一图标样式部分 */

.nav-label {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
  margin-top: 2px;
}

.nav-item:hover .nav-label {
  font-weight: 600;
}

.nav-item.active .nav-label {
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 380px) {
  .bottom-nav {
    padding: 3px 0;
  }
  
  .container {
    padding: 0 8px;
  }
  
  .nav-item {
    padding: 4px 2px;
    min-width: 50px;
  }
  
  .nav-label {
    font-size: 10px;
  }
  
  .nav-icon-wrapper {
    width: 28px;
    height: 28px;
  }
  
  .nav-icon {
    font-size: 14px !important;
  }
}

/* 确保在所有浏览器和设备上固定定位正常工作 */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari 特定优化 */
  .bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100vw;
  }
}

/* 添加全局样式重置，防止其他元素影响底部导航 */
.bottom-nav * {
  box-sizing: border-box;
}

/* 确保在任何情况下底部导航都保持固定 */
.bottom-nav-fixed {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  z-index: 99999 !important; /* 提高z-index */
  
  /* 强制重置可能被页面样式影响的属性 */
  transform: translateZ(0) !important;
  margin: 0 !important;
  top: auto !important;
  height: auto !important;
  max-height: none !important;
  min-height: 65px !important;
  overflow: visible !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  
  /* 防止强制滚动模式影响 */
  contain: layout !important;
  will-change: auto !important;
  backface-visibility: hidden !important;
  
  /* 确保脱离文档流，不受其他样式影响 */
  isolation: isolate !important;
  
  /* 强制创建新的堆叠上下文 */
  -webkit-transform: translateZ(0) !important;
  -moz-transform: translateZ(0) !important;
  -ms-transform: translateZ(0) !important;
  -o-transform: translateZ(0) !important;
  
  /* 强制GPU加速，但避免影响其他元素 */
  -webkit-perspective: 1000px !important;
  -webkit-backface-visibility: hidden !important;
  
  /* 重置所有可能的定位相关属性 */
  float: none !important;
  clear: none !important;
  position: fixed !important;
}

/* 统一图标样式 */
.nav-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 图标基础样式 */
.nav-icon {
  font-size: 16px !important;
  margin-bottom: 0 !important;
  transition: all 0.3s ease;
  color: inherit;
}

/* 悬浮状态 */
.nav-item:hover .nav-icon-wrapper {
  background: var(--primary-gold-alpha);
  border-color: var(--primary-gold);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(200, 134, 13, 0.2);
}

.nav-item:hover .nav-icon {
  color: var(--primary-gold-light);
  transform: scale(1.1);
}

/* 激活状态 */
.nav-item.active .nav-icon-wrapper {
  background: var(--gradient-primary);
  border-color: var(--primary-gold);
  box-shadow: var(--shadow-gold);
}

.nav-item.active .nav-icon {
  color: white;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  animation: icon-glow 2s infinite;
}

/* 备用图标样式 - 如果FontAwesome无法加载 */
.activity-icon {
  font-style: normal !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Segoe UI Emoji', 'Apple Color Emoji', 'Noto Color Emoji', sans-serif !important;
}

/* 活动徽章样式 */
.activity-badge {
  position: absolute;
  top: -4px;
  right: -8px;
  background: linear-gradient(135deg, #FF4757, #FF6B7A);
  color: white;
  font-size: 8px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 6px;
  line-height: 1;
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
  animation: badge-bounce 3s infinite;
  letter-spacing: 0.5px;
}

/* 动画效果 */
@keyframes icon-glow {
  0%, 100% {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  }
  50% {
    text-shadow: 0 0 16px rgba(255, 255, 255, 0.6), 0 0 24px rgba(200, 134, 13, 0.4);
  }
}

@keyframes badge-bounce {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* 响应式调整 */
@media (max-width: 380px) {
  .nav-icon-wrapper {
    width: 28px;
    height: 28px;
  }
  
  .nav-icon {
    font-size: 14px !important;
  }
  
  .activity-badge {
    font-size: 7px;
    padding: 1px 3px;
    top: -3px;
    right: -6px;
  }
}

/* 毛玻璃效果增强 */
@supports (backdrop-filter: blur(20px)) {
  .bottom-nav {
    background: rgba(37, 37, 48, 0.7);
    backdrop-filter: blur(20px) saturate(180%);
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .bottom-nav {
    background: rgba(15, 15, 21, 0.8);
    border-top-color: rgba(255, 255, 255, 0.05);
  }
}
</style> 