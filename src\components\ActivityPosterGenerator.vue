<template>
  <div class="poster-generator" v-if="visible">
    <!-- 遮罩层 -->
    <div class="poster-overlay" @click="closePoster"></div>
    
    <!-- 海报内容 -->
    <div class="poster-content">
      <div class="poster-header">
        <h3>{{ activity.activityName }}</h3>
        <button class="close-btn" @click="closePoster">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <!-- 海报画布（内容区，滚动） -->
      <div class="poster-canvas" ref="posterRef">
        <div class="poster-bg">
          <div class="poster-cover">
            <img :src="activity.activityCover" :alt="activity.activityName" />
            <div class="cover-overlay">
              <div class="poster-brand">凌云数资</div>
            </div>
          </div>
          <!-- <div class="poster-info">
            <h2 class="poster-title">{{ activity.activityName }}</h2>
          </div> -->
          <div class="qr-section">
            <div class="qr-container">
              <div class="qr-code" ref="qrCodeRef">
                <canvas 
                  v-if="!qrCodeError" 
                  ref="qrCanvas"
                  class="qr-canvas"
                ></canvas>
                <div v-else class="qr-fallback">
                  <i class="fas fa-qrcode"></i>
                  <div class="qr-fallback-text">二维码</div>
                </div>
              </div>
            </div>
          </div>
          <div class="invite-link-section" :class="{ 'primary': qrCodeError }">
            <div class="invite-link-label">邀请链接</div>
            <div class="invite-link-wrapper">
              <div class="invite-link-text">{{ inviteUrl }}</div>
              <button class="copy-link-btn" @click="copyInviteLink">
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
          <div class="invite-code-section">
            <div class="invite-code">
              <span class="invite-label">邀请码：</span>
              <span class="invite-value">{{ activity.inviteCode }}</span>
              <button class="copy-btn" @click="copyInviteCode">
                <i class="fas fa-copy"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- 操作按钮始终在底部 -->
      <div class="poster-actions">
        <button class="action-btn download-btn" @click="downloadPoster">
          <i class="fas fa-download"></i>
          下载海报
        </button>
        <button class="action-btn share-btn" @click="sharePoster">
          <i class="fas fa-share-alt"></i>
          分享海报
        </button>
      </div>
    </div>
    <!-- 图片预览弹窗，移动端长按可保存 -->
    <div v-if="showImagePreview" class="image-preview-mask" @click="showImagePreview = false">
      <img :src="previewImageUrl" class="preview-img" @click.stop />
      <div class="preview-tip">长按图片保存到相册<br/>（点击空白处关闭）</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useNotification } from '@/composables/useNotification'
import type { ActivityDetail } from '@/api/activity'
import QRCode from 'qrcode'
import html2canvas from 'html2canvas' // 新增：导入html2canvas

// Props
interface Props {
  visible: boolean
  activity: ActivityDetail
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 通知系统
const { success, error, info } = useNotification()

// 引用
const posterRef = ref<HTMLElement>()
const qrCodeRef = ref<HTMLElement>()
const qrCanvas = ref<HTMLCanvasElement>()

// 二维码状态
const qrCodeError = ref(false)

// 新增：图片预览弹窗相关变量
const showImagePreview = ref(false)
const previewImageUrl = ref('')

/**
 * 邀请链接URL
 */
const inviteUrl = computed(() => {
  const baseUrl = window.location.origin
  return `${baseUrl}/invite-register?inviteCode=${props.activity.inviteCode}`
})

/**
 * 100%本地生成二维码到Canvas
 */
const generateQRCode = async () => {
  if (!qrCanvas.value) {
    qrCodeError.value = true
    return
  }
  try {
    await QRCode.toCanvas(qrCanvas.value, inviteUrl.value, {
      width: 160,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    qrCodeError.value = false
  } catch (err) {
    qrCodeError.value = true
    console.error('本地二维码生成失败', err)
  }
}

/**
 * 重新生成二维码
 */
const regenerateQRCode = async () => {
  qrCodeError.value = false
  await nextTick()
  generateQRCode()
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string): string => {
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    })
  } catch {
    return dateStr
  }
}

/**
 * 关闭海报
 */
const closePoster = () => {
  emit('close')
}

/**
 * 复制邀请码
 */
const copyInviteCode = async () => {
  try {
    await navigator.clipboard.writeText(props.activity.inviteCode || '')
    success('邀请码已复制到剪贴板')
  } catch {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = props.activity.inviteCode || ''
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('邀请码已复制到剪贴板')
  }
}

/**
 * 复制邀请链接
 */
const copyInviteLink = async () => {
  try {
    await navigator.clipboard.writeText(inviteUrl.value)
    success('邀请链接已复制到剪贴板')
  } catch {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = inviteUrl.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('邀请链接已复制到剪贴板')
  }
}

/**
 * 下载海报
 * 使用html2canvas将海报区域（含封面、活动名、二维码）渲染为图片并下载，并弹出预览支持长按保存
 */
const downloadPoster = async () => {
  try {
    info('正在生成海报图片...')
    // 1. 获取海报区域DOM（.poster-bg 包含封面、活动名、二维码）
    const posterEl = posterRef.value?.querySelector('.poster-bg') as HTMLElement
    if (!posterEl) {
      error('未找到海报内容')
      return
    }
    // 2. 用html2canvas渲染为canvas图片
    const canvas = await html2canvas(posterEl, {
      useCORS: true, // 支持跨域图片（活动封面需支持CORS）
      backgroundColor: null, // 保持透明
      scale: 2 // 高清输出
    })
    // 3. 转为图片并自动下载
    const url = canvas.toDataURL('image/png')
    const link = document.createElement('a')
    link.href = url
    link.download = `${props.activity.activityName || '邀新海报'}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    // 4. 手机端弹出图片预览，提示长按保存
    previewImageUrl.value = url
    showImagePreview.value = true
    success('长按图片可保存到相册')
  } catch (err) {
    error('海报下载失败')
    console.error('Download poster error:', err)
  }
}

/**
 * 分享海报
 */
const sharePoster = async () => {
  try {
    if (navigator.share) {
      await navigator.share({
        title: `${props.activity.activityName} - 邀新活动`,
        text: `邀请您参与${props.activity.activityName}活动，扫码注册即可参与！`,
        url: `${window.location.origin}/invite-register?inviteCode=${props.activity.inviteCode}`
      })
    } else {
      // 备用分享方式：复制链接
      const shareUrl = `${window.location.origin}/invite-register?inviteCode=${props.activity.inviteCode}`
      await navigator.clipboard.writeText(shareUrl)
      success('分享链接已复制到剪贴板')
    }
  } catch (err) {
    error('分享失败')
    console.error('Share poster error:', err)
  }
}

// 监听visible变化，显示时生成二维码
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    nextTick(() => {
      setTimeout(() => {
        generateQRCode()
      }, 200)
    })
  }
}, { immediate: true })

onMounted(() => {
  // 组件挂载时不主动生成，等visible为true时生成
})
</script>

<style scoped>
.poster-generator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.poster-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.poster-content {
  position: relative;
  background: #1a1a1a;
  border-radius: 16px;
  padding: 20px;
  max-width: 400px;
  width: 100%;
  max-height: 90vh;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
}
.poster-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
}
.poster-header h3 {
  color: #d4a574;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #999;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  color: #d4a574;
  background: rgba(212, 165, 116, 0.1);
}

/* 海报画布 */
.poster-canvas {
  flex: 1 1 auto;
  overflow-y: auto;
  margin-bottom: 12px;
  min-height: 0;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.poster-bg {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  position: relative;
}

/* 活动封面 */
.poster-cover {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.poster-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.poster-brand {
  color: #d4a574;
  font-size: 20px;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

/* 活动信息 */
.poster-info {
  padding: 20px;
  text-align: center;
}

.poster-title {
  font-size: 18px;
  font-weight: bold;
  color: #f8f6f0;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.poster-desc {
  font-size: 12px;
  color: #c4c0b1;
  margin-bottom: 16px;
}

.poster-time {
  background: rgba(212, 165, 116, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  border: 1px solid rgba(212, 165, 116, 0.3);
}

.time-item {
  font-size: 11px;
}

.time-label {
  color: #c4c0b1;
}

.time-value {
  color: #d4a574;
  font-weight: 500;
}

/* 二维码区域 */
.qr-section {
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 二维码备用显示 */
.qr-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 24px;
}

.qr-fallback-text {
  font-size: 10px;
  margin-top: 4px;
}

.qr-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.qr-code {
  width: 140px;
  height: 140px;
  background: white;
  border-radius: 8px;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-code img,
.qr-canvas,
.qr-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
  display: block;
  max-width: 100%;
  max-height: 100%;
}

.qr-text {
  text-align: center;
}

.qr-title {
  font-size: 14px;
  font-weight: 600;
  color: #f8f6f0;
  margin-bottom: 4px;
}

.qr-subtitle {
  font-size: 11px;
  color: #c4c0b1;
}

.refresh-qr-btn {
  margin-top: 8px;
  padding: 4px 8px;
  background: rgba(212, 165, 116, 0.2);
  border: 1px solid rgba(212, 165, 116, 0.3);
  color: #d4a574;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.refresh-qr-btn:hover {
  background: rgba(212, 165, 116, 0.3);
  border-color: #d4a574;
}

/* 邀请链接区域 */
.invite-link-section {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.invite-link-section.primary {
  background: rgba(244, 162, 97, 0.1);
  border-color: rgba(244, 162, 97, 0.3);
}

.invite-link-label {
  font-size: 11px;
  color: #c4c0b1;
  margin-bottom: 8px;
  text-align: center;
}

.invite-link-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.invite-link-text {
  flex: 1;
  font-size: 10px;
  color: #f8f6f0;
  word-break: break-all;
  line-height: 1.3;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  font-family: monospace;
}

.copy-link-btn {
  background: rgba(212, 165, 116, 0.2);
  border: 1px solid rgba(212, 165, 116, 0.3);
  color: #d4a574;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.copy-link-btn:hover {
  background: rgba(212, 165, 116, 0.3);
  border-color: #d4a574;
}

/* 邀请码区域 */
.invite-code-section {
  padding: 16px 20px;
  background: rgba(212, 165, 116, 0.1);
  border-top: 1px solid rgba(212, 165, 116, 0.2);
}

.invite-code {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 12px;
}

.invite-label {
  color: #c4c0b1;
}

.invite-value {
  color: #d4a574;
  font-weight: bold;
  font-family: monospace;
  letter-spacing: 1px;
}

.copy-btn {
  background: rgba(212, 165, 116, 0.2);
  border: 1px solid rgba(212, 165, 116, 0.3);
  color: #d4a574;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 10px;
  transition: all 0.3s ease;
}

.copy-btn:hover {
  background: rgba(212, 165, 116, 0.3);
  border-color: #d4a574;
}

/* 操作按钮 */
.poster-actions {
  position: sticky;
  bottom: 0;
  background: #1a1a1a;
  padding-top: 12px;
  display: flex;
  gap: 12px;
  z-index: 2;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.download-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
}

.share-btn {
  background: rgba(244, 162, 97, 0.1);
  color: #f4a261;
  border: 1px solid rgba(244, 162, 97, 0.3);
}

.share-btn:hover {
  background: rgba(244, 162, 97, 0.2);
  border-color: #f4a261;
  transform: translateY(-2px);
}

/* 响应式调整 */
@media (max-width: 480px) {
  .poster-content {
    margin: 10px;
    padding: 12px;
    max-width: none;
  }
  
  .poster-cover {
    height: 160px;
  }
  
  .poster-title {
    font-size: 16px;
  }
  
  .qr-container {
    flex-direction: column;
    gap: 12px;
  }
  
  .poster-actions {
    padding-top: 8px;
    gap: 8px;
  }
}
.image-preview-mask {
  position: fixed;
  z-index: 2000;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.85);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.preview-img {
  max-width: 90vw;
  max-height: 60vh;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.5);
  background: #fff;
}
.preview-tip {
  color: #fff;
  font-size: 15px;
  margin-top: 18px;
  text-align: center;
  line-height: 1.6;
  text-shadow: 0 2px 8px #000;
}
</style> 