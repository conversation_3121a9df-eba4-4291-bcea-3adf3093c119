<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>专区 - 凌云数资</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: var(--primary-gold-light);
        }
        
        .hero-section {
            margin: 20px 0;
            text-align: center;
        }
        
        .hero-title {
            font-size: 24px;
            font-weight: 700;
            color: var(--neutral-100);
            margin-bottom: 8px;
        }
        
        .hero-subtitle {
            font-size: 14px;
            color: var(--neutral-400);
            margin-bottom: 20px;
        }
        
        .zones-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 24px 0;
        }
        
        .zone-card {
            background: var(--gradient-card);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .zone-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-gold);
        }
        
        .zone-banner {
            height: 120px;
            background: linear-gradient(135deg, var(--primary-gold-dark), var(--primary-gold));
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .zone-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        }
        
        .zone-icon {
            font-size: 48px;
            color: white;
            position: relative;
            z-index: 1;
        }
        
        .zone-info {
            padding: 20px;
        }
        
        .zone-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 8px;
        }
        
        .zone-description {
            font-size: 14px;
            color: var(--neutral-400);
            line-height: 1.5;
            margin-bottom: 16px;
        }
        
        .zone-stats {
            display: flex;
            gap: 16px;
            margin-bottom: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: var(--primary-gold);
        }
        
        .stat-label {
            font-size: 12px;
            color: var(--neutral-400);
        }
        
        .zone-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }
        
        .feature-tag {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            border: 1px solid var(--primary-gold);
        }
        
        .zone-actions {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 24px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            flex: 1;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(200, 134, 13, 0.4);
        }
        
        .btn-secondary {
            background: var(--gradient-card);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-gold);
        }
        
        .special-zones {
            margin: 32px 0;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-200);
            margin-bottom: 16px;
            padding: 0 4px;
        }
        
        .special-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .special-card {
            background: var(--gradient-card);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .special-card:hover {
            transform: translateY(-2px);
            border-color: var(--secondary-purple);
            box-shadow: var(--shadow-purple);
        }
        
        .special-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            margin-bottom: 12px;
        }
        
        .special-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 4px;
        }
        
        .special-desc {
            font-size: 12px;
            color: var(--neutral-400);
            line-height: 1.4;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: var(--neutral-400);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--accent-red);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }
        
        /* 响应式调整 */
        @media (max-width: 480px) {
            .container {
                padding: 0 12px;
            }
            
            .hero-title {
                font-size: 20px;
            }
            
            .zone-info {
                padding: 16px;
            }
            
            .zone-banner {
                height: 100px;
            }
            
            .zone-icon {
                font-size: 40px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 style="font-size: 18px; font-weight: 600; color: var(--primary-gold);">专区</h1>
                <button class="back-btn" onclick="showSearch()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 标题区域 -->
            <section class="hero-section">
                <h2 class="hero-title">四川文化专区</h2>
                <p class="hero-subtitle">探索巴蜀文明，收藏数字珍品</p>
            </section>

            <!-- 主要专区 -->
            <section class="zones-grid">
                <!-- 三星堆专区 -->
                <div class="zone-card" onclick="enterZone('sanxingdui')">
                    <div class="zone-banner">
                        <div class="zone-icon">
                            <i class="fas fa-mask"></i>
                        </div>
                    </div>
                    <div class="zone-info">
                        <h3 class="zone-title">三星堆文明专区</h3>
                        <p class="zone-description">
                            神秘的古蜀文明，青铜器与黄金面具的奇幻世界。收集来自三星堆遗址的珍贵数字藏品。
                        </p>
                        <div class="zone-stats">
                            <div class="stat-item">
                                <div class="stat-value">89</div>
                                <div class="stat-label">藏品</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">2.3k</div>
                                <div class="stat-label">收藏者</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.8</div>
                                <div class="stat-label">评分</div>
                            </div>
                        </div>
                        <div class="zone-features">
                            <span class="feature-tag">考古发现</span>
                            <span class="feature-tag">青铜器</span>
                            <span class="feature-tag">3D模型</span>
                            <span class="feature-tag">限量版</span>
                        </div>
                        <div class="zone-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-store"></i> 进入专区
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-heart"></i> 关注
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 金沙遗址专区 -->
                <div class="zone-card" onclick="enterZone('jinsha')">
                                         <div class="zone-banner" style="background: linear-gradient(135deg, var(--secondary-purple-dark), var(--secondary-purple));">
                         <div class="zone-icon">
                             <i class="fas fa-sun"></i>
                         </div>
                     </div>
                    <div class="zone-info">
                        <h3 class="zone-title">金沙遗址专区</h3>
                        <p class="zone-description">
                            太阳神鸟的故乡，古蜀王国的政治经济中心。感受金沙文化的璀璨光芒。
                        </p>
                        <div class="zone-stats">
                            <div class="stat-item">
                                <div class="stat-value">156</div>
                                <div class="stat-label">藏品</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.1k</div>
                                <div class="stat-label">收藏者</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.9</div>
                                <div class="stat-label">评分</div>
                            </div>
                        </div>
                        <div class="zone-features">
                            <span class="feature-tag">太阳神鸟</span>
                            <span class="feature-tag">黄金制品</span>
                            <span class="feature-tag">象牙雕刻</span>
                            <span class="feature-tag">精品收藏</span>
                        </div>
                        <div class="zone-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-store"></i> 进入专区
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-heart"></i> 关注
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 蜀锦文化专区 -->
                <div class="zone-card" onclick="enterZone('shujin')">
                    <div class="zone-banner" style="background: linear-gradient(135deg, var(--accent-orange), var(--primary-gold-light));">
                        <div class="zone-icon">
                            <i class="fas fa-cut"></i>
                        </div>
                    </div>
                    <div class="zone-info">
                        <h3 class="zone-title">蜀锦文化专区</h3>
                        <p class="zone-description">
                            千年织造工艺，丝绸之路的起点。体验蜀锦的精湛技艺和文化内涵。
                        </p>
                        <div class="zone-stats">
                            <div class="stat-item">
                                <div class="stat-value">67</div>
                                <div class="stat-label">藏品</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">1.8k</div>
                                <div class="stat-label">收藏者</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">4.7</div>
                                <div class="stat-label">评分</div>
                            </div>
                        </div>
                        <div class="zone-features">
                            <span class="feature-tag">传统工艺</span>
                            <span class="feature-tag">纹样设计</span>
                            <span class="feature-tag">非遗文化</span>
                            <span class="feature-tag">匠心制作</span>
                        </div>
                        <div class="zone-actions">
                            <button class="btn btn-primary">
                                <i class="fas fa-store"></i> 进入专区
                            </button>
                            <button class="btn btn-secondary">
                                <i class="fas fa-heart"></i> 关注
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 特色专区 -->
            <section class="special-zones">
                <h3 class="section-title">特色专区</h3>
                <div class="special-grid">
                    <div class="special-card" onclick="enterZone('chuanju')">
                        <div class="special-icon">
                            <i class="fas fa-theater-masks"></i>
                        </div>
                        <div class="special-title">川剧变脸</div>
                        <div class="special-desc">传统戏曲艺术，变脸绝技数字化典藏</div>
                    </div>

                    <div class="special-card" onclick="enterZone('food')">
                        <div class="special-icon">
                            <i class="fas fa-pepper-hot"></i>
                        </div>
                        <div class="special-title">川菜文化</div>
                        <div class="special-desc">麻辣鲜香，川菜技艺与文化传承</div>
                    </div>

                    <div class="special-card" onclick="enterZone('panda')">
                        <div class="special-icon">
                            <i class="fas fa-paw"></i>
                        </div>
                        <div class="special-title">熊猫故乡</div>
                        <div class="special-desc">国宝大熊猫的可爱形象与保护故事</div>
                    </div>

                    <div class="special-card" onclick="enterZone('landscape')">
                        <div class="special-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <div class="special-title">巴山蜀水</div>
                        <div class="special-desc">四川名山大川的壮美景色</div>
                    </div>

                    <div class="special-card" onclick="enterZone('tea')">
                        <div class="special-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="special-title">茶马古道</div>
                        <div class="special-desc">古代贸易路线，茶文化的历史印记</div>
                    </div>

                    <div class="special-card" onclick="enterZone('literature')">
                        <div class="special-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="special-title">文学巴蜀</div>
                        <div class="special-desc">李白杜甫故里，文学名家作品典藏</div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item active">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 进入专区
        function enterZone(zoneType) {
            const zoneNames = {
                'sanxingdui': '三星堆文明专区',
                'jinsha': '金沙遗址专区', 
                'shujin': '蜀锦文化专区',
                'chuanju': '川剧变脸专区',
                'food': '川菜文化专区',
                'panda': '熊猫故乡专区',
                'landscape': '巴山蜀水专区',
                'tea': '茶马古道专区',
                'literature': '文学巴蜀专区'
            };
            
            alert(`进入${zoneNames[zoneType]}`);
            // 这里可以跳转到具体的专区页面
            // window.location.href = `zone-${zoneType}.html`;
        }

        // 关注专区
        document.querySelectorAll('.btn-secondary').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                
                if (this.innerHTML.includes('关注')) {
                    this.innerHTML = '<i class="fas fa-heart" style="color: var(--accent-red);"></i> 已关注';
                    this.style.color = 'var(--accent-red)';
                    alert('关注成功');
                } else {
                    this.innerHTML = '<i class="fas fa-heart"></i> 关注';
                    this.style.color = '';
                    alert('取消关注');
                }
            });
        });

        // 搜索功能
        function showSearch() {
            alert('打开专区搜索');
        }

        // 添加点击效果
        document.querySelectorAll('.zone-card, .special-card, .btn').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 专区卡片悬浮效果
        document.querySelectorAll('.zone-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 12px 24px rgba(200, 134, 13, 0.3)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });
    </script>
</body>
</html> 
