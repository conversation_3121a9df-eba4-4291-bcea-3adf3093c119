/**
 * 手机号码验证工具
 * 包含虚拟运营商号码段检测和屏蔽功能
 */

/**
 * 虚拟运营商号码段配置
 */
export const VIRTUAL_OPERATOR_PREFIXES = {
  // 电信合作虚拟运营商
  TELECOM: ['1700', '1701', '1702', '162'],

  // 移动合作虚拟运营商
  MOBILE: ['1703', '1705', '1706', '165'],

  // 联通合作虚拟运营商
  UNICOM: ['1704', '1707', '1708', '1709', '171', '167']
}

/**
 * 所有虚拟运营商号码段（扁平化数组）
 */
export const ALL_VIRTUAL_PREFIXES = [
  ...VIRTUAL_OPERATOR_PREFIXES.TELECOM,
  ...VIRTUAL_OPERATOR_PREFIXES.MOBILE,
  ...VIRTUAL_OPERATOR_PREFIXES.UNICOM
]

/**
 * 虚拟运营商企业示例
 */
export const VIRTUAL_OPERATOR_EXAMPLES = {
  TELECOM: ['迪信通', '京东通信'],
  MOBILE: ['中兴视通', '银盛通信'],
  UNICOM: ['蜗牛移动', '阿里通信']
}

/**
 * 检测手机号是否为虚拟运营商号码
 * @param phoneNumber 手机号码
 * @returns 检测结果对象
 */
export function detectVirtualOperator(phoneNumber: string): {
  isVirtual: boolean
  operator?: 'TELECOM' | 'MOBILE' | 'UNICOM'
  prefix?: string
  examples?: string[]
} {
  if (!phoneNumber || phoneNumber.length < 4) {
    return { isVirtual: false }
  }

  // 检查4位前缀
  const prefix4 = phoneNumber.substring(0, 4)
  for (const [operator, prefixes] of Object.entries(VIRTUAL_OPERATOR_PREFIXES)) {
    if (prefixes.includes(prefix4)) {
      return {
        isVirtual: true,
        operator: operator as 'TELECOM' | 'MOBILE' | 'UNICOM',
        prefix: prefix4,
        examples: VIRTUAL_OPERATOR_EXAMPLES[operator as keyof typeof VIRTUAL_OPERATOR_EXAMPLES]
      }
    }
  }

  // 检查3位前缀
  const prefix3 = phoneNumber.substring(0, 3)
  for (const [operator, prefixes] of Object.entries(VIRTUAL_OPERATOR_PREFIXES)) {
    if (prefixes.includes(prefix3)) {
      return {
        isVirtual: true,
        operator: operator as 'TELECOM' | 'MOBILE' | 'UNICOM',
        prefix: prefix3,
        examples: VIRTUAL_OPERATOR_EXAMPLES[operator as keyof typeof VIRTUAL_OPERATOR_EXAMPLES]
      }
    }
  }

  return { isVirtual: false }
}

/**
 * 验证手机号码格式和运营商限制
 * @param phoneNumber 手机号码
 * @param allowVirtual 是否允许虚拟运营商号码，默认false
 * @returns 验证结果
 */
export function validatePhoneNumber(phoneNumber: string, allowVirtual: boolean = false): {
  isValid: boolean
  error?: string
  virtualInfo?: ReturnType<typeof detectVirtualOperator>
} {
  // 基础格式验证
  if (!phoneNumber) {
    return {
      isValid: false,
      error: '请输入手机号码'
    }
  }

  if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
    return {
      isValid: false,
      error: '请输入正确的手机号码'
    }
  }

  // 虚拟运营商检测
  const virtualInfo = detectVirtualOperator(phoneNumber)

  if (virtualInfo.isVirtual && !allowVirtual) {
    const operatorName = getOperatorName(virtualInfo.operator!)
    const exampleText = virtualInfo.examples ? `（如：${virtualInfo.examples.join('、')}）` : ''

    return {
      isValid: false,
      // error: `暂不支持${operatorName}虚拟运营商号码${exampleText}`,
      error: `暂不支持虚拟运营商号码`,
      virtualInfo
    }
  }

  return {
    isValid: true,
    virtualInfo
  }
}

/**
 * 获取运营商中文名称
 * @param operator 运营商代码
 * @returns 中文名称
 */
export function getOperatorName(operator: 'TELECOM' | 'MOBILE' | 'UNICOM'): string {
  const names = {
    TELECOM: '电信',
    MOBILE: '移动',
    UNICOM: '联通'
  }
  return names[operator]
}

/**
 * 格式化手机号显示（中间4位用*替换）
 * @param phoneNumber 手机号码
 * @returns 格式化后的手机号
 */
export function formatPhoneDisplay(phoneNumber: string): string {
  if (!phoneNumber || phoneNumber.length !== 11) {
    return phoneNumber
  }
  return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

/**
 * 检查是否为测试手机号（开发环境使用）
 * @param phoneNumber 手机号码
 * @returns 是否为测试号码
 */
export function isTestPhoneNumber(phoneNumber: string): boolean {
  const testPrefixes = ['1888', '1999'] // 测试号码前缀
  return testPrefixes.some(prefix => phoneNumber.startsWith(prefix))
}

/**
 * 获取虚拟运营商详细信息（用于提示用户）
 * @param phoneNumber 手机号码
 * @returns 详细信息字符串
 */
export function getVirtualOperatorInfo(phoneNumber: string): string {
  const info = detectVirtualOperator(phoneNumber)

  if (!info.isVirtual) {
    return ''
  }

  const operatorName = getOperatorName(info.operator!)
  const exampleText = info.examples ? `，如${info.examples.join('、')}等企业使用` : ''

  // return `该号码属于${operatorName}合作虚拟运营商号码段（${info.prefix}）${exampleText}`
  return ""
}

export default {
  detectVirtualOperator,
  validatePhoneNumber,
  getOperatorName,
  formatPhoneDisplay,
  isTestPhoneNumber,
  getVirtualOperatorInfo,
  VIRTUAL_OPERATOR_PREFIXES,
  ALL_VIRTUAL_PREFIXES
}
