<template>
  <div class="activity-detail-container" :style="{ paddingTop: '40px' }">
    <AppPageHeader :title="'活动详情'" @back="$router.back()" />


    <main class="main-content">
      <div class="container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载活动详情中...</div>
        </div>

        <!-- 活动详情内容 -->
        <div v-else-if="activity" class="activity-detail">
          <!-- 活动封面 -->
          <section class="activity-hero" v-if="activity.activityCover">
            <div class="hero-image">
              <img :src="activity.activityCover" :alt="activity.activityName" />
            </div>
            <div class="hero-overlay">
              <div class="activity-status" :class="getActivityStatusByTime.class">
                {{ getActivityStatusByTime.text }}
              </div>
            </div>
          </section>

          <!-- 活动基本信息 -->
          <section class="activity-info">
            <h1 class="activity-title">{{ activity.activityName }}</h1>

            <!-- 时间信息卡片 -->
            <div class="time-card">
              <div class="time-item">
                <div class="time-icon">
                  <i class="fas fa-play-circle"></i>
                </div>
                <div class="time-content">
                  <div class="time-label">活动开始</div>
                  <div class="time-value">{{ ActivityAPI.formatDateTime(activity.startTime) }}</div>
                </div>
              </div>

              <div class="time-item">
                <div class="time-icon">
                  <i class="fas fa-stop-circle"></i>
                </div>
                <div class="time-content">
                  <div class="time-label">活动结束</div>
                  <div class="time-value">{{ ActivityAPI.formatDateTime(activity.endTime) }}</div>
                </div>
              </div>

              <div class="time-item" v-if="activity.drawTime">
                <div class="time-icon">
                  <i class="fas fa-gift"></i>
                </div>
                <div class="time-content">
                  <div class="time-label">开奖时间</div>
                  <div class="time-value">{{ ActivityAPI.formatDateTime(activity.drawTime) }}</div>
                </div>
              </div>
            </div>
          </section>
          <!-- 参与按钮 -->
          <section class="activity-actions">
            <!-- 只在活动时间内且已参与时显示生成邀新海报按钮 -->
            <button
              v-if="canShowPosterButton"
              class="participate-btn btn-poster"
              @click="generatePoster"
            >
              生成邀新海报
            </button>
            <!-- 只在活动时间内且未参与时显示立即参与按钮 -->
            <button
              v-else-if="canShowParticipateButton"
              class="participate-btn btn-primary"
              :disabled="joining"
              @click="participateActivity"
            >
              {{ joining ? '参与中...' : '立即参与' }}
            </button>
          </section>

          <!-- 邀新排行榜按钮 -->
          <section class="rank-actions" v-if="activity">
            <button
              class="rank-btn"
              @click="viewInviteRank"
            >
              <i class="fas fa-trophy"></i>
              查看邀新排行榜
            </button>
          </section>

          <!-- 查看中奖结果按钮 -->
          <section class="prize-result-actions" v-if="activity && getActivityStatusByTime.text === '已结束'">
            <button
              class="prize-result-btn"
              @click="viewPrizeResult"
            >
              <i class="fas fa-gift"></i>
              查看中奖结果
            </button>
          </section>

          <!-- 活动描述 -->
          <section class="activity-description">
            <h2 class="section-title">活动详情</h2>
            <div class="description-content">
              <!-- <div class="description-text">{{ activity.description }}</div> -->
              <!-- 如果description是图片URL，则显示图片 -->
              <div  class="description-image">
                <img :src="activity.description" alt="活动说明图片" />
              </div>
            </div>
          </section>

          <!-- 活动奖品 -->
          <!-- <section class="activity-prizes" v-if="activity.prizeList && activity.prizeList.length > 0">
            <h2 class="section-title">活动奖品</h2>
            <div class="prizes-grid">
              <div
                v-for="prize in activity.prizeList"
                :key="prize.id"
                class="prize-card"
              >
                <div class="prize-icon">
                  <i class="fas fa-trophy" v-if="prize.type === '数字藏品'"></i>
                  <i class="fas fa-gift" v-else></i>
                </div>
                <div class="prize-info">
                  <div class="prize-name">{{ prize.name }}</div>
                  <div class="prize-type">{{ prize.type }}</div>
                  <div class="prize-quantity">数量：{{ prize.quantity }}</div>
                </div>
              </div>
            </div>
          </section> -->



          <!-- 活动信息 -->
          <!-- <section class="activity-meta">
            <div class="meta-item" v-if="activity.createStaff">
              <span class="meta-label">创建人：</span>
              <span class="meta-value">{{ activity.createStaff }}</span>
            </div>
            <div class="meta-item" v-if="activity.createDate">
              <span class="meta-label">创建时间：</span>
              <span class="meta-value">{{ ActivityAPI.formatDateTime(activity.createDate) }}</span>
            </div>
          </section> -->
        </div>

        <!-- 错误状态 -->
        <div v-else class="error-state">
          <i class="fas fa-exclamation-triangle error-icon"></i>
          <div class="error-text">活动详情加载失败</div>
          <button class="retry-btn" @click="loadActivityDetail">重试</button>
        </div>
      </div>
    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />

    <!-- 海报生成器 -->
    <ActivityPosterGenerator
      v-if="activity"
      :visible="showPosterGenerator"
      :activity="activity"
      @close="showPosterGenerator = false"
    />

    <!-- 中奖结果弹窗 -->
    <PrizeResultModal
      v-if="activity"
      :visible="showPrizeResultModal"
      :activity-id="activity.activityId"
      @close="showPrizeResultModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import BottomNavigation from '@/components/BottomNavigation.vue'
import ActivityPosterGenerator from '@/components/ActivityPosterGenerator.vue'
import PrizeResultModal from '@/components/PrizeResultModal.vue'
import ActivityAPI, { type ActivityDetail } from '@/api/activity'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const router = useRouter()
const route = useRoute()
const { success, error, info } = useNotification()

// 响应式数据
const loading = ref(false)
const activity = ref<ActivityDetail | null>(null)
const joining = ref(false) // 参与活动加载状态
const showPosterGenerator = ref(false) // 是否显示海报生成器
const showPrizeResultModal = ref(false) // 是否显示中奖结果弹窗
let joinDebounceTimer: number | null = null // 防抖定时器
let statusUpdateTimer: number | null = null // 状态更新定时器

// 暴露ActivityAPI到模板
const { getStatusText, getStatusClass, formatDateTime } = ActivityAPI

// 计算属性：是否显示生成邀新海报按钮
const canShowPosterButton = computed(() => {
  return getActivityStatusByTime.value.text === '进行中' && activity.value?.isParticipated
})
// 计算属性：是否显示立即参与按钮
const canShowParticipateButton = computed(() => {
  return getActivityStatusByTime.value.text === '进行中' && !activity.value?.isParticipated
})

// 计算属性：是否在开奖时间之后
const isAfterDrawTime = computed(() => {
  if (!activity.value?.drawTime) return false
  const now = new Date()
  const drawTime = new Date(activity.value.drawTime)
  return now > drawTime
})

// 基于时间的活动状态判断
const getActivityStatusByTime = computed(() => {
  if (!activity.value) return { text: '加载中...', class: 'status-unknown' }

  const now = new Date()
  const startTime = new Date(activity.value.startTime)
  const endTime = new Date(activity.value.endTime)
  const drawTime = activity.value.drawTime ? new Date(activity.value.drawTime) : null

  // 活动开始时间之前：未开始
  if (now < startTime) {
    return { text: '未开始', class: 'status-upcoming' }
  }
  // 开始时间到结束时间：进行中
  else if (now >= startTime && now <= endTime) {
    return { text: '进行中', class: 'status-ongoing' }
  }
  // 结束时间到开奖时间：开奖中
  else if (drawTime && now > endTime && now <= drawTime) {
    return { text: '开奖中', class: 'status-drawing' }
  }
  // 开奖时间后：已结束
  else if (drawTime && now > drawTime) {
    return { text: '已结束', class: 'status-ended' }
  }
  // 没有开奖时间，活动结束后直接显示已结束
  else if (!drawTime && now > endTime) {
    return { text: '已结束', class: 'status-ended' }
  }

  // 默认状态
  return { text: '未知状态', class: 'status-unknown' }
})

// 方法
const goBack = () => {
  router.back()
}



/**
 * 获取参与按钮文本
 */
const getParticipateButtonText = (): string => {
  if (!activity.value) return '加载中...'

  // 如果正在参与中，显示参与状态
  if (joining.value) return '参与中...'

  // 如果已经参与了，显示生成海报按钮
  if (activity.value.isParticipated) return '生成邀新海报'

  // 基于时间状态返回相应文本
  const status = getActivityStatusByTime.value.text

  switch (status) {
    case '未开始':
      return '活动未开始'
    case '进行中':
      return '立即参与'
    case '开奖中':
      return '开奖中'
    case '已结束':
      return '活动已结束'
    default:
      return '未知状态'
  }
}

/**
 * 防抖函数 - 防止重复点击
 */
const debounce = (func: Function, delay: number = 800) => {
  return (...args: any[]) => {
    // 清除之前的定时器
    if (joinDebounceTimer) {
      clearTimeout(joinDebounceTimer)
    }

    // 设置新的定时器
    joinDebounceTimer = window.setTimeout(() => {
      func.apply(null, args)
    }, delay)
  }
}

/**
 * 参与活动的实际逻辑
 */
const doParticipateActivity = async () => {
  if (!activity.value) {
    return
  }

  // 如果已经参与，则生成海报
  if (activity.value.isParticipated) {
    generatePoster()
    return
  }

  try {
    joining.value = true
    // info(`正在参与活动：${activity.value.activityName}`)

    const response = await ActivityAPI.joinActivity(activity.value.activityId)

    if (response.code === 200) {
      success('参与活动成功！')
      // 更新本地状态，标记为已参与
      if (activity.value) {
        activity.value.isParticipated = true
      }
      // 也可以选择重新加载活动详情
      // await loadActivityDetail()
    } else {
      error(response.msg || '参与活动失败')
    }
  } catch (err: any) {
    console.error('参与活动失败:', err)
    error(err.message || '参与活动失败，请稍后重试')
  } finally {
    joining.value = false
  }
}

/**
 * 生成邀新海报
 */
const generatePoster = () => {
  if (!activity.value) return

  // info('正在生成邀新海报...')
  showPosterGenerator.value = true
}

/**
 * 查看邀新排行榜
 */
const viewInviteRank = () => {
  if (!activity.value) return

  router.push(`/activity/${activity.value.activityId}/invite-rank`)
}

/**
 * 查看中奖结果
 */
const viewPrizeResult = () => {
  if (!activity.value) return

  showPrizeResultModal.value = true
}

/**
 * 参与活动 - 带防抖处理
 */
const participateActivity = debounce(doParticipateActivity, 800)

/**
 * 加载活动详情
 */
const loadActivityDetail = async () => {
  const activityId = route.params.activityId as string
  if (!activityId) {
    error('活动ID不存在')
    router.back()
    return
  }

  try {
    loading.value = true
    const response = await ActivityAPI.getActivityDetail(Number(activityId))

    if (response.code === 200 && response.data) {
      activity.value = response.data
    } else {
      error(`加载活动详情失败: ${response.msg}`)
    }
  } catch (err: any) {
    console.error('加载活动详情失败:', err)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    if (err?.code === 401 || err?.status === 401) {
      // 401错误时不设置任何数据，保持空状态
      activity.value = null
      return
    }

    error('加载活动详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadActivityDetail()

  // 启动状态更新定时器，每秒更新一次状态
  statusUpdateTimer = window.setInterval(() => {
    // 强制更新计算属性
    // 这里不需要做任何事情，因为计算属性会自动重新计算
  }, 1000)
})

// 组件销毁时清理定时器
onUnmounted(() => {
  if (joinDebounceTimer) {
    clearTimeout(joinDebounceTimer)
    joinDebounceTimer = null
  }
  if (statusUpdateTimer) {
    clearInterval(statusUpdateTimer)
    statusUpdateTimer = null
  }
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);

  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;

  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;

  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);

  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);

  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --secondary-purple-alpha: var(--accent-alpha);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --accent-blue: var(--info-color);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.activity-detail-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* 顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 0;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-btn {
  background: none;
  border: none;
  color: var(--primary-gold);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: var(--primary-gold-alpha);
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: var(--neutral-100);
}

.text-primary {
  color: var(--primary-gold);
}

/* 主要内容 */
.main-content {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding-bottom: 80px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--neutral-400);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(212, 165, 116, 0.2);
  border-top: 3px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--neutral-400);
}

/* 活动详情 */
.activity-detail {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding-bottom: 20px;
}

/* 活动封面 */
.activity-hero {
  position: relative;
  width: 100%;
  height: 250px;
  margin: 20px 0;
  border-radius: 16px;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-overlay {
  position: absolute;
  top: 16px;
  right: 16px;
}

.activity-status {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.status-ongoing {
  background: rgba(59, 130, 246, 0.8);
  color: white;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.status-upcoming {
  background: rgba(212, 165, 116, 0.8);
  color: white;
  border: 1px solid rgba(212, 165, 116, 0.5);
}

.status-ended {
  background: rgba(148, 163, 184, 0.8);
  color: white;
  border: 1px solid rgba(148, 163, 184, 0.5);
}

.status-drawing {
  background: rgba(168, 85, 247, 0.8);
  color: white;
  border: 1px solid rgba(168, 85, 247, 0.5);
  animation: pulse-drawing 2s infinite;
}

@keyframes pulse-drawing {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(168, 85, 247, 0.7);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(168, 85, 247, 0);
  }
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.8);
  color: white;
  border: 1px solid rgba(239, 68, 68, 0.5);
}

.status-paused {
  background: rgba(245, 158, 11, 0.8);
  color: white;
  border: 1px solid rgba(245, 158, 11, 0.5);
}

/* 活动信息 */
.activity-info {
  margin-bottom: 24px;
}

.activity-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--neutral-100);
  margin-bottom: 20px;
  line-height: 1.3;
}

/* 时间卡片 */
.time-card {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-icon {
  width: 40px;
  height: 40px;
  background: var(--gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 16px;
}

.time-content {
  flex: 1;
}

.time-label {
  font-size: 12px;
  color: var(--neutral-400);
  margin-bottom: 4px;
}

.time-value {
  font-size: 14px;
  color: var(--neutral-100);
  font-weight: 500;
}

/* 区域标题 */
.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 16px;
  padding-left: 12px;
  border-left: 3px solid var(--primary-gold);
}

/* 活动描述 */
.activity-description {
  margin-bottom: 24px;
}

.description-content {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.description-text {
  font-size: 14px;
  color: var(--neutral-200);
  line-height: 1.6;
  margin-bottom: 16px;
}

.description-image {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.description-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

/* 活动奖品 */
.activity-prizes {
  margin-bottom: 24px;
}

.prizes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.prize-card {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.prize-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-gold);
  box-shadow: var(--shadow-md);
}

.prize-icon {
  width: 48px;
  height: 48px;
  background: var(--gradient-primary);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  margin-right: 16px;
}

.prize-info {
  flex: 1;
}

.prize-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 4px;
}

.prize-type {
  font-size: 12px;
  color: var(--primary-gold);
  margin-bottom: 4px;
}

.prize-quantity {
  font-size: 12px;
  color: var(--neutral-400);
}

/* 参与按钮 */
.activity-actions {
  margin-bottom: 24px;
}

.participate-btn {
  width: 100%;
  padding: 16px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-disabled {
  background: var(--bg-tertiary);
  color: var(--neutral-400);
  cursor: not-allowed;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-joining {
  background: var(--gradient-accent);
  color: white;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.btn-joining::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: loading-shine 1.5s infinite;
}

@keyframes loading-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

.btn-poster {
  background: var(--gradient-accent);
  color: white;
  cursor: pointer;
  position: relative;
}

.btn-poster:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(244, 162, 97, 0.4);
}

.btn-poster::before {
  content: '📄';
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
}

/* 邀新排行榜按钮 */
.rank-actions {
  margin-bottom: 24px;
}

.rank-btn {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--primary-color);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  background: transparent;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.rank-btn:hover {
  background: var(--primary-alpha);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.rank-btn i {
  font-size: 16px;
}

/* 中奖结果按钮 */
.prize-result-actions {
  margin-bottom: 24px;
}

.prize-result-btn {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--accent-color);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  background: var(--gradient-accent);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  box-shadow: var(--shadow-primary);
}

.prize-result-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.prize-result-btn i {
  font-size: 16px;
}

/* 活动元信息 */
.activity-meta {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.meta-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  color: var(--neutral-400);
  margin-right: 8px;
  min-width: 60px;
}

.meta-value {
  color: var(--neutral-200);
  flex: 1;
}

/* 错误状态 */
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--neutral-400);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--error-color);
}

.error-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.retry-btn {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .activity-title {
    font-size: 20px;
  }

  .prizes-grid {
    grid-template-columns: 1fr;
  }

  .time-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }

  .activity-hero {
    height: 205px;
  }

  .activity-title {
    font-size: 18px;
  }

  .time-card {
    padding: 16px;
  }

  .description-content,
  .activity-meta {
    padding: 16px;
  }
}</style>
