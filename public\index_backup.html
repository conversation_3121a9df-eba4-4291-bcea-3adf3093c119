<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>凌云数资</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #e0e0e0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #fff;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #fff;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #e0e0e0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #4dabf7;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #4dabf7;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 24px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            min-width: 120px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4dabf7, #1890ff);
            color: white;
            box-shadow: 0 3px 10px rgba(77, 171, 247, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(77, 171, 247, 0.6);
            background: linear-gradient(135deg, #69c0ff, #40a9ff);
        }
        
        .btn-secondary {
            background: rgba(40, 40, 40, 0.7);
            color: #e0e0e0;
            border: 1px solid rgba(77, 171, 247, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(60, 60, 60, 0.7);
            border-color: #4dabf7;
        }
        
        .btn-small {
            padding: 6px 16px;
            font-size: 13px;
            min-width: auto;
        }
        
        .tag {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .tag-accent {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .tag-success {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .product-card {
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
            border: 1px solid #333;
            max-width: 100%;
            margin: 0 auto;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(77, 171, 247, 0.4);
            border-color: rgba(77, 171, 247, 0.5);
        }
        
        .product-image {
            width: 100%;
            height: 160px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #1c1c1c, #2a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #4dabf7;
            font-size: 13px;
        }
        
        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #b22222;
            color: #f8f0e0;
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 16px;
            font-weight: 500;
            z-index: 2;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #fff;
            line-height: 1.4;
        }
        
        .price {
            font-size: 18px;
            font-weight: bold;
            color: #ff6b6b;
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        .sale-time {
            font-size: 11px;
            color: #d4af37;
            background: rgba(100, 80, 60, 0.3);
            padding: 3px 8px;
            border-radius: 10px;
            margin-left: auto;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(15, 15, 15, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #222;
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #daa520, #b8860b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2a1616;
            font-weight: bold;
        }
        
        .nav-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .nav-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .nav-btn:hover {
            color: #f8f0e0;
        }
        
        .banner-slider {
            height: 200px;
            background: linear-gradient(135deg, #2a1b0c, #1a1006);
            border-radius: 12px;
            margin: 16px 0;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .banner-content {
            position: absolute;
            inset: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(45deg, rgba(218, 165, 32, 0.1), rgba(178, 34, 34, 0.1));
        }
        
        .search-section {
            margin: 20px 0;
        }
        
        .search-container {
            display: flex;
            background: rgba(60, 40, 30, 0.7);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .search-dropdown {
            background: rgba(218, 165, 32, 0.2);
            border: none;
            color: #daa520;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #f0e0d0;
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: rgba(240, 224, 208, 0.5);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #daa520, #b8860b);
            border: none;
            color: #2a1616;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .section {
            margin: 24px 0;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-more {
            color: #daa520;
            font-size: 14px;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .section-more:hover {
            color: #f8f0e0;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-item:hover {
            color: #f8f0e0;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-sun"></i>
                    </div>
                    <span class="text-medium text-primary">四川数字资产</span>
                </div>
                <div class="nav-actions">
                    <button class="nav-btn">
                        <i class="fas fa-user-circle"></i>
                    </button>
                    <button class="nav-btn">
                        <i class="fas fa-bell"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 轮播Banner -->
            <section class="banner-slider">
                <div class="banner-content">
                    <div style="text-align: center;">
                        <h2 class="text-large text-primary">太阳神鸟限量发行</h2>
                        <p class="text-normal">古蜀文明数字藏品，限�?000�?/p>
                        <button class="btn btn-primary" style="margin-top: 12px;">立即查看</button>
                    </div>
                </div>
            </section>

            <!-- 检索框 -->
            <section class="search-section">
                <div class="search-container">
                    <select class="search-dropdown">
                        <option>名称</option>
                        <option>关键�?/option>
                        <option>发行�?/option>
                    </select>
                    <input type="text" class="search-input" placeholder="搜索数字资产...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 即将发售 -->
            <section class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-clock"></i>
                        即将发售
                    </h3>
                    <a href="exhibition.html" class="section-more">查看全部 �?/a>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-image">
                            <div>太阳神鸟</div>
                            <div class="product-tag">即将开�?/div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">金沙太阳神鸟</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">限量</span>
                                <span class="tag tag-success">1000�?/span>
                            </div>
                            <div class="price">
                                �?99.00
                                <span class="sale-time">12:00开�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>青铜面具</div>
                            <div class="product-tag">预售�?/div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">三星堆青铜面�?/div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-accent">热门</span>
                                <span class="tag tag-success">500�?/span>
                            </div>
                            <div class="price">
                                �?99.00
                                <span class="sale-time">明日开�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>金沙遗址</div>
                            <div class="product-tag">3D</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">金沙遗址全景</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">3D模型</span>
                                <span class="tag tag-success">200�?/span>
                            </div>
                            <div class="price">
                                �?99.00
                                <span class="sale-time">本周开�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>蜀锦纹�?/div>
                            <div class="product-tag">新品</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">蜀锦织品纹�?/div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">文化</span>
                                <span class="tag tag-success">800�?/span>
                            </div>
                            <div class="price">
                                �?9.00
                                <span class="sale-time">下周开�?/span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 热门推荐 -->
            <section class="section">
                <div class="section-header">
                    <h3 class="section-title">
                        <i class="fas fa-fire"></i>
                        热门推荐
                    </h3>
                    <a href="exhibition.html" class="section-more">查看更多 �?/a>
                </div>
                <div class="products-grid">
                    <div class="product-card">
                        <div class="product-image">
                            <div>古蜀玉器</div>
                            <div class="product-tag">热销</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">古蜀玉器收藏</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-accent">精品</span>
                                <span class="tag tag-success">已售67%</span>
                            </div>
                            <div class="price">
                                �?59.00
                                <span class="sale-time">热售�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>巴蜀印章</div>
                            <div class="product-tag">珍藏</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">巴蜀印章系列</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">历史</span>
                                <span class="tag tag-success">已售45%</span>
                            </div>
                            <div class="price">
                                �?29.00
                                <span class="sale-time">抢购�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>文物复刻</div>
                            <div class="product-tag">联名</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">博物院联名款</div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-accent">限定</span>
                                <span class="tag tag-success">已售30%</span>
                            </div>
                            <div class="price">
                                �?99.00
                                <span class="sale-time">联名�?/span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="product-card">
                        <div class="product-image">
                            <div>数字音乐</div>
                            <div class="product-tag">音频</div>
                        </div>
                        <div class="product-info">
                            <div class="product-name">古乐器演�?/div>
                            <div style="margin: 6px 0;">
                                <span class="tag tag-primary">音乐</span>
                                <span class="tag tag-success">已售80%</span>
                            </div>
                            <div class="price">
                                �?9.00
                                <span class="sale-time">热门</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item active">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 搜索功能
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchType = document.querySelector('.search-dropdown').value;
            const searchText = document.querySelector('.search-input').value;
            
            if (searchText.trim()) {
                alert(`�?{searchType}搜索: ${searchText}`);
                // 这里可以跳转到搜索结果页�?
            }
        });

        // 搜索框回车事�?
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // 产品卡片点击事件
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function() {
                // 这里可以跳转到详情页�?
                alert('跳转到资产详情页�?);
            });
        });

        // 导航按钮点击事件
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const icon = this.querySelector('i');
                if (icon.classList.contains('fa-user-circle')) {
                    window.location.href = 'login.html';
                } else if (icon.classList.contains('fa-bell')) {
                    window.location.href = 'notifications.html';
                }
            });
        });
    </script>
</body>
</html> 
