import request from './request'
import type { ApiResponse } from './types'

export interface CouponListItem {
  couponId: number
  couponName: string
  couponCode: string
  couponCover?: string
  description?: string
  statusCd: string
  startTime: string
  endTime: string
  isUsed: string | number
}

/**
 * 获取用户权益券数量
 */
export const getUserCouponCount = (): Promise<ApiResponse<number>> => {
  return request.get<ApiResponse<number>>('/coupon/coupon/client/getUserCouponCount')
}

/**
 * 获取用户权益券列表
 */
export const listUserCoupon = (params?: { pageNum?: number; pageSize?: number }) => {
  return request.get<ApiResponse<CouponListItem[]>>('/coupon/coupon/client/listUserCoupon', params)
} 