@import './base.css';
@import './styles/auth.css';

/* 强制滚动修复 */
* {
  box-sizing: border-box;
}

html {
  margin: 0 !important;
  padding: 0 !important;
  overflow-y: auto !important;
  height: auto !important;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-y: auto !important;
  height: auto !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  width: 100%;
  overflow-y: auto !important;
  height: auto !important;
}

/* 认证页面特殊处理 */
.auth-layout {
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh;
}

.auth-layout .auth-page {
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh;
}

/* 全局样式重置 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#app {
  width: 100%;
  min-height: 100vh;
}

/* 普通页面样式 */
.app-layout #app {
  max-width: none;
  margin: 0;
  padding: 0;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}
