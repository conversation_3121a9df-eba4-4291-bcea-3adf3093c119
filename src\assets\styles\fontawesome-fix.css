/* Font Awesome 全局修复样式 */

/* 确保Font Awesome CDN加载 */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css');

/* 强制Font Awesome样式应用 */
.fas, .fab, .far, .fal, .fat, .fad {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 6 Pro" !important;
  font-weight: 900 !important;
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
  line-height: 1 !important;
}

.fas::before, .far::before {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

.fab::before {
  font-family: "Font Awesome 6 Brands" !important;
  font-weight: 400 !important;
}

/* 确保所有图标都有正确的基础样式 */
.fa, [class^="fa-"], [class*=" fa-"] {
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
}

/* 覆盖任何可能的冲突样式 */
* .fas, * .fab, * .far, * .fal, * .fat, * .fad {
  font-family: "Font Awesome 6 Free", "Font Awesome 6 Brands", "Font Awesome 6 Pro" !important;
}

/* 特定图标的字体映射 */
.fa-mobile::before { content: "\f3ce" !important; }
.fa-shield::before { content: "\f132" !important; }
.fa-user::before { content: "\f007" !important; }
.fa-lock::before { content: "\f023" !important; }
.fa-eye::before { content: "\f06e" !important; }
.fa-eye-slash::before { content: "\f070" !important; }
.fa-spinner::before { content: "\f110" !important; }
.fa-sign-in::before { content: "\f090" !important; }
.fa-plus::before { content: "\f067" !important; }
.fa-sun::before { content: "\f185" !important; }
.fa-arrow-left::before { content: "\f060" !important; }
.fa-arrow-right::before { content: "\f061" !important; } 