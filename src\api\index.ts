/**
 * API服务统一导出
 */

// 导出API服务类
export { default as AuthAPI } from './auth'
export { default as UserAPI } from './user'
export { default as DictionaryAPI } from './dictionary'
export { default as BannerAPI } from './banner'
export { default as RecommendationAPI } from './recommendation'
export { default as PresaleAPI } from './presale'
export { default as SeriesAPI } from './series'
export { default as ZoneAPI } from './zone'
export { default as SearchAPI } from './search'
export { default as PayAPI } from './pay'
export { default as ActivityAPI } from './activity'

// 导出类型定义
export * from './types'
export type {
  UserStats,
  UserWallet,
  Collection,
  Order,
  Transaction
} from './user'
export type {
  RawDictionaryItem,
  DictionaryItem,
  RawDictionaryResponse,
  DictionaryResponse
} from './dictionary'
export type {
  BannerItem,
  BannerListResponse
} from './banner'
export type {
  RecommendationAsset,
  RecommendationListParams,
  RecommendationListResponse
} from './recommendation'
export type {
  PresaleAsset,
  PresaleListResponse
} from './presale'
export type {
  SeriesDetail,
  SeriesDetailResponse,
  SeriesAssetListParams,
  SeriesAssetListResponse
} from './series'
export type {
  ZoneDetail,
  ZoneAssetListParams,
  ZoneAssetListResponse
} from './zone'
export type {
  DigSearchVo,
  AssetSearchResult,
  SeriesSearchResult,
  ZoneSearchResult,
  SearchData,
  SearchResponse
} from './search'
export type {
  AddOrderRequest,
  AddOrderResponse,
  OrderDetailResponse
} from './pay'

// 导出请求工具
export { default as request } from './request' 