import request from './request'
import type { ApiResponse } from './types'

/**
 * 下单请求参数
 */
export interface AddOrderRequest {
  /** 购买数量 */
  buyQuantity: number
  /** 交易类型 */
  tradeType: string
  /** 支付渠道 */
  payChannel?: string
  /** 资产ID */
  assetId: string
}

/**
 * 下单响应数据
 */
export interface AddOrderResponse {
  /** 订单ID */
  orderId: string
  /** 支付URL（支付宝H5支付会返回） */
  payUrl?: string
  /** 支付表单HTML（部分支付方式返回） */
  payForm?: string
  /** 订单状态 */
  orderStatus?: string
  /** 订单金额 */
  orderAmount?: number
  /** 支付超时时间 */
  expireTime?: string
  /** 其他支付相关信息 */
  [key: string]: any
}

/**
 * 订单查询响应数据
 */
export interface OrderDetailResponse {
  /** 订单ID */
  orderId: string
  /** 资产ID */
  assetId: string
  /** 资产名称 */
  assetName: string
  /** 购买数量 */
  buyQuantity: number
  /** 订单金额 */
  orderAmount: number
  /** 订单状态 */
  orderStatus: string
  /** 支付状态 */
  payStatus: string
  /** 创建时间 */
  createTime: string
  /** 支付时间 */
  payTime?: string
  /** 支付渠道 */
  payChannel: string
  /** 交易类型 */
  tradeType: string
}

/**
 * 订单列表查询参数
 */
export interface OrderListQueryParams {
  /** 页码 */
  pageNum?: number
  /** 页面大小 */
  pageSize?: number
  /** 资产名称 */
  assetName?: string
  /** 订单状态 */
  statusCd?: string
}

/**
 * 订单列表项数据结构
 */
export interface OrderListItem {
  /** 订单ID */
  orderId: number
  /** 订单号(业务唯一) */
  orderNo: string
  /** 订单金额 */
  orderAmount: number
  /** 资产ID */
  assetId: number
  /** 资产名称 */
  assetName: string
  /** 资产封面 */
  assetCover: string
  /** 资产封面缩略图 */
  assetCoverThumbnail: string
  /** 退款订单号 */
  refundOrderNo?: string
  /** 状态代码 */
  statusCd: string
  /** 创建时间 */
  createDate: string
}

/**
 * 订单列表响应数据
 */
export interface OrderListResponse {
  /** 总条数 */
  total: number
  /** 数据列表 */
  rows: OrderListItem[]
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
}

/**
 * 支付API类
 */
export class PayAPI {
  /**
   * 创建订单
   * @param orderData 订单数据
   * @returns 订单创建结果
   */
  static async addOrder(orderData: AddOrderRequest): Promise<ApiResponse<AddOrderResponse>> {
    console.log('📡 发起下单请求:', orderData)
    
    const response = await request.post<ApiResponse<AddOrderResponse>>(
      '/pay/order/addOrder',
      orderData
    )
    
    console.log('📥 下单接口响应:', response)
    return response
  }

  /**
   * 查询订单详情
   * @param orderId 订单ID
   * @returns 订单详情
   */
  static async getOrderDetail(orderId: string): Promise<ApiResponse<OrderDetailResponse>> {
    console.log('📡 查询订单详情:', orderId)
    
    const response = await request.get<ApiResponse<OrderDetailResponse>>(
      `/pay/order/detail/${orderId}`
    )
    
    console.log('📥 订单详情响应:', response)
    return response
  }

  /**
   * 查询订单支付状态
   * @param orderId 订单ID
   * @returns 支付状态
   */
  static async getPaymentStatus(orderId: string): Promise<ApiResponse<{ payStatus: string; orderStatus: string }>> {
    console.log('📡 查询支付状态:', orderId)
    
    const response = await request.get<ApiResponse<{ payStatus: string; orderStatus: string }>>(
      `/pay/order/status/${orderId}`
    )
    
    console.log('📥 支付状态响应:', response)
    return response
  }

  /**
   * 取消订单
   * @param orderId 订单ID
   * @returns 取消结果
   */
  static async cancelOrder(orderId: string): Promise<ApiResponse<any>> {
    console.log('📡 取消订单:', orderId)
    
    const response = await request.post<ApiResponse<any>>(
      `/pay/order/cancel/${orderId}`
    )
    
    console.log('📥 取消订单响应:', response)
    return response
  }

  /**
   * 查询订单状态
   * @param orderNo 订单号
   * @returns 订单状态信息
   */
  static async getOrderStatus(orderNo: string): Promise<ApiResponse<{ status: string; [key: string]: any }>> {
    console.log('📡 查询订单状态:', orderNo)
    
    const response = await request.get<ApiResponse<{ status: string; [key: string]: any }>>(
      `/pay/order/orderStatus/${orderNo}`
    )
    
    console.log('📥 订单状态响应:', response)
    return response
  }

  /**
   * 发起支付
   * @param orderNo 订单号
   * @param payChannel 支付渠道
   * @returns 支付结果
   */
  static async pay(orderNo: string, payChannel: string): Promise<ApiResponse<any>> {
    console.log('📡 发起支付:', { orderNo, payChannel })
    
    // 构建查询参数
    const params = new URLSearchParams({
      orderNo,
      payChannel
    })
    
    const response = await request.post<ApiResponse<any>>(
      `/pay/order/pay?${params.toString()}`
    )
    
    console.log('📥 支付响应:', response)
    return response
  }

  /**
   * 获取客户订单列表
   * @param params 查询参数
   * @returns 订单列表
   */
  static async getOrderList(params: OrderListQueryParams = {}): Promise<OrderListResponse> {
    console.log('📡 查询订单列表:', params)
    
    const queryParams: Record<string, any> = {
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 20
    }
    
    if (params.assetName) {
      queryParams.assetName = params.assetName
    }
    if (params.statusCd) {
      queryParams.statusCd = params.statusCd
    }
    
    const response = await request.get<OrderListResponse>('/pay/order/client/list', queryParams)
    
    console.log('📥 订单列表响应:', response)
    return response
  }
}

// 导出默认类
export default PayAPI 