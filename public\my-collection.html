<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的收藏 - 四川省数字资产发行平�?/title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
            background: linear-gradient(135deg, #e6b422, #c99a10);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #b22222, #8b1a1a);
            color: #f8f0e0;
            box-shadow: 0 3px 10px rgba(178, 34, 34, 0.4);
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(178, 34, 34, 0.6);
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .tag {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .tag-accent {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .tag-success {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .tag-warning {
            background: rgba(205, 133, 63, 0.2);
            color: #cd853f;
            border: 1px solid rgba(205, 133, 63, 0.4);
        }
        
        .product-card {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
            border: 1px solid rgba(218, 165, 32, 0.3);
            max-width: 100%;
            margin: 0 auto;
            cursor: pointer;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(218, 165, 32, 0.4);
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #daa520;
            font-size: 13px;
        }
        
        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #b22222;
            color: #f8f0e0;
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 16px;
            font-weight: 500;
            z-index: 2;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .product-tag.zone-tag {
            background: #daa520;
            color: #2a1616;
        }
        
        .collection-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
            z-index: 3;
        }
        
        .action-btn {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s;
            background: rgba(0, 0, 0, 0.6);
            color: #f8f0e0;
            backdrop-filter: blur(5px);
        }
        
        .action-btn:hover {
            transform: scale(1.1);
        }
        
        .action-btn.favorited {
            background: rgba(178, 34, 34, 0.8);
            color: #f8f0e0;
        }
        
        .action-btn.share {
            background: rgba(218, 165, 32, 0.8);
            color: #2a1616;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 15px;
            font-weight: bold;
            margin-bottom: 6px;
            color: #f8f0e0;
            line-height: 1.4;
        }
        
        .collection-date {
            font-size: 11px;
            color: #d4af37;
            margin-bottom: 6px;
        }
        
        .price {
            font-size: 14px;
            font-weight: bold;
            color: #b22222;
            display: flex;
            align-items: center;
            margin-top: 4px;
        }
        
        .collection-status {
            font-size: 11px;
            color: #8fbc8f;
            background: rgba(143, 188, 143, 0.2);
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: auto;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: #f8f0e0;
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
        }
        
        .search-section {
            margin: 20px 0;
        }
        
        .search-container {
            display: flex;
            background: rgba(60, 40, 30, 0.7);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #f0e0d0;
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: rgba(240, 224, 208, 0.5);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #daa520, #b8860b);
            border: none;
            color: #2a1616;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .filters-section {
            margin: 16px 0;
        }
        
        .filters-scroll {
            overflow-x: auto;
            padding: 8px 0;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .filters-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .filters-list {
            display: flex;
            gap: 12px;
            padding: 0 4px;
            min-width: max-content;
        }
        
        .filter-tag {
            padding: 8px 16px;
            border-radius: 16px;
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .filter-tag.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            border-color: #daa520;
            box-shadow: 0 3px 8px rgba(218, 165, 32, 0.4);
        }
        
        .filter-tag:hover {
            border-color: #daa520;
            transform: translateY(-1px);
        }
        
        .collections-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0 16px;
        }
        
        .collections-count {
            font-size: 14px;
            color: #daa520;
            font-weight: 600;
        }
        
        .sort-btn {
            background: rgba(60, 40, 30, 0.7);
            border: 1px solid rgba(218, 165, 32, 0.3);
            color: #f0e0d0;
            padding: 6px 12px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .sort-btn:hover {
            border-color: #daa520;
        }
        
        .collections-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: rgba(218, 165, 32, 0.5);
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #f0e0d0;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 24px;
        }
        
        .load-more {
            text-align: center;
            margin: 24px 0;
        }
        
        .batch-actions {
            position: fixed;
            bottom: 80px;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 16px;
            transform: translateY(100%);
            transition: transform 0.3s;
            z-index: 40;
        }
        
        .batch-actions.show {
            transform: translateY(0);
        }
        
        .batch-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .batch-info {
            flex: 1;
            font-size: 14px;
            color: #daa520;
        }
        
        .batch-buttons {
            display: flex;
            gap: 8px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-item:hover {
            color: #f8f0e0;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .checkbox-input {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .product-card.selected {
            border-color: #daa520;
            box-shadow: 0 0 0 2px rgba(218, 165, 32, 0.3);
        }
        
        .collection-checkbox {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid #f8f0e0;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 3;
        }
        
        .collection-checkbox.show {
            display: flex;
        }
        
        .collection-checkbox.checked {
            background: #daa520;
            border-color: #daa520;
        }
        
        .collection-checkbox i {
            font-size: 10px;
            color: #2a1616;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">我的收藏</h1>
                <div class="header-actions">
                    <button class="btn btn-small btn-secondary" onclick="toggleSelectMode()" id="select-btn">选择</button>
                    <button class="btn btn-small btn-secondary" onclick="showSortMenu()">
                        <i class="fas fa-sort"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 搜索�?-->
            <section class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索我的收藏..." id="search-input">
                    <button class="search-btn" onclick="searchCollections()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 筛选标�?-->
            <section class="filters-section">
                <div class="filters-scroll">
                    <div class="filters-list">
                        <button class="filter-tag active" data-filter="all">全部</button>
                        <button class="filter-tag" data-filter="ancient-shu">古蜀文明</button>
                        <button class="filter-tag" data-filter="sanxingdui">三星�?/button>
                        <button class="filter-tag" data-filter="jinsha">金沙遗址</button>
                        <button class="filter-tag" data-filter="shu-brocade">蜀锦文�?/button>
                        <button class="filter-tag" data-filter="recent">最近收�?/button>
                    </div>
                </div>
            </section>

            <!-- 收藏列表头部 -->
            <section class="collections-header">
                <div class="collections-count" id="collections-count">�?2件收�?/div>
                <button class="sort-btn" onclick="toggleSort()">
                    <span id="sort-text">收藏时间</span>
                    <i class="fas fa-chevron-down" id="sort-icon"></i>
                </button>
            </section>

            <!-- 收藏网格 -->
            <section class="collections-grid" id="collections-grid">
                <!-- 收藏�?-->
                <div class="product-card" data-category="ancient-shu" data-collection-date="2024-01-15">
                    <div class="collection-checkbox" onclick="toggleSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="product-image">
                        <div>太阳神鸟</div>
                        <div class="product-tag zone-tag">古蜀文明</div>
                    </div>
                    <div class="collection-actions">
                        <button class="action-btn favorited" onclick="toggleFavorite(this)" title="取消收藏">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn share" onclick="shareItem(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="collection-date">收藏�?2024-01-15</div>
                        <div class="product-name">金沙太阳神鸟金饰</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">限量</span>
                            <span class="tag tag-accent">热门</span>
                        </div>
                        <div class="price">
                            �?99.00
                            <span class="collection-status">已收�?/span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="sanxingdui" data-collection-date="2024-01-12">
                    <div class="collection-checkbox" onclick="toggleSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="product-image">
                        <div>青铜面具</div>
                        <div class="product-tag zone-tag">三星�?/div>
                    </div>
                    <div class="collection-actions">
                        <button class="action-btn favorited" onclick="toggleFavorite(this)" title="取消收藏">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn share" onclick="shareItem(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="collection-date">收藏�?2024-01-12</div>
                        <div class="product-name">三星堆青铜面�?/div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">考古发现</span>
                            <span class="tag tag-success">500�?/span>
                        </div>
                        <div class="price">
                            �?99.00
                            <span class="collection-status">已收�?/span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="jinsha" data-collection-date="2024-01-10">
                    <div class="collection-checkbox" onclick="toggleSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="product-image">
                        <div>金沙遗址</div>
                        <div class="product-tag zone-tag">金沙遗址</div>
                    </div>
                    <div class="collection-actions">
                        <button class="action-btn favorited" onclick="toggleFavorite(this)" title="取消收藏">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn share" onclick="shareItem(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="collection-date">收藏�?2024-01-10</div>
                        <div class="product-name">金沙遗址3D全景</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">3D模型</span>
                            <span class="tag tag-warning">珍藏�?/span>
                        </div>
                        <div class="price">
                            �?99.00
                            <span class="collection-status">已收�?/span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="shu-brocade" data-collection-date="2024-01-08">
                    <div class="collection-checkbox" onclick="toggleSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="product-image">
                        <div>蜀锦纹�?/div>
                        <div class="product-tag zone-tag">蜀锦文�?/div>
                    </div>
                    <div class="collection-actions">
                        <button class="action-btn favorited" onclick="toggleFavorite(this)" title="取消收藏">
                            <i class="fas fa-heart"></i>
                        </button>
                        <button class="action-btn share" onclick="shareItem(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="collection-date">收藏�?2024-01-08</div>
                        <div class="product-name">蜀锦织品纹样集</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">传统工艺</span>
                            <span class="tag tag-success">800�?/span>
                        </div>
                        <div class="price">
                            �?9.00
                            <span class="collection-status">已收�?/span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 空状�?-->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="empty-title">暂无收藏</div>
                <div class="empty-description">
                    您还没有收藏任何数字藏品<br>
                    快去发现心仪的巴蜀文化珍品�?
                </div>
                <button class="btn btn-primary" onclick="goToExplore()">去探�?/button>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" id="load-more">
                <button class="btn btn-secondary">
                    <i class="fas fa-plus"></i> 加载更多
                </button>
            </div>
        </div>
    </main>

    <!-- 批量操作�?-->
    <div class="batch-actions" id="batch-actions">
        <div class="container">
            <div class="batch-content">
                <div class="batch-info">
                    已选择 <span id="selected-count">0</span> �?
                </div>
                <div class="batch-buttons">
                    <button class="btn btn-small btn-secondary" onclick="cancelSelection()">取消</button>
                    <button class="btn btn-small btn-danger" onclick="batchRemove()">移除收藏</button>
                    <button class="btn btn-small btn-primary" onclick="batchShare()">批量分享</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        let isSelectMode = false;
        let selectedItems = new Set();
        let currentSort = 'collection-date';
        let sortOrder = 'desc';

        // 搜索功能
        function searchCollections() {
            const searchText = document.getElementById('search-input').value.trim();
            const cards = document.querySelectorAll('.product-card');
            
            cards.forEach(card => {
                const productName = card.querySelector('.product-name').textContent.toLowerCase();
                const isMatch = productName.includes(searchText.toLowerCase());
                card.style.display = isMatch ? 'block' : 'none';
            });
            
            updateCollectionsCount();
        }

        // 筛选功�?
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                filterCollections(filter);
            });
        });

        function filterCollections(filter) {
            const cards = document.querySelectorAll('.product-card');
            let visibleCount = 0;
            
            cards.forEach(card => {
                const category = card.getAttribute('data-category');
                const collectionDate = card.getAttribute('data-collection-date');
                
                let shouldShow = false;
                
                if (filter === 'all') {
                    shouldShow = true;
                } else if (filter === 'recent') {
                    const date = new Date(collectionDate);
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    shouldShow = date >= weekAgo;
                } else {
                    shouldShow = category === filter;
                }
                
                if (shouldShow) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 显示空状�?
            const emptyState = document.getElementById('empty-state');
            const grid = document.getElementById('collections-grid');
            const loadMore = document.getElementById('load-more');
            
            if (visibleCount === 0) {
                grid.style.display = 'none';
                loadMore.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                grid.style.display = 'grid';
                loadMore.style.display = 'block';
                emptyState.style.display = 'none';
            }
            
            updateCollectionsCount();
        }

        // 更新收藏数量
        function updateCollectionsCount() {
            const visibleCards = document.querySelectorAll('.product-card[style*="block"], .product-card:not([style])');
            document.getElementById('collections-count').textContent = `�?{visibleCards.length}件收藏`;
        }

        // 排序功能
        function toggleSort() {
            const sortOptions = ['collection-date', 'price', 'name'];
            const currentIndex = sortOptions.indexOf(currentSort);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            
            currentSort = sortOptions[nextIndex];
            
            let sortText = '';
            switch(currentSort) {
                case 'collection-date':
                    sortText = '收藏时间';
                    break;
                case 'price':
                    sortText = '价格';
                    break;
                case 'name':
                    sortText = '名称';
                    break;
            }
            
            document.getElementById('sort-text').textContent = sortText;
            sortCollections();
        }

        function sortCollections() {
            const grid = document.getElementById('collections-grid');
            const cards = Array.from(grid.querySelectorAll('.product-card'));
            
            cards.sort((a, b) => {
                let aValue, bValue;
                
                switch(currentSort) {
                    case 'collection-date':
                        aValue = new Date(a.getAttribute('data-collection-date'));
                        bValue = new Date(b.getAttribute('data-collection-date'));
                        break;
                    case 'price':
                        aValue = parseFloat(a.querySelector('.price').textContent.replace(/[^\d.]/g, ''));
                        bValue = parseFloat(b.querySelector('.price').textContent.replace(/[^\d.]/g, ''));
                        break;
                    case 'name':
                        aValue = a.querySelector('.product-name').textContent;
                        bValue = b.querySelector('.product-name').textContent;
                        break;
                }
                
                if (sortOrder === 'desc') {
                    return aValue > bValue ? -1 : 1;
                } else {
                    return aValue < bValue ? -1 : 1;
                }
            });
            
            cards.forEach(card => grid.appendChild(card));
        }

        // 选择模式
        function toggleSelectMode() {
            isSelectMode = !isSelectMode;
            const selectBtn = document.getElementById('select-btn');
            const checkboxes = document.querySelectorAll('.collection-checkbox');
            
            if (isSelectMode) {
                selectBtn.textContent = '取消';
                selectBtn.classList.remove('btn-secondary');
                selectBtn.classList.add('btn-primary');
                checkboxes.forEach(cb => cb.classList.add('show'));
            } else {
                selectBtn.textContent = '选择';
                selectBtn.classList.remove('btn-primary');
                selectBtn.classList.add('btn-secondary');
                checkboxes.forEach(cb => {
                    cb.classList.remove('show', 'checked');
                });
                selectedItems.clear();
                document.querySelectorAll('.product-card').forEach(card => {
                    card.classList.remove('selected');
                });
                hideBatchActions();
            }
        }

        // 切换选择状�?
        function toggleSelection(checkbox) {
            if (!isSelectMode) return;
            
            const card = checkbox.closest('.product-card');
            const cardId = Array.from(card.parentNode.children).indexOf(card);
            
            if (selectedItems.has(cardId)) {
                selectedItems.delete(cardId);
                checkbox.classList.remove('checked');
                card.classList.remove('selected');
            } else {
                selectedItems.add(cardId);
                checkbox.classList.add('checked');
                card.classList.add('selected');
            }
            
            updateBatchActions();
        }

        // 更新批量操作
        function updateBatchActions() {
            const batchActions = document.getElementById('batch-actions');
            const selectedCount = document.getElementById('selected-count');
            
            selectedCount.textContent = selectedItems.size;
            
            if (selectedItems.size > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }
        }

        function hideBatchActions() {
            document.getElementById('batch-actions').classList.remove('show');
        }

        // 取消选择
        function cancelSelection() {
            toggleSelectMode();
        }

        // 批量移除
        function batchRemove() {
            if (selectedItems.size === 0) return;
            
            if (confirm(`确定要移除选中�?{selectedItems.size}件收藏吗？`)) {
                const cards = document.querySelectorAll('.product-card');
                selectedItems.forEach(cardIndex => {
                    cards[cardIndex].remove();
                });
                
                selectedItems.clear();
                hideBatchActions();
                updateCollectionsCount();
                alert('已移除选中的收�?);
            }
        }

        // 批量分享
        function batchShare() {
            if (selectedItems.size === 0) return;
            
            alert(`已分享选中�?{selectedItems.size}件收藏`);
        }

        // 单项操作
        function toggleFavorite(btn) {
            const card = btn.closest('.product-card');
            if (confirm('确定要取消收藏这件藏品吗�?)) {
                card.remove();
                updateCollectionsCount();
                alert('已取消收�?);
            }
        }

        function shareItem(btn) {
            const card = btn.closest('.product-card');
            const productName = card.querySelector('.product-name').textContent;
            alert(`分享"${productName}"`);
        }

        // 产品卡片点击
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是操作按钮或复选框，不跳转
                if (e.target.closest('.collection-actions') || e.target.closest('.collection-checkbox')) {
                    e.stopPropagation();
                    return;
                }
                
                // 在选择模式下，点击卡片等同于点击复选框
                if (isSelectMode) {
                    const checkbox = this.querySelector('.collection-checkbox');
                    toggleSelection(checkbox);
                } else {
                    const productName = this.querySelector('.product-name').textContent;
                    alert(`跳转�?${productName}"详情页面`);
                }
            });
        });

        // 去探�?
        function goToExplore() {
            window.location.href = 'index.html';
        }

        // 加载更多
        document.querySelector('.load-more button').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载�?..';
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-plus"></i> 加载更多';
                alert('已加载更多收�?);
            }, 1500);
        });

        // 搜索框回车事�?
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCollections();
            }
        });

        // 初始�?
        updateCollectionsCount();

        // 添加点击效果
        document.querySelectorAll('.btn, .filter-tag, .action-btn').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
