<template>
  <AppPageHeader :title="'邀请注册'" @back="$router.back()" />

  <div class="auth-page" :class="{ 'keyboard-visible': isKeyboardVisible }">
    <div class="auth-container">
      <!-- 页面头部 -->
      <div class="auth-header">
        <!-- <button class="back-btn" @click="goBack"> -->
          <!-- ←
        </button> -->
        <!-- <h1 style="font-size: 18px; font-weight: 600; color: var(--neutral-100);">邀请注册</h1> -->
      </div>
      
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="welcome-text">凌云数资</div>
        <div class="subtitle">
          <!-- <span v-if="inviteCode">通过邀请码注册，享受专属优惠</span>
          <span v-else>注册您的数字资产账户</span> -->
        </div>
        
        <!-- 邀请码显示 -->
        <div v-if="inviteCode" class="invite-code-display">
          <div class="invite-label">邀请码</div>
          <div class="invite-code-value">{{ inviteCode }}</div>
        </div>
      </div>
      
      <!-- 注册表单 -->
      <div class="auth-form">
        <!-- <div class="form-group">
          <label class="form-label">
            <i class="fas fa-user"></i>
            用户名
          </label>
          <div class="input-wrapper">
            <input 
              type="text" 
              class="form-input" 
              :class="{ error: errors.userName }"
              placeholder="请输入用户名"
              v-model="registerForm.userName"
              @blur="validateUserName"
              required
            >
            <div class="error-message" :class="{ show: errors.userName }">
              {{ errors.userName }}
            </div>
          </div>
        </div> -->
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-mobile"></i>
            手机号码
          </label>
          <div class="input-wrapper">
            <input 
              type="tel" 
              class="form-input" 
              :class="{ error: errors.phone }"
              placeholder="请输入手机号码"
              maxlength="11"
              v-model="registerForm.phone"
              @input="formatPhoneNumber"
              @blur="validatePhoneNumber"
              required
            >
            <div class="error-message" :class="{ show: errors.phone }">
              {{ errors.phone }}
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-shield"></i>
            短信验证码
          </label>
          <div class="input-wrapper">
            <div class="sms-input-group">
              <input 
                type="text" 
                class="form-input sms-input" 
                :class="{ error: errors.smsCode }"
                placeholder="请输入验证码"
                maxlength="6"
                v-model="registerForm.smsCode"
                @blur="validateSmsCode"
                required
              >
              <button 
                type="button" 
                class="sms-btn"
                :class="{ disabled: isSmsDisabled || !isPhoneValid }"
                :disabled="isSmsDisabled || !isPhoneValid"
                @click="sendSmsCode"
              >
                {{ smsButtonText }}
              </button>
            </div>
            <div class="error-message" :class="{ show: errors.smsCode }">
              {{ errors.smsCode }}
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-lock"></i>
            密码
          </label>
          <div class="input-wrapper">
            <div class="password-input-group">
              <input 
                :type="showPassword ? 'text' : 'password'" 
                class="form-input password-input" 
                :class="{ error: errors.password }"
                placeholder="请输入密码"
                v-model="registerForm.password"
                @blur="validatePassword"
                required
              >
              <button 
                type="button" 
                class="password-toggle"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" :class="{ show: errors.password }">
              {{ errors.password }}
            </div>
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-lock"></i>
            确认密码
          </label>
          <div class="input-wrapper">
            <div class="password-input-group">
              <input 
                :type="showConfirmPassword ? 'text' : 'password'" 
                class="form-input password-input" 
                :class="{ error: errors.confirmPassword }"
                placeholder="请再次输入密码"
                v-model="registerForm.confirmPassword"
                @blur="validateConfirmPassword"
                required
              >
              <button 
                type="button" 
                class="password-toggle"
                @click="toggleConfirmPassword"
              >
                <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
            <div class="error-message" :class="{ show: errors.confirmPassword }">
              {{ errors.confirmPassword }}
            </div>
          </div>
        </div>
        
        <!-- 服务条款 -->
        <div class="form-group checkbox-group">
          <div class="checkbox-wrapper">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                class="checkbox-input"
                v-model="agreeToTerms"
                required
              >
              <span class="checkbox-custom"></span>
              <span class="checkbox-text">
                我同意
                <span class="link" style="cursor:pointer" @click="showUserAgreement">《用户协议》</span>
                和
                <span class="link" style="cursor:pointer" @click="showPrivacyPolicy">《隐私政策》</span>
              </span>
            </label>
          </div>
        </div>
        
        <!-- 注册按钮 -->
        <button 
          type="button" 
          class="submit-btn"
          :class="{ 
            disabled: !isFormValid || isSubmitting,
            loading: isSubmitting 
          }"
          :disabled="!isFormValid || isSubmitting"
          @click="handleRegister"
        >
          <span v-if="isSubmitting">
            <i class="fas fa-spinner fa-spin"></i>
            注册中...
          </span>
          <span v-else>立即注册</span>
        </button>
        
        <!-- 登录链接 -->
        <!-- <div class="auth-footer">
          <span class="footer-text">已有账户？</span>
          <router-link to="/login" class="link">立即登录</router-link>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import { useKeyboardAdaptation } from '@/composables/useKeyboardAdaptation'
import ActivityAPI from '@/api/activity'
import AuthAPI from '@/api/auth'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const router = useRouter()
const route = useRoute()
const { success, error, warning } = useNotification()

// 键盘适配
const { isKeyboardVisible } = useKeyboardAdaptation()

// 获取邀请码
const inviteCode = ref<string>('')

// 表单数据
const registerForm = reactive({
  userName: '',
  phone: '',
  smsCode: '',
  password: '',
  confirmPassword: '',
  inviteCode: ''
})

// 表单验证错误
  const errors = reactive({
    // userName: '',
  phone: '',
  smsCode: '',
  password: '',
  confirmPassword: ''
})

// 表单状态
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const agreeToTerms = ref(false)
const isSubmitting = ref(false)

// 短信验证码相关
const smsCountdown = ref(0)
const smsTimer = ref<number | null>(null)

// 计算属性
const isPhoneValid = computed(() => {
  return /^1[3-9]\d{9}$/.test(registerForm.phone)
})

const isSmsDisabled = computed(() => {
  return smsCountdown.value > 0
})

const smsButtonText = computed(() => {
  return smsCountdown.value > 0 ? `${smsCountdown.value}s` : '发送验证码'
})

const isFormValid = computed(() => {
  return isPhoneValid.value &&
         registerForm.smsCode.length === 6 &&
         registerForm.password.length >= 6 &&
         registerForm.password === registerForm.confirmPassword &&
         agreeToTerms.value &&
         !Object.values(errors).some(error => error)
})

// 方法
const goBack = () => {
  router.back()
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const formatPhoneNumber = () => {
  // 只保留数字
  registerForm.phone = registerForm.phone.replace(/\D/g, '')
  if (registerForm.phone.length > 11) {
    registerForm.phone = registerForm.phone.slice(0, 11)
  }
}

// 表单验证方法
// const validateUserName = () => {
//   if (registerForm.userName.length < 2) {
//     errors.userName = '用户名至少2个字符'
//   } else if (registerForm.userName.length > 20) {
//     errors.userName = '用户名不能超过20个字符'
//   } else {
//     errors.userName = ''
//   }
// }

const validatePhoneNumber = () => {
  if (!registerForm.phone) {
    errors.phone = '请输入手机号码'
  } else if (!isPhoneValid.value) {
    errors.phone = '请输入正确的手机号码'
  } else {
    errors.phone = ''
  }
}

const validateSmsCode = () => {
  if (!registerForm.smsCode) {
    errors.smsCode = '请输入验证码'
  } else if (registerForm.smsCode.length !== 6) {
    errors.smsCode = '验证码为6位数字'
  } else {
    errors.smsCode = ''
  }
}

const validatePassword = () => {
  if (!registerForm.password) {
    errors.password = '请输入密码'
  } else if (registerForm.password.length < 6) {
    errors.password = '密码至少6位字符'
  } else if (registerForm.password.length > 20) {
    errors.password = '密码不能超过20位字符'
  } else {
    errors.password = ''
  }
}

const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
  } else if (registerForm.confirmPassword !== registerForm.password) {
    errors.confirmPassword = '两次输入密码不一致'
  } else {
    errors.confirmPassword = ''
  }
}

// 发送短信验证码
const sendSmsCode = async () => {
  if (!isPhoneValid.value) {
    warning('请输入正确的手机号码')
    return
  }

  try {
    const response = await AuthAPI.sendSmsCode({
      phone: registerForm.phone,
      scene: 'H5register'
    })

    if (response.code === 200) {
      success('验证码已发送')
      startCountdown()
    } else {
      error(response.msg || '发送失败')
    }
  } catch (err) {
    error('发送验证码失败，请稍后重试')
    console.error('Send SMS error:', err)
  }
}

// 开始倒计时
const startCountdown = () => {
  smsCountdown.value = 60
  smsTimer.value = window.setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer.value!)
      smsTimer.value = null
    }
  }, 1000)
}

// 处理注册
const handleRegister = async () => {
  // 验证所有字段
  // validateUserName()
  validatePhoneNumber()
  validateSmsCode()
  validatePassword()
  validateConfirmPassword()

  if (!isFormValid.value) {
    warning('请检查表单信息')
    return
  }

  try {
    isSubmitting.value = true

    const response = await ActivityAPI.inviteRegister({
      username: registerForm.phone,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword,
      phone: registerForm.phone,
      smsCode: registerForm.smsCode,
      inviteCode: registerForm.inviteCode
    })

    if (response.code === 200) {
      success('注册成功！请登录您的账户')
      
      // 跳转到登录页面
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    } else {
      error(response.msg || '注册失败')
    }
  } catch (err: any) {
    console.error('Register error:', err)
    error(err.message || '注册失败，请稍后重试')
  } finally {
    isSubmitting.value = false
  }
}

const saveFormToSession = () => {
  sessionStorage.setItem('inviteRegisterForm', JSON.stringify(registerForm))
}

const showUserAgreement = () => {
  saveFormToSession()
  router.push('/user-agreement')
}

const showPrivacyPolicy = () => {
  saveFormToSession()
  router.push('/privacy-policy')
}

// 生命周期
onMounted(() => {
  // 获取URL参数中的邀请码
  const urlInviteCode = route.query.inviteCode as string
  if (urlInviteCode) {
    inviteCode.value = urlInviteCode
    registerForm.inviteCode = urlInviteCode
  }
})

onUnmounted(() => {
  if (smsTimer.value) {
    clearInterval(smsTimer.value)
  }
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --neutral-100: #f8f6f0;
  --neutral-200: #e0ddd4;
  --neutral-300: #c4c0b1;
  --neutral-400: #a39d8e;
}

/* 使用现有的认证页面样式 */
@import '@/assets/styles/auth.css';

/* 邀请码显示区域 */
.invite-code-display {
  margin-top: 16px;
  padding: 12px 16px;
  background: rgba(212, 165, 116, 0.1);
  border: 1px solid rgba(212, 165, 116, 0.3);
  border-radius: 8px;
  text-align: center;
}

.invite-label {
  font-size: 12px;
  color: var(--neutral-400);
  margin-bottom: 4px;
}

.invite-code-value {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-gold);
  font-family: monospace;
  letter-spacing: 2px;
}

/* 表单组件样式 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--neutral-200);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
}

.form-label i {
  color: var(--primary-gold);
  width: 16px;
}

.input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--neutral-100);
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
}

.form-input.error {
  border-color: var(--error-color);
}

.form-input::placeholder {
  color: var(--neutral-400);
}

/* 短信验证码输入组 */
.sms-input-group {
  display: flex;
  gap: 8px;
}

.sms-input {
  flex: 1;
}

.sms-btn {
  padding: 12px 16px;
  border: 1px solid var(--primary-gold);
  border-radius: 8px;
  background: transparent;
  color: var(--primary-gold);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 100px;
}

.sms-btn:hover:not(.disabled) {
  background: var(--primary-gold);
  color: white;
}

.sms-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 密码输入组 */
.password-input-group {
  position: relative;
}

.password-input {
  padding-right: 44px;
}

.password-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(212, 165, 116, 0.3);
  color: var(--primary-color);
  cursor: pointer;
  font-size: 14px;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.3s ease;
  z-index: 100;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  opacity: 1;
  visibility: visible;
}

.password-toggle:hover {
  background: rgba(212, 165, 116, 0.25);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-50%) scale(1.05);
}

.password-toggle:focus {
  outline: none;
  background: rgba(212, 165, 116, 0.3);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.password-toggle:active {
  background: rgba(212, 165, 116, 0.4);
  transform: translateY(-50%) scale(0.95);
}

/* 错误信息 */
.error-message {
  font-size: 12px;
  color: var(--error-color);
  margin-top: 4px;
  opacity: 0;
  height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.error-message.show {
  opacity: 1;
  height: auto;
  margin-top: 8px;
}

/* 复选框组 */
.checkbox-group {
  margin: 24px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: flex-start;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  line-height: 1.4;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  background: transparent;
  position: relative;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-input:checked + .checkbox-custom {
  background: var(--primary-gold);
  border-color: var(--primary-gold);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.checkbox-text {
  font-size: 12px;
  color: var(--neutral-300);
}

.link {
  color: var(--primary-gold);
  text-decoration: none;
  transition: all 0.3s ease;
}

.link:hover {
  color: var(--primary-gold-light);
  text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  padding: 14px 16px;
  border: none;
  border-radius: 8px;
  background: var(--gradient-primary);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 20px;
}

.submit-btn:hover:not(.disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 165, 116, 0.3);
}

.submit-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.submit-btn.loading {
  opacity: 0.8;
}

/* 页面底部 */
.auth-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-text {
  color: var(--neutral-400);
  font-size: 14px;
  margin-right: 8px;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .auth-container {
    padding: 20px 16px;
  }
  
  .form-input {
    padding: 10px 12px;
  }
  
  .sms-btn {
    min-width: 80px;
    padding: 10px 12px;
    font-size: 11px;
  }
}
</style> 