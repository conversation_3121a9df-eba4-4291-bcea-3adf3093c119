<template>
  <AppPageHeader :title="'邀新活动'" @back="$router.back()" />
  <div class="activities-container" :style="{ paddingTop: '40px' }">


    <main class="main-content">
      <div class="container">
        <!-- 搜索和筛选区域 -->
        <section class="search-section">
          <div class="search-container">
            <input
              type="text"
              class="search-input"
              placeholder="搜索活动名称或关键词"
              v-model="searchText"
              @input="handleSearch"
            >
            <i class="fas fa-search search-icon"></i>
          </div>

          <!-- <div class="filter-tabs">
            <div
              v-for="filter in filterTabs"
              :key="filter.value"
              class="filter-tab"
              :class="{ active: activeFilter === filter.value }"
              @click="setActiveFilter(filter.value)"
            >
              {{ filter.label }}
            </div>
          </div> -->
        </section>

        <!-- 活动列表 -->
        <section class="activities-list">
          <!-- 加载状态 -->
          <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <div class="loading-text">加载活动数据中...</div>
          </div>

          <!-- 活动卡片 -->
          <div
            v-for="activity in filteredActivities"
            :key="activity.activityId"
            class="activity-card"
            @click="viewActivity(activity)"
          >
            <!-- 活动封面 -->
            <div class="activity-cover" v-if="activity.activityCover">
              <img :src="activity.activityCover" :alt="activity.activityName" />
            </div>

            <div class="activity-content">
              <div class="activity-title">{{ activity.activityName }}</div>
              <!-- <div class="activity-desc">{{ activity.description }}</div> -->
              <div class="activity-meta">
                <!-- <div
                  class="activity-status"
                  :class="ActivityAPI.getStatusClass(activity.statusCd)"
                >
                  {{ ActivityAPI.getStatusText(activity.statusCd) }}
                </div> -->
                <!-- <div class="activity-time">
                  {{ formatActivityTime(activity) }}
                </div> -->
              </div>

              <!-- 时间信息 -->
              <div class="activity-time-details">
                <div class="time-item">
                  <span class="time-label">活动时间：</span>
                  <span class="time-value">{{ ActivityAPI.formatDateTime(activity.startTime) }} - {{ ActivityAPI.formatDateTime(activity.endTime) }}</span>
                </div>
                <div class="time-item" v-if="activity.drawTime">
                  <span class="time-label">开奖时间：</span>
                  <span class="time-value">{{ ActivityAPI.formatDateTime(activity.drawTime) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredActivities.length === 0" class="empty-state">
            <i class="fas fa-calendar-times empty-icon"></i>
            <div class="empty-text">暂无相关活动</div>
            <div class="empty-desc">请尝试其他筛选条件或搜索关键词</div>
          </div>
        </section>
      </div>
    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import BottomNavigation from '@/components/BottomNavigation.vue'
import ActivityAPI, { type Activity } from '@/api/activity'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const router = useRouter()
const { info } = useNotification()

// 响应式数据
const searchText = ref('')
const activeFilter = ref('all')
const loading = ref(false)
const activities = ref<Activity[]>([])

// 暴露ActivityAPI到模板
const { getStatusText, getStatusClass, formatDateTime } = ActivityAPI

// 筛选选项
const filterTabs = ref([
  { label: '全部', value: 'all' },
  { label: '进行中', value: 'ONGOING' },
  { label: '即将开始', value: 'UPCOMING' },
  { label: '已结束', value: 'ENDED' },
  { label: '已取消', value: 'CANCELLED' }
])

// 计算属性：过滤后的活动列表
const filteredActivities = computed(() => {
  let filtered = activities.value

  // 按筛选条件过滤
  if (activeFilter.value !== 'all') {
    filtered = filtered.filter(activity => activity.statusCd === activeFilter.value)
  }

  // 按搜索文本过滤
  if (searchText.value.trim()) {
    const searchTerm = searchText.value.toLowerCase()
    filtered = filtered.filter(activity =>
      activity.activityName.toLowerCase().includes(searchTerm) ||
      activity.description.toLowerCase().includes(searchTerm)
    )
  }

  return filtered
})

// 方法
const goBack = () => {
  router.back()
}

const setActiveFilter = (filterValue: string) => {
  activeFilter.value = filterValue
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
  console.log('搜索:', searchText.value)
}

/**
 * 格式化活动时间显示
 */
const formatActivityTime = (activity: Activity): string => {
  const startDate = new Date(activity.startTime)
  const endDate = new Date(activity.endTime)
  const now = new Date()

  if (now < startDate) {
    return `${Math.ceil((startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))}天后开始`
  } else if (now > endDate) {
    return '活动已结束'
  } else {
    return `还剩${Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))}天`
  }
}

/**
 * 查看活动详情
 */
const viewActivity = (activity: Activity) => {
  // info(`查看活动：${activity.activityName}`)
  router.push(`/activity/${activity.activityId}`)
}

/**
 * 加载活动列表数据
 */
const loadActivities = async () => {
  try {
    loading.value = true
    const response = await ActivityAPI.getActivityList({
      pageNum: 1,
      pageSize: 50
    })

    if (response.code === 200 && response.rows) {
      activities.value = response.rows || []
    } else {
      console.warn('活动列表API返回错误:', response.msg)
    }
  } catch (error: any) {
    console.error('加载活动列表失败:', error)

    // 如果是401认证错误，不显示任何数据，让request.ts处理跳转
    if (error?.code === 401 || error?.status === 401) {
      // 401错误时不设置任何数据，保持空状态
      activities.value = []
      return
    }

    // 其他错误使用降级数据已在API中处理
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  console.log('活动列表页面已加载')
  loadActivities()
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);

  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);

  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;

  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;

  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);

  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);

  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);

  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);

  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --secondary-purple-alpha: var(--accent-alpha);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --accent-blue: var(--info-color);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.activities-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* 顶部导航 */
.header {
  position: sticky;
  top: 0;
  z-index: 50;
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 12px 0;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.back-btn {
  background: none;
  border: none;
  color: var(--primary-gold);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: var(--primary-gold-alpha);
}

.text-medium {
  font-size: 18px;
  font-weight: 600;
  color: var(--neutral-100);
}

.text-primary {
  color: var(--primary-gold);
}

/* 搜索区域 */
.search-section {
  padding: 20px 0;
}

.search-container {
  position: relative;
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 20px 12px 44px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  background: var(--bg-tertiary);
  color: var(--neutral-100);
  font-size: 14px;
  transition: all 0.3s;
  outline: none;
}

.search-input:focus {
  border-color: var(--primary-gold);
  background: var(--bg-secondary);
  box-shadow: var(--shadow-gold);
}

.search-input::placeholder {
  color: var(--neutral-400);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-gold);
  font-size: 16px;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.filter-tab {
  background: var(--bg-tertiary);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--neutral-200);
  padding: 8px 16px;
  border-radius: 20px;
  white-space: nowrap;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s;
  flex-shrink: 0;
}

.filter-tab:hover {
  border-color: var(--primary-gold);
}

.filter-tab.active {
  background: var(--gradient-primary);
  color: white;
  border-color: var(--primary-gold);
  box-shadow: var(--shadow-gold);
}

/* 活动列表 */
.activities-list {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding-bottom: 65px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--neutral-400);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(212, 165, 116, 0.2);
  border-top: 3px solid var(--primary-gold);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--neutral-400);
}

.activity-card {
  background: var(--gradient-card);
  border-radius: 16px;
  padding: 0;
  margin-bottom: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.activity-card:hover {
  transform: translateY(-2px);
  border-color: var(--primary-gold);
  box-shadow: var(--shadow-lg);
}

.activity-cover {
  width: 100%;
  height: 200px;
  overflow: hidden;
  position: relative;
}

.activity-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.activity-card:hover .activity-cover img {
  transform: scale(1.05);
}

.activity-content {
  padding: 20px;
}

.activity-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 8px;
  line-height: 1.4;
}

.activity-desc {
  font-size: 14px;
  color: var(--neutral-400);
  line-height: 1.6;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.activity-status {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-ongoing {
  background: rgba(59, 130, 246, 0.1);
  color: var(--accent-blue);
  border: 1px solid var(--accent-blue);
}

.status-upcoming {
  background: var(--primary-gold-alpha);
  color: var(--primary-gold);
  border: 1px solid var(--primary-gold);
}

.status-ended {
  background: rgba(148, 163, 184, 0.1);
  color: var(--neutral-400);
  border: 1px solid var(--neutral-400);
}

.status-cancelled {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

.status-paused {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
  border: 1px solid var(--warning-color);
}

.activity-time {
  font-size: 12px;
  color: var(--neutral-400);
  font-weight: 500;
}

.activity-time-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.time-item {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  font-size: 12px;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-label {
  color: var(--neutral-400);
  margin-right: 8px;
  min-width: 60px;
}

.time-value {
  color: var(--neutral-200);
  flex: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--neutral-400);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--neutral-600);
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: var(--neutral-600);
}

/* 页面布局 */

.main-content {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding-bottom: 65px;
}

/* 响应式调整 */
@media (max-width: 380px) {
  .container {
    padding: 0 12px;
  }

  .activity-card {
    padding: 16px;
  }

  .activity-title {
    font-size: 16px;
  }

  .filter-tabs {
    gap: 6px;
  }

  .filter-tab {
    padding: 6px 12px;
    font-size: 12px;
  }
}
</style>
