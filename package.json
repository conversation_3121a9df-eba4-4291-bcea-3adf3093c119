{"name": "cwsf-issue-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:local": "vite --mode locally", "dev:ts": "vite --mode ts", "dev:prod": "vite --mode prod", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "run-p type-check \"build-only:dev {@}\" --", "build:ts": "run-p type-check \"build-only:ts {@}\" --", "build:prod": "run-p type-check \"build-only:prod {@}\" --", "preview": "vite preview", "test:unit": "vitest", "test:e2e": "playwright test", "build-only": "vite build", "build-only:dev": "vite build --mode dev", "build-only:ts": "vite build --mode ts", "build-only:prod": "vite build --mode prod", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@types/three": "^0.178.0", "html2canvas": "^1.4.1", "pinia": "^3.0.1", "qrcode": "^1.5.4", "three": "^0.178.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@playwright/test": "^1.51.1", "@stagewise-plugins/vue": "^0.5.0", "@stagewise/toolbar-vue": "^0.4.4", "@tsconfig/node22": "^22.0.1", "@types/jsdom": "^21.1.7", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vitest/eslint-plugin": "^1.1.39", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-oxlint": "^0.16.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.0", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vitest": "^3.1.1", "vue-tsc": "^2.2.8"}}