<template>
  <AppPageHeader :title="'我的藏品'" @back="$router.back()" />

  <div class="assets-page with-bottom-nav">

    <main class="main-content">
      <!-- 搜索区域 -->
      <section class="search-section">
        <div class="search-container">
          <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索资产名称..."
              class="search-input"
              @input="handleSearch"
              @keyup.enter="doSearch"
            />
            <button v-if="searchQuery" @click="clearSearch" class="clear-btn">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </section>

      <!-- 资产列表 -->
      <section class="assets-list-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="fas fa-gem"></i>
            资产列表
          </h2>
          <div class="assets-count">
            共 {{ totalAssets }} 件
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
          </div>
          <p class="loading-text">加载中...</p>
        </div>

        <!-- 空状态 -->
        <div v-else-if="assetList.length === 0" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-gem"></i>
          </div>
          <p class="empty-text">
            {{ searchQuery ? '未找到相关资产' : '暂无资产' }}
          </p>
          <button v-if="searchQuery" @click="clearSearch" class="retry-btn">
            清除搜索
          </button>
        </div>

        <!-- 资产列表 -->
        <div v-else class="assets-grid">
          <div
            v-for="asset in assetList"
            :key="asset.dataAssetId"
            class="asset-card"
            @click="handleAssetClick(asset)"
          >
            <!-- 资产封面区域 -->
            <div class="asset-cover">
              <!-- 封面图片 -->
              <div class="cover-container">
                <img
                  v-if="asset.assetCover"
                  :src="asset.assetCover"
                  :alt="asset.assetName"
                  class="cover-image"
                  @error="handleImageError"
                />
                <div v-else class="cover-placeholder">
                  <i class="fas fa-gem"></i>
                  <span class="placeholder-text">暂无封面</span>
                </div>
              </div>
              
              <!-- 顶部标签组 -->
              <div class="badge-group">
                <!-- 状态标签 -->
                <div class="status-badge" :class="getStatusClass(asset.statusCd)">
                  <i class="fas fa-circle status-dot"></i>
                  <span>{{ getStatusText(asset.statusCd) }}</span>
                </div>
                
                <!-- 权益状态 -->
                <div v-if="asset.isRedeemed === 1" class="redeemed-badge">
                  <i class="fas fa-check-circle"></i>
                  <span>已兑换</span>
                </div>
              </div>

              <!-- 底部渐变遮罩 -->
              <div class="cover-gradient"></div>
            </div>

            <!-- 资产信息区域 -->
            <div class="asset-info">
              <!-- 主要信息 -->
              <div class="asset-header">
                <h3 class="asset-name" :title="asset.assetName">
                  {{ asset.assetName }}
                </h3>
                <div class="asset-id">
                  <i class="fas fa-hashtag"></i>
                  <span>{{ asset.assetId }}</span>
                </div>
              </div>
              
              <!-- 资产编号 -->
              <div class="asset-code">
                <i class="fas fa-barcode"></i>
                <span class="code-text">{{ asset.assetCode }}</span>
                <button class="copy-btn" @click.stop="copyAssetCode(asset.assetCode)" :title="'复制编号'">
                  <i class="fas fa-copy"></i>
                </button>
              </div>
              
              <!-- 详细信息 -->
              <div class="asset-details">
                <!-- 链上信息 -->
                <div class="detail-item" v-if="asset.chainHash">
                  <div class="detail-label">
                    <i class="fas fa-link"></i>
                    <span>链上哈希</span>
                  </div>
                  <div class="detail-value">
                    <span class="chain-hash" :title="asset.chainHash">
                      {{ formatChainHash(asset.chainHash) }}
                    </span>
                    <button class="copy-btn" @click.stop="copyChainHash(asset.chainHash)" :title="'复制哈希'">
                      <i class="fas fa-external-link-alt"></i>
                    </button>
                  </div>
                </div>
                
                <!-- 时间信息 -->
                <div class="detail-item">
                  <div class="detail-label">
                    <i class="fas fa-clock"></i>
                    <span>上链时间</span>
                  </div>
                  <div class="detail-value">
                    <span class="chain-time">{{ formatTime(asset.chainTime) }}</span>
                  </div>
                </div>
                
                <!-- 创建时间 -->
                <div class="detail-item">
                  <div class="detail-label">
                    <i class="fas fa-calendar-plus"></i>
                    <span>创建时间</span>
                  </div>
                  <div class="detail-value">
                    <span class="create-time">{{ formatTime(asset.createDate) }}</span>
                  </div>
                </div>
              </div>

              <!-- 底部操作区 -->
              <div class="asset-actions">
                <button class="action-btn model-btn" @click.stop="view3DModel(asset)">
                  <i class="fas fa-cube"></i>
                  <span>3D模型</span>
                </button>
                <button class="action-btn primary" @click.stop="viewAssetDetail(asset)">
                  <i class="fas fa-eye"></i>
                  <span>详情</span>
                </button>
                <button class="action-btn secondary" @click.stop="shareAsset(asset)">
                  <i class="fas fa-share-alt"></i>
                </button>
              </div>
            </div>


          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPages > 1" class="pagination-container">
          <div class="pagination">
            <button
              :disabled="currentPage <= 1"
              @click="goToPage(currentPage - 1)"
              class="page-btn prev-btn"
            >
              <i class="fas fa-chevron-left"></i>
            </button>
            
            <div class="page-numbers">
              <button
                v-for="page in getPageNumbers()"
                :key="page"
                :class="['page-btn', { active: page === currentPage, ellipsis: page === '...' }]"
                :disabled="page === '...'"
                @click="page !== '...' && goToPage(page as number)"
              >
                {{ page }}
              </button>
            </div>
            
            <button
              :disabled="currentPage >= totalPages"
              @click="goToPage(currentPage + 1)"
              class="page-btn next-btn"
            >
              <i class="fas fa-chevron-right"></i>
            </button>
          </div>
          
          <div class="pagination-info">
            第 {{ currentPage }} 页，共 {{ totalPages }} 页
          </div>
        </div>
      </section>
    </main>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import UserAPI, { type AssetData } from '@/api/user'
import BottomNavigation from '@/components/BottomNavigation.vue'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const router = useRouter()
const { success, error, warning, info } = useNotification()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const totalAssets = ref(0)
const assetList = ref<AssetData[]>([])
const searchTimeout = ref<number | null>(null)

// 计算属性
const totalPages = computed(() => Math.ceil(totalAssets.value / pageSize.value))

/**
 * 加载资产列表
 */
const loadAssetList = async (page: number = 1, search?: string) => {
  loading.value = true
  
  try {
    const params: any = {
      pageNum: page,
      pageSize: pageSize.value
    }
    
    // 如果有搜索条件，添加资产名称搜索参数
    if (search && search.trim()) {
      params.assetName = search.trim()
    }
    
    const response = await UserAPI.getUserAssetList(params)
    
    if (response.code === 200) {
      assetList.value = response.rows || []
      totalAssets.value = response.total || 0
      currentPage.value = page
    } else {
      throw new Error(response.msg || '获取资产列表失败')
    }
  } catch (err) {
    console.error('Failed to load asset list:', err)
    error('获取资产列表失败')
    assetList.value = []
    totalAssets.value = 0
  } finally {
    loading.value = false
  }
}

/**
 * 处理搜索输入
 */
const handleSearch = () => {
  // 清除之前的定时器
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  // 设置新的定时器，500ms后执行搜索
  searchTimeout.value = setTimeout(() => {
    doSearch()
  }, 500) as unknown as number
}

/**
 * 执行搜索
 */
const doSearch = () => {
  loadAssetList(1, searchQuery.value.trim())
}

/**
 * 清除搜索
 */
const clearSearch = () => {
  searchQuery.value = ''
  loadAssetList(1)
}

/**
 * 跳转到指定页面
 */
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value && page !== currentPage.value) {
    loadAssetList(page, searchQuery.value.trim())
  }
}

/**
 * 获取分页按钮数组
 */
const getPageNumbers = (): (number | string)[] => {
  const pages: (number | string)[] = []
  const total = totalPages.value
  const current = currentPage.value
  
  if (total <= 7) {
    // 页数较少时显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 页数较多时显示省略号
    pages.push(1)
    
    if (current > 3) {
      pages.push('...')
    }
    
    const start = Math.max(2, current - 1)
    const end = Math.min(total - 1, current + 1)
    
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    if (current < total - 2) {
      pages.push('...')
    }
    
    pages.push(total)
  }
  
  return pages
}

/**
 * 格式化链上哈希
 */
const formatChainHash = (hash: string): string => {
  if (!hash || hash.length < 10) return hash
  return `${hash.slice(0, 6)}...${hash.slice(-4)}`
}

/**
 * 格式化时间
 */
const formatTime = (timeString: string): string => {
  if (!timeString) return ''
  try {
    const date = new Date(timeString)
    return date.toLocaleDateString('zh-CN')
  } catch {
    return timeString
  }
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1000': 'status-active',      // 已上链
    '1100': 'status-pending',     // 上链中
    'active': 'status-active',
    'inactive': 'status-inactive',
    'pending': 'status-pending',
    'expired': 'status-expired'
  }
  return statusMap[status] || 'status-default'
}

/**
 * 获取状态文字
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1000': '已上链',
    '1100': '上链中',
    'active': '正常',
    'inactive': '停用',
    'pending': '待审核',
    'expired': '已过期'
  }
  return statusMap[status] || status || '未知'
}

/**
 * 处理图片加载失败
 */
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
  const placeholder = target.parentNode?.querySelector('.cover-placeholder') as HTMLElement
  if (placeholder) {
    placeholder.style.display = 'flex'
  }
}

/**
 * 复制资产编号
 */
const copyAssetCode = async (code: string) => {
  try {
    await navigator.clipboard.writeText(code)
    success('资产编号已复制到剪贴板')
  } catch (err) {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = code
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('资产编号已复制到剪贴板')
  }
}

/**
 * 复制链上哈希
 */
const copyChainHash = async (hash: string) => {
  try {
    await navigator.clipboard.writeText(hash)
    success('链上哈希已复制到剪贴板')
  } catch (err) {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = hash
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('链上哈希已复制到剪贴板')
  }
}

/**
 * 查看资产详情
 */
const viewAssetDetail = (asset: AssetData) => {
  router.push({
    name: 'purchased-asset',
    params: { dataAssetId: asset.dataAssetId.toString() }
  })
}

/**
 * 分享资产
 */
const shareAsset = (asset: AssetData) => {
  // TODO: 实现分享功能
  console.log('分享资产:', asset)
  info('分享功能开发中...')
}

/**
 * 查看3D模型
 */
const view3DModel = (asset: AssetData) => {
  router.push({
    name: 'asset-model',
    params: { assetId: asset.assetId.toString() },
    query: {
      name: asset.assetName,
      code: asset.assetCode,
      time: asset.chainTime
    }
  })
}

/**
 * 处理资产卡片点击
 */
const handleAssetClick = (asset: AssetData) => {
  viewAssetDetail(asset)
}

// 生命周期
onMounted(() => {
  loadAssetList()
})
</script>

<style scoped>
/* CSS变量定义 */
.assets-page {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --accent-color: #f4a261;
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --border-light: rgba(248, 246, 240, 0.1);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);

  background: var(--gradient-hero);
  color: var(--text-primary);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  padding: 12px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  max-width: 100%;
  margin: 0 auto;
}

.back-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--primary-color);
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.back-btn:hover {
  background: rgba(212, 165, 116, 0.2);
  transform: translateX(-2px);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  flex: 1;
  text-align: center;
}

.header-actions {
  width: 40px; /* 与back-btn宽度相同，保持对称 */
}

/* 主要内容 */
.main-content {
  padding: 0 16px 100px 16px; /* 底部预留导航空间 */
}

/* 搜索区域 */
.search-section {
  padding: 20px 0;
}

.search-container {
  position: relative;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.search-box:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.2);
}

.search-icon {
  color: var(--text-muted);
  margin-right: 8px;
  font-size: 14px;
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
  padding: 0;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.clear-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  margin-left: 8px;
}

.clear-btn:hover {
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.1);
}

/* 资产列表区域 */
.assets-list-section {
  padding-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 0 4px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.section-title i {
  color: var(--primary-color);
}

.assets-count {
  font-size: 12px;
  color: var(--text-muted);
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 24px;
  color: var(--primary-color);
  margin-bottom: 12px;
}

.loading-text {
  color: var(--text-muted);
  margin: 0;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-muted);
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  color: var(--text-muted);
  margin-bottom: 20px;
}

.retry-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
}

/* 资产网格 */
.assets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

@media (max-width: 768px) {
  .assets-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 资产卡片 */
.asset-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateZ(0);
  position: relative;
  backdrop-filter: blur(20px);
}

.asset-card:hover {
  transform: translateY(-8px) translateZ(0);
  border-color: var(--primary-color);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(212, 165, 116, 0.3);
}

.asset-card:active {
  transform: translateY(-4px) translateZ(0);
}

/* 封面区域 */
.asset-cover {
  position: relative;
  height: 220px;
  background: var(--bg-secondary);
  overflow: hidden;
}

.cover-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.asset-card:hover .cover-image {
  transform: scale(1.1);
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
  gap: 8px;
}

.cover-placeholder i {
  font-size: 48px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 12px;
  opacity: 0.8;
}

/* 底部渐变遮罩 */
.cover-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  pointer-events: none;
}

/* 标签组 */
.badge-group {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  z-index: 2;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  color: white;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.status-dot {
  font-size: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-active {
  background: rgba(144, 169, 85, 0.9);
  border-color: rgba(144, 169, 85, 0.3);
}

.status-inactive {
  background: rgba(169, 169, 169, 0.9);
  border-color: rgba(169, 169, 169, 0.3);
}

.status-pending {
  background: rgba(244, 162, 97, 0.9);
  border-color: rgba(244, 162, 97, 0.3);
}

.status-expired {
  background: rgba(230, 57, 70, 0.9);
  border-color: rgba(230, 57, 70, 0.3);
}

.status-default {
  background: rgba(116, 116, 116, 0.9);
  border-color: rgba(116, 116, 116, 0.3);
}

.redeemed-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(212, 165, 116, 0.9);
  color: white;
  padding: 6px 10px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  backdrop-filter: blur(12px);
  border: 1px solid rgba(212, 165, 116, 0.3);
}

/* 资产信息区域 */
.asset-info {
  padding: 20px;
  background: var(--gradient-card);
}

/* 资产头部 */
.asset-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  line-height: 1.4;
}

.asset-id {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--text-muted);
  background: rgba(255, 255, 255, 0.08);
  padding: 4px 8px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
  white-space: nowrap;
}

/* 资产编号 */
.asset-code {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.asset-code:hover {
  background: rgba(255, 255, 255, 0.08);
}

.asset-code i {
  color: var(--primary-color);
  font-size: 12px;
}

.code-text {
  font-size: 11px;
  color: var(--text-muted);
  font-family: 'Courier New', monospace;
  flex: 1;
}

.copy-btn {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-size: 10px;
}

.copy-btn:hover {
  color: var(--primary-color);
  background: rgba(212, 165, 116, 0.1);
  transform: scale(1.1);
}

/* 详细信息 */
.asset-details {
  margin-bottom: 16px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: var(--text-tertiary);
  min-width: 80px;
}

.detail-label i {
  color: var(--primary-color);
  width: 12px;
  text-align: center;
}

.detail-value {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: var(--text-muted);
  font-family: 'Courier New', monospace;
}

.chain-hash {
  color: var(--text-muted);
}

.chain-time, .create-time {
  color: var(--text-muted);
}

/* 操作按钮 */
.asset-actions {
  display: flex;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-btn.model-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex: 1;
}

.action-btn.model-btn:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.action-btn.primary {
  background: var(--primary-color);
  color: white;
  flex: 1;
}

.action-btn.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(212, 165, 116, 0.3);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-muted);
  min-width: 36px;
  justify-content: center;
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-1px);
}

/* 分页 */
.pagination-container {
  margin-top: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  min-width: 40px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.page-btn:hover:not(:disabled) {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.page-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.page-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.page-btn.ellipsis {
  background: none;
  border: none;
  cursor: default;
  padding: 8px 4px;
}

.pagination-info {
  font-size: 12px;
  color: var(--text-muted);
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .main-content {
    padding: 0 12px 100px 12px;
  }
  
  .search-section {
    padding: 16px 0;
  }
  
  .asset-cover {
    height: 160px;
  }
  
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .page-btn {
    min-width: 36px;
    height: 32px;
    padding: 6px 10px;
    font-size: 13px;
  }
}
</style> 