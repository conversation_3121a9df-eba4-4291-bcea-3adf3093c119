<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>凌云数资 - 科幻Banner预览</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0F0F15;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            color: #E8A317;
            text-align: center;
            margin-bottom: 30px;
            font-size: 32px;
            font-weight: bold;
        }
        
        .banner-container {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
            margin-bottom: 30px;
        }
        
        .banner-container iframe {
            width: 100%;
            height: 400px;
            border: none;
            display: block;
        }
        
        .description {
            color: #94A3B8;
            text-align: center;
            margin-top: 20px;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: rgba(26, 26, 37, 0.9);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid rgba(232, 163, 23, 0.3);
        }
        
        .feature-title {
            color: #E8A317;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #94A3B8;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <h1 class="title">凌云数资 - 科幻Banner预览</h1>
        
        <div class="banner-container">
            <svg width="100%" height="400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 400">
              <defs>
                <!-- 渐变定义 -->
                <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#0a0a0f;stop-opacity:1" />
                  <stop offset="30%" style="stop-color:#1a1a2e;stop-opacity:1" />
                  <stop offset="70%" style="stop-color:#16213e;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
                </linearGradient>
                
                <!-- 金色渐变 -->
                <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#c8860d;stop-opacity:1" />
                  <stop offset="50%" style="stop-color:#e8a317;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#c8860d;stop-opacity:1" />
                </linearGradient>
                
                <!-- 发光效果 -->
                <filter id="glow">
                  <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                  <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
                
                <!-- 强烈发光 -->
                <filter id="strongGlow">
                  <feGaussianBlur stdDeviation="8" result="coloredBlur"/>
                  <feMerge> 
                    <feMergeNode in="coloredBlur"/>
                    <feMergeNode in="SourceGraphic"/>
                  </feMerge>
                </filter>
                
                <!-- 数据流渐变 -->
                <linearGradient id="dataFlow" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:0" />
                  <stop offset="50%" style="stop-color:#00d4ff;stop-opacity:0.8" />
                  <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:0" />
                  <animateTransform attributeName="gradientTransform" 
                                    type="translate" 
                                    values="-200 0;1400 0;-200 0" 
                                    dur="3s" 
                                    repeatCount="indefinite"/>
                </linearGradient>
              </defs>
              
              <!-- 背景 -->
              <rect width="1200" height="400" fill="url(#bgGradient)"/>
              
              <!-- 星空背景 -->
              <g id="stars">
                <circle cx="150" cy="80" r="1" fill="#ffffff" opacity="0.8">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
                </circle>
                <circle cx="250" cy="120" r="0.5" fill="#ffffff" opacity="0.6">
                  <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3s" repeatCount="indefinite"/>
                </circle>
                <circle cx="350" cy="60" r="1.5" fill="#e8a317" opacity="0.7">
                  <animate attributeName="opacity" values="0.4;1;0.4" dur="2.5s" repeatCount="indefinite"/>
                </circle>
                <circle cx="500" cy="100" r="0.8" fill="#ffffff" opacity="0.5">
                  <animate attributeName="opacity" values="0.3;0.9;0.3" dur="4s" repeatCount="indefinite"/>
                </circle>
                <circle cx="700" cy="90" r="1" fill="#00d4ff" opacity="0.6">
                  <animate attributeName="opacity" values="0.2;1;0.2" dur="2.8s" repeatCount="indefinite"/>
                </circle>
                <circle cx="850" cy="70" r="0.7" fill="#ffffff" opacity="0.7">
                  <animate attributeName="opacity" values="0.4;0.9;0.4" dur="3.2s" repeatCount="indefinite"/>
                </circle>
                <circle cx="950" cy="110" r="1.2" fill="#e8a317" opacity="0.8">
                  <animate attributeName="opacity" values="0.3;1;0.3" dur="2.2s" repeatCount="indefinite"/>
                </circle>
                <circle cx="1050" cy="85" r="0.6" fill="#ffffff" opacity="0.5">
                  <animate attributeName="opacity" values="0.2;0.8;0.2" dur="3.5s" repeatCount="indefinite"/>
                </circle>
              </g>
              
              <!-- 几何图形装饰 -->
              <g id="geometry" opacity="0.3">
                <!-- 六边形 -->
                <polygon points="100,300 120,285 140,300 140,330 120,345 100,330" 
                         fill="none" 
                         stroke="#00d4ff" 
                         stroke-width="1">
                  <animateTransform attributeName="transform" 
                                    type="rotate" 
                                    values="0 120 315;360 120 315" 
                                    dur="15s" 
                                    repeatCount="indefinite"/>
                </polygon>
                
                <!-- 三角形 -->
                <polygon points="1100,320 1130,280 1160,320" 
                         fill="none" 
                         stroke="#e8a317" 
                         stroke-width="1">
                  <animateTransform attributeName="transform" 
                                    type="rotate" 
                                    values="0 1130 306;-360 1130 306" 
                                    dur="12s" 
                                    repeatCount="indefinite"/>
                </polygon>
                
                <!-- 圆环 -->
                <circle cx="80" cy="200" r="25" 
                        fill="none" 
                        stroke="#c8860d" 
                        stroke-width="2" 
                        opacity="0.4">
                  <animate attributeName="r" values="20;30;20" dur="4s" repeatCount="indefinite"/>
                </circle>
              </g>
              
              <!-- 数据流线条 -->
              <g id="dataStreams">
                <rect x="0" y="150" width="1200" height="2" fill="url(#dataFlow)"/>
                <rect x="0" y="250" width="1200" height="1" fill="url(#dataFlow)">
                  <animateTransform attributeName="transform" 
                                    type="translate" 
                                    values="0 0;0 0" 
                                    dur="4s" 
                                    repeatCount="indefinite"/>
                </rect>
              </g>
              
              <!-- 中央装饰环 -->
              <g id="centerRing" transform="translate(600, 200)">
                <circle r="80" fill="none" stroke="#e8a317" stroke-width="1" opacity="0.3">
                  <animate attributeName="r" values="70;90;70" dur="6s" repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.2;0.5;0.2" dur="6s" repeatCount="indefinite"/>
                </circle>
                <circle r="60" fill="none" stroke="#00d4ff" stroke-width="1" opacity="0.4">
                  <animateTransform attributeName="transform" 
                                    type="rotate" 
                                    values="0;360" 
                                    dur="20s" 
                                    repeatCount="indefinite"/>
                </circle>
              </g>
              
              <!-- 主标题 "凌云数资" -->
              <g id="mainTitle" transform="translate(600, 200)">
                <!-- 背景光晕 -->
                <text x="0" y="0" 
                      text-anchor="middle" 
                      dominant-baseline="middle"
                      font-family="'PingFang SC', 'Microsoft YaHei', sans-serif" 
                      font-size="72" 
                      font-weight="900"
                      fill="url(#goldGradient)"
                      filter="url(#strongGlow)"
                      opacity="0.6">凌云数资</text>
                
                <!-- 主文字 -->
                <text x="0" y="0" 
                      text-anchor="middle" 
                      dominant-baseline="middle"
                      font-family="'PingFang SC', 'Microsoft YaHei', sans-serif" 
                      font-size="72" 
                      font-weight="900"
                      fill="url(#goldGradient)"
                      filter="url(#glow)">凌云数资
                  <animate attributeName="opacity" values="0.8;1;0.8" dur="3s" repeatCount="indefinite"/>
                </text>
              </g>
              
              <!-- 副标题 -->
              <g id="subtitle" transform="translate(600, 260)">
                <text x="0" y="0" 
                      text-anchor="middle" 
                      dominant-baseline="middle"
                      font-family="'PingFang SC', 'Microsoft YaHei', sans-serif" 
                      font-size="24" 
                      font-weight="300"
                      fill="#00d4ff"
                      opacity="0.8"
                      letter-spacing="8px">DIGITAL ASSETS PLATFORM</text>
              </g>
              
              <!-- 装饰性粒子 -->
              <g id="particles">
                <circle cx="200" cy="180" r="2" fill="#e8a317" opacity="0.6">
                  <animateTransform attributeName="transform" 
                                    type="translate" 
                                    values="0 0;20 -10;0 0" 
                                    dur="5s" 
                                    repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.3;0.8;0.3" dur="5s" repeatCount="indefinite"/>
                </circle>
                
                <circle cx="1000" cy="220" r="1.5" fill="#00d4ff" opacity="0.7">
                  <animateTransform attributeName="transform" 
                                    type="translate" 
                                    values="0 0;-15 15;0 0" 
                                    dur="6s" 
                                    repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.4;1;0.4" dur="6s" repeatCount="indefinite"/>
                </circle>
                
                <circle cx="400" cy="300" r="1" fill="#ffffff" opacity="0.5">
                  <animateTransform attributeName="transform" 
                                    type="translate" 
                                    values="0 0;10 -20;0 0" 
                                    dur="4s" 
                                    repeatCount="indefinite"/>
                </circle>
                
                <circle cx="800" cy="160" r="2.5" fill="#c8860d" opacity="0.4">
                  <animateTransform attributeName="transform" 
                                    type="translate" 
                                    values="0 0;-25 5;0 0" 
                                    dur="7s" 
                                    repeatCount="indefinite"/>
                  <animate attributeName="opacity" values="0.2;0.6;0.2" dur="7s" repeatCount="indefinite"/>
                </circle>
              </g>
              
              <!-- 边框装饰 -->
              <g id="borders" opacity="0.3">
                <!-- 左边框 -->
                <rect x="20" y="50" width="2" height="300" fill="url(#goldGradient)">
                  <animate attributeName="height" values="250;350;250" dur="8s" repeatCount="indefinite"/>
                </rect>
                
                <!-- 右边框 -->
                <rect x="1178" y="50" width="2" height="300" fill="url(#goldGradient)">
                  <animate attributeName="height" values="300;250;300" dur="8s" repeatCount="indefinite"/>
                </rect>
                
                <!-- 顶部装饰线 -->
                <rect x="50" y="30" width="1100" height="1" fill="#00d4ff" opacity="0.5">
                  <animate attributeName="opacity" values="0.3;0.7;0.3" dur="4s" repeatCount="indefinite"/>
                </rect>
                
                <!-- 底部装饰线 -->
                <rect x="50" y="370" width="1100" height="1" fill="#00d4ff" opacity="0.5">
                  <animate attributeName="opacity" values="0.5;0.3;0.5" dur="4s" repeatCount="indefinite"/>
                </rect>
              </g>
            </svg>
        </div>

        <p class="description">
            这是专为"凌云数资"平台设计的科幻风格动画banner，采用SVG技术实现了复杂的动画效果，
            包括星空背景、数据流动、几何图形旋转等视觉元素，完美展现了数字资产平台的科技感和未来感。
        </p>

        <div class="features">
            <div class="feature-card">
                <div class="feature-title">🎨 视觉设计</div>
                <div class="feature-description">
                    采用深蓝渐变背景，金色主标题，青色副标题，营造出深邃的科技氛围
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">✨ 动画效果</div>
                <div class="feature-description">
                    星空闪烁、数据流动、几何图形旋转、粒子浮动，多层次动画创造沉浸感
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🔥 发光特效</div>
                <div class="feature-description">
                    主标题使用双层发光效果，副标题带有阴影，整体呈现梦幻科幻质感
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">📱 响应式</div>
                <div class="feature-description">
                    SVG矢量格式确保在任何屏幕尺寸下都能保持清晰，完美适配移动端
                </div>
            </div>
        </div>
    </div>
</body>
</html> 