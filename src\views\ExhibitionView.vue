<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import BottomNavigation from '@/components/BottomNavigation.vue'
import SearchBox from '@/components/SearchBox.vue'
import AssetCard from '@/components/AssetCard.vue'
import { getSeriesList, getZonesList, getAssetList } from '@/api/exhibition'
import type { AssetListParams, AssetItem, IssuerInfo } from '@/api/exhibition'
import type { ApiResponse } from '@/api/types'
import { DictionaryAPI } from '@/api/dictionary'
import type { DictionaryItem } from '@/api/dictionary'
import { ZoneAPI } from '@/api/zone'
import type { ZoneListItem } from '@/api/zone'

// 路由
const router = useRouter()

// 搜索相关
const searchText = ref('')

// 筛选和排序
const selectedZone = ref('')
const selectedSeries = ref('')
const selectedAssetType = ref('')
const activeSort = ref('newest')

// 下拉菜单状态
const showZoneDropdown = ref(false)
const showSeriesDropdown = ref(false)
const showTypeDropdown = ref(false)

// 当前激活的筛选标签
const activeFilterTab = ref('')

// 专区数据
const zonesList = ref<ZoneListItem[]>([])
const zonesLoading = ref(false)

// 资产类型数据
const assetTypesList = ref<DictionaryItem[]>([])
const assetTypesLoading = ref(false)

// 下拉框数据（移除重复声明）
const seriesList = ref<Array<{seriesId: string, name: string}>>([])
const loading = ref(false)
const seriesLoading = ref(false)

// 资产数据状态
const exhibitions = ref<AssetItem[]>([])
const exhibitionsLoading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 根据筛选条件筛选展示的资产（API已处理专区和类型筛选，这里只处理排序）
const filteredExhibitions = computed(() => {
  let filtered = [...exhibitions.value]
  
  // API已经处理了专区和资产类型筛选，这里不需要重复筛选
  // 只处理排序逻辑
  if (activeSort.value === 'newest') {
    filtered.sort((a, b) => {
      const timeA = a.createTime ? new Date(a.createTime).getTime() : 0
      const timeB = b.createTime ? new Date(b.createTime).getTime() : 0
      return timeB - timeA
    })
  } else if (activeSort.value === 'popular') {
    filtered.sort((a, b) => (b.popularity || 0) - (a.popularity || 0))
  } else if (activeSort.value === 'price') {
    filtered.sort((a, b) => {
      const priceA = parseFloat(String(a.price || a.issuePrice || '0').replace(/[¥,]/g, ''))
      const priceB = parseFloat(String(b.price || b.issuePrice || '0').replace(/[¥,]/g, ''))
      return priceA - priceB
    })
  }

  return filtered
})

// 计算总数
const totalCount = computed(() => filteredExhibitions.value.length)

// 选择专区
const selectZone = (zoneId: string | number) => {
  console.log('🎯 选择专区:', zoneId, '类型:', typeof zoneId)
  selectedZone.value = String(zoneId)
  console.log('📝 设置selectedZone:', selectedZone.value, '类型:', typeof selectedZone.value)
  currentPage.value = 1
  activeFilterTab.value = '' // 关闭筛选面板
  fetchAssetList()
}

// 选择系列
const selectSeries = (seriesId: string | number) => {
  console.log('🎯 选择系列:', seriesId, '类型:', typeof seriesId)
  selectedSeries.value = String(seriesId)
  console.log('📝 设置selectedSeries:', selectedSeries.value, '类型:', typeof selectedSeries.value)
  currentPage.value = 1
  activeFilterTab.value = '' // 关闭筛选面板
  fetchAssetList()
}

// 选择资产类型
const selectAssetType = (assetType: string) => {
  selectedAssetType.value = assetType
  currentPage.value = 1
  activeFilterTab.value = '' // 关闭筛选面板
  fetchAssetList()
}

// 切换筛选标签展开/收起状态
const toggleFilterTab = (tabName: string) => {
  if (activeFilterTab.value === tabName) {
    // 如果点击的是当前激活的标签，则收起
    activeFilterTab.value = ''
  } else {
    // 如果点击的是其他标签，则切换到该标签
    activeFilterTab.value = tabName
  }
}

// 重置筛选条件
const resetFilters = () => {
  selectedZone.value = ''
  selectedSeries.value = ''
  selectedAssetType.value = ''
  currentPage.value = 1
  fetchAssetList()
}

// 获取资产列表
const fetchAssetList = async () => {
  try {
    exhibitionsLoading.value = true
    
    const params: AssetListParams = {
      page: currentPage.value,
      size: pageSize.value
    }
    
    // 添加搜索参数
    if (searchText.value.trim()) {
      params.assetName = searchText.value.trim()
    }
    
    // 添加筛选参数（移除系列筛选）
    
    if (selectedZone.value && selectedZone.value.trim() !== '') {
      const zoneId = parseInt(selectedZone.value.trim(), 10)
      if (!isNaN(zoneId)) {
        params.zoneId = zoneId
        console.log('🔍 添加专区筛选参数 zoneId:', zoneId)
      } else {
        console.warn('⚠️ 专区ID转换失败:', selectedZone.value)
      }
    }
    
    if (selectedAssetType.value && selectedAssetType.value.trim() !== '') {
      params.assetType = selectedAssetType.value.trim()
      console.log('🔍 添加类型筛选参数 assetType:', params.assetType)
    }

    if (selectedSeries.value && selectedSeries.value.trim() !== '') {
      const seriesId = parseInt(selectedSeries.value.trim(), 10)
      if (!isNaN(seriesId)) {
        params.seriesId = seriesId
        console.log('🔍 添加系列筛选参数 seriesId:', seriesId)
      } else {
        console.warn('⚠️ 系列ID转换失败:', selectedSeries.value)
      }
    }
    
    console.log('📡 请求参数:', params)
    const response = await getAssetList(params)
    
    if (response && (response as any).code === 200) {
      // 处理不同的API响应格式
      let assetList: AssetItem[] = []
      let totalCount = 0
      
      const responseData = (response as any).data || response
      
      if (Array.isArray(responseData)) {
        assetList = responseData
        totalCount = responseData.length
      } else if (responseData && responseData.rows && Array.isArray(responseData.rows)) {
        assetList = responseData.rows
        totalCount = responseData.total || responseData.rows.length
      } else if (responseData && responseData.list && Array.isArray(responseData.list)) {
        assetList = responseData.list
        totalCount = responseData.total || responseData.list.length
      } else if (typeof responseData === 'object' && responseData !== null) {
        const keys = Object.keys(responseData)
        for (const key of keys) {
          const value = responseData[key]
          if (Array.isArray(value)) {
            assetList = value
            totalCount = responseData.total || value.length
            break
          }
        }
      }
      
      // 接口无数据时直接设置exhibitions.value = []，不再赋值模拟数据
      exhibitions.value = assetList
      total.value = totalCount
      
      console.log('✅ 资产列表更新完成')
      console.log('📊 当前专区筛选:', selectedZone.value)
      console.log('📊 返回资产数量:', assetList.length)
      console.log('📊 资产列表示例:', assetList.slice(0, 2))
      
    } else {
      exhibitions.value = []
      total.value = 0
      console.warn('⚠️ API响应格式异常或无数据')
    }
  } catch (error) {
    console.error('获取资产列表失败:', error)
    exhibitions.value = []
    total.value = 0
  } finally {
    exhibitionsLoading.value = false
  }
}

// 获取专区列表
const fetchZonesList = async () => {
  try {
    zonesLoading.value = true
    console.log('📡 开始请求专区列表数据...')
    
    const response = await ZoneAPI.getZoneListName()
    
    console.log('📥 专区列表API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      zonesList.value = response.data
      console.log('✅ 专区列表数据加载成功:', zonesList.value.length, '个专区')
      
      // 默认选中第一个专区
      if (zonesList.value.length > 0) {
        const firstZone = zonesList.value[0]
        selectedZone.value = String(firstZone.zoneId)
        console.log('🎯 默认选中第一个专区:', firstZone.zoneName, 'ID:', selectedZone.value)
      }
    } else {
      zonesList.value = []
      console.warn('⚠️ 专区列表接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取专区列表失败:', error)
    zonesList.value = []
  } finally {
    zonesLoading.value = false
  }
}

// 获取资产类型字典数据
const fetchAssetTypes = async () => {
  try {
    assetTypesLoading.value = true
    console.log('📡 开始请求资产类型字典数据...')
    
    const response = await DictionaryAPI.getDictionary('dig_asset_type')
    
    console.log('📥 资产类型字典API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      assetTypesList.value = response.data
      console.log('✅ 资产类型字典数据加载成功:', assetTypesList.value.length, '种类型')
    } else {
      assetTypesList.value = []
      console.warn('⚠️ 资产类型字典接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取资产类型字典失败:', error)
    assetTypesList.value = []
  } finally {
    assetTypesLoading.value = false
  }
}

// 获取系列列表（保留原有功能）
const fetchSeriesList = async () => {
  try {
    seriesLoading.value = true
    const response = await getSeriesList() as ApiResponse<Array<any>>
    if (response && response.code === 200 && response.data) {
      let processedData: Array<{seriesId: string, name: string}> = []
      if (Array.isArray(response.data)) {
        processedData = response.data.map((item: any, index: number) => {
          // 兼容后端返回的seriesId字段
          let seriesId = item.seriesId || item.id || String(index + 1)
          return { seriesId: String(seriesId), name: item.name || item.seriesName || seriesId }
        })
      }
      if (processedData.length > 0) {
        seriesList.value = processedData
      }
    }
  } catch (error) {
    console.error('获取系列列表失败:', error)
  } finally {
    seriesLoading.value = false
  }
}

// 方法
const handleSearch = (keyword: string) => {
  if (keyword !== undefined) {
    searchText.value = keyword
  }
  currentPage.value = 1
  fetchAssetList()
}

const handleProductClick = (product: AssetItem | any) => {
  const assetId = product.assetId || product.id
  if (assetId) {
    router.push({ name: 'asset-detail', params: { assetId: String(assetId) } })
  } else {
    console.warn('资产缺少ID信息，无法跳转到详情页')
  }
}

// 格式化价格显示
const formatPrice = (product: AssetItem): string => {
  // 判断是否已到开售时间
  const isSaleStarted = (() => {
    if (!product.saleStartTime) return true // 没有开售时间则默认已开售
    const saleTime = new Date(product.saleStartTime)
    const now = new Date()
    return saleTime <= now
  })()
  
  // 如果未到开售时间，显示???
  if (!isSaleStarted) {
    return '¥-.-'
  }
  
  const price = product.issuePrice || product.price || 0
  if (typeof price === 'number') {
    return `¥${price.toFixed(2)}`
  }
  return `¥${String(price).replace(/[¥]/g, '') || '0.00'}`
}

// 格式化发售时间
const formatSaleTime = (timeStr: string): string => {
  try {
    const date = new Date(timeStr)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    
    return `${month}/${day} ${hours}:${minutes}`
  } catch (error) {
    if (timeStr.length >= 16) {
      return timeStr.slice(5, 16).replace('T', ' ')
    }
    return timeStr
  }
}

// 获取发行方信息
const getIssuerInfo = (product: AssetItem): Array<{ name: string, logo?: string }> => {
  if (product.issuers && Array.isArray(product.issuers) && product.issuers.length > 0) {
    return product.issuers.map(issuer => {
      const name = issuer.issuerName || issuer.name || '未知发行方'
      const logo = issuer.issuerLogo || issuer.logo
      return { name, logo }
    })
  }
  return []
}

// 获取产品标签
const getProductTag = (product: AssetItem): { text: string, type: string } | null => {
  if (product.statusCd) {
    switch (product.statusCd) {
      case 'ON_SALE':
        return { text: '在售', type: 'on-sale' }
      case 'SOLD_OUT':
        return { text: '售罄', type: 'sold-out' }
      case 'LIMITED':
        return { text: '限量', type: 'limited' }
      default:
        return { text: product.statusCd, type: 'default' }
    }
  }
  
  if (product.saleStartTime) {
    const saleTime = new Date(product.saleStartTime)
    const now = new Date()
    if (saleTime > now) {
      return { text: '预售', type: 'limited' }
    }
  }
  
  return null
}

// 获取关键词数组
const getProductKeywords = (product: AssetItem): string[] => {
  if (product.assetKeywords) {
    return product.assetKeywords.split(',').map(k => k.trim()).filter(k => k)
  }
  return []
}

// 生命周期
onMounted(async () => {
  await Promise.all([
    fetchZonesList(),
    fetchAssetTypes(),
    fetchSeriesList()
  ])
  await fetchAssetList()
})
</script>

<template>
  <div class="exhibition-container">
    <main class="main-content">
      <div class="container">
        <!-- 搜索区域 -->
        <div class="search-section">
          <SearchBox 
            v-model="searchText"
            @search="handleSearch"
            placeholder="搜索您感兴趣的数字藏品..."
          />
        </div>

        <!-- 筛选区域 -->
        <section class="filter-section">
          <!-- 顶部筛选标签 -->
          <div class="filter-header-tabs">
            <button 
              class="filter-header-tab"
              :class="{ active: activeFilterTab === 'zone' }"
              @click="toggleFilterTab('zone')"
            >
              <span class="header-tab-text">专区</span>
              <i class="fas fa-chevron-down" :class="{ 'rotate': activeFilterTab === 'zone' }"></i>
            </button>
            
            <button 
              class="filter-header-tab"
              :class="{ active: activeFilterTab === 'series' }"
              @click="toggleFilterTab('series')"
            >
              <span class="header-tab-text">系列</span>
              <i class="fas fa-chevron-down" :class="{ 'rotate': activeFilterTab === 'series' }"></i>
            </button>
            
            <button 
              class="filter-header-tab"
              :class="{ active: activeFilterTab === 'type' }"
              @click="toggleFilterTab('type')"
            >
              <span class="header-tab-text">类型</span>
              <i class="fas fa-chevron-down" :class="{ 'rotate': activeFilterTab === 'type' }"></i>
            </button>
          </div>

          <!-- 筛选选项展开区域 -->
          <div v-if="activeFilterTab" class="filter-options-panel">
            <!-- 专区筛选选项 -->
            <div v-if="activeFilterTab === 'zone'" class="filter-options-grid">
              <div v-if="zonesLoading" class="filter-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
              </div>
              <button 
                v-else-if="zonesList.length === 0"
                class="filter-option-btn"
                :class="{ active: selectedZone === '' }"
                @click="selectZone('')"
              >
                <span class="option-text">全部专区</span>
              </button>
              <button 
                v-for="zone in zonesList" 
                :key="zone.zoneId"
                class="filter-option-btn"
                :class="{ active: selectedZone === String(zone.zoneId) }"
                @click="selectZone(zone.zoneId)"
              >
                <span class="option-text">{{ zone.zoneName }}</span>
                <span v-if="zone.assetCount !== undefined" class="option-count">
                  {{ zone.assetCount }}
                </span>
              </button>
            </div>

            <!-- 系列筛选选项 -->
            <div v-if="activeFilterTab === 'series'" class="filter-options-grid">
              <div v-if="seriesLoading" class="filter-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
              </div>
              <button 
                v-else
                class="filter-option-btn"
                :class="{ active: selectedSeries === '' }"
                @click="selectSeries('')"
              >
                <span class="option-text">全部系列</span>
              </button>
              <button 
                v-for="series in seriesList" 
                :key="series.seriesId"
                class="filter-option-btn"
                :class="{ active: selectedSeries === String(series.seriesId) }"
                @click="selectSeries(series.seriesId)"
              >
                <span class="option-text">{{ series.name }}</span>
              </button>
            </div>

            <!-- 类型筛选选项 -->
            <div v-if="activeFilterTab === 'type'" class="filter-options-grid">
              <div v-if="assetTypesLoading" class="filter-loading">
                <i class="fas fa-spinner fa-spin"></i>
                <span>加载中...</span>
              </div>
              <button 
                v-else
                class="filter-option-btn"
                :class="{ active: selectedAssetType === '' }"
                @click="selectAssetType('')"
              >
                <span class="option-text">全部类型</span>
              </button>
              <button 
                v-for="assetType in assetTypesList" 
                :key="assetType.value"
                class="filter-option-btn"
                :class="{ active: selectedAssetType === assetType.value }"
                @click="selectAssetType(assetType.value)"
              >
                <span class="option-text">{{ assetType.label }}</span>
              </button>
            </div>
          </div>
        </section>

        <!-- 内容区域 -->
        <section class="content-section">
          <!-- 统计信息 -->
          <!--<div class="stats-bar">
            <div class="stats-info">
              <i class="fas fa-layer-group stats-icon"></i>
              <span class="stats-text">
                当前筛选结果
                <strong>{{ totalCount }}</strong> 件藏品
              </span>
            </div>
            
            
             <div class="sort-options">
              <button 
                class="sort-btn"
                :class="{ active: activeSort === 'newest' }"
                @click="activeSort = 'newest'"
              >
                <i class="fas fa-clock"></i>
                <span>最新发布</span>
              </button>
              <button 
                class="sort-btn"
                :class="{ active: activeSort === 'popular' }"
                @click="activeSort = 'popular'"
              >
                <i class="fas fa-fire"></i>
                <span>热门推荐</span>
              </button>
              <button 
                class="sort-btn"
                :class="{ active: activeSort === 'price' }"
                @click="activeSort = 'price'"
              >
                <i class="fas fa-tag"></i>
                <span>价格排序</span>
              </button>
            </div>
          </div> -->

          <!-- 藏品列表 - 单列大图布局 -->
          <div class="assets-list">
            <!-- 加载状态 -->
            <div v-if="exhibitionsLoading" class="loading-state">
              <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
              </div>
              <p class="loading-text">正在加载藏品...</p>
            </div>
            
            <!-- 藏品卡片列表 -->
            <div v-else class="assets-container">
              <article 
                v-for="(product, index) in filteredExhibitions" 
                :key="product.assetId || product.id || index"
                class="asset-card-large"
                @click="handleProductClick(product)"
              >
                <!-- 左侧图片区域 -->
                <div class="asset-image-section">
                  <div class="image-container">
                    <!-- 资产图片 -->
                    <img 
                      v-if="product.assetCoverThumbnail || product.image"
                      :src="product.assetCoverThumbnail || product.image" 
                      :alt="product.assetName || product.name"
                      class="asset-image"
                      loading="lazy"
                    >
                    <div v-else class="image-placeholder">
                      <i class="fas fa-image"></i>
                      <span>暂无图片</span>
                    </div>
                    
                    <!-- 状态标签 -->
                    <div v-if="getProductTag(product)" class="status-tag" :class="getProductTag(product)?.type">
                      {{ getProductTag(product)?.text }}
                    </div>
                    
                    <!-- 收藏按钮 -->
                    <!-- <button class="favorite-btn" @click.stop="">
                      <i class="far fa-heart"></i>
                    </button> -->
                  </div>
                </div>

                <!-- 右侧信息区域 -->
                <div class="asset-info-section">
                  <!-- 标题和描述 -->
                  <div class="asset-header">
                    <h3 class="asset-title">{{ product.assetName || product.name || '未命名资产' }}</h3>
                    <!-- <p v-if="product.assetDesc" class="asset-desc">{{ product.assetDesc }}</p> -->
                  </div>

                  <!-- 关键词标签 -->
                  <div v-if="getProductKeywords(product).length > 0" class="keywords-section">
                    <span 
                      v-for="keyword in getProductKeywords(product).slice(0, 3)" 
                      :key="keyword"
                      class="keyword-tag"
                    >
                      {{ keyword }}
                    </span>
                  </div>

                  <!-- 发行方信息 -->
                  <div v-if="getIssuerInfo(product).length > 0" class="issuer-section">
                    <div 
                      v-for="(issuer, index) in getIssuerInfo(product)" 
                      :key="`${product.assetId || product.id}-issuer-${index}`"
                      class="issuer-info"
                    >
                      <div class="issuer-avatar">
                        <img 
                          v-if="issuer.logo"
                          :src="issuer.logo" 
                          :alt="issuer.name"
                          class="issuer-logo"
                        >
                        <i v-else class="fas fa-building default-issuer-icon"></i>
                      </div>
                      <div class="issuer-details">
                        <span class="issuer-label">发行方</span>
                        <span class="issuer-name">{{ issuer.name }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 价格和时间信息 -->
                  <div class="asset-footer">
                    <div class="price-section">
                      <span class="price-label">发行价</span>
                      <span class="price-value">{{ formatPrice(product) }}</span>
                    </div>
                    
                    <!-- 库存信息 -->
                    <!-- <div v-if="product.inventoryNum !== undefined && product.inventoryNum !== null" class="inventory-section">
                      <span class="inventory-label">剩余</span>
                      <span class="inventory-value" :class="{ 'low-stock': product.inventoryNum <= 10, 'out-of-stock': product.inventoryNum <= 0 }">
                        {{ product.inventoryNum }}
                      </span>
                      <span class="inventory-unit">份</span>
                    </div> -->
                    
                    <div v-if="product.saleStartTime" class="time-section">
                      <i class="far fa-clock time-icon"></i>
                      <span class="time-text">{{ formatSaleTime(product.saleStartTime) }}</span>
                    </div>
                  </div>
                </div>
              </article>
            </div>

            <!-- 空状态 -->
            <div v-if="!exhibitionsLoading && filteredExhibitions.length === 0" class="empty-state">
              <div class="empty-icon">
                <i class="fas fa-search"></i>
              </div>
              <h3 class="empty-title">暂无相关藏品</h3>
              <p class="empty-desc">
                当前筛选条件下暂时没有符合条件的藏品，
                请尝试调整筛选条件或搜索关键词
              </p>
            </div>
          </div>
        </section>
      </div>
    </main>

    <!-- 底部导航组件 -->
    <BottomNavigation />
  </div>
</template>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.exhibition-container {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 16px;
}

/* 搜索区域 */
.search-section {
  margin: 20px 0;
}

/* 筛选区域 */
.filter-section {
  margin: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 0;
  background: var(--bg-secondary);
  border-radius: 12px;
  overflow: visible;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
}

/* 顶部筛选标签 */
.filter-header-tabs {
  display: flex;
  background: var(--bg-primary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px 12px 0 0;
  position: relative;
  z-index: 101;
}

.filter-header-tab {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 20px;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-header-tab:last-child {
  border-right: none;
}

.filter-header-tab:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

.filter-header-tab.active {
  background: var(--bg-secondary);
  color: var(--primary-color);
  font-weight: 600;
}

.filter-header-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-color);
}

.header-tab-text {
  font-weight: inherit;
}

.filter-header-tab .fa-chevron-down {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.filter-header-tab .fa-chevron-down.rotate {
  transform: rotate(180deg);
}

/* 筛选选项展开区域 */
.filter-options-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
  z-index: 100;
  backdrop-filter: blur(15px);
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-options-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  max-width: 100%;
}

.filter-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 20px;
  color: var(--text-muted);
  font-size: 14px;
  grid-column: 1 / -1;
}

.filter-option-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 60px;
  justify-content: center;
}

.filter-option-btn:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--primary-color);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.filter-option-btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(212, 165, 116, 0.4);
}

.option-text {
  font-weight: inherit;
  line-height: 1.2;
}

.option-count {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 11px;
  font-weight: 600;
  line-height: 1;
  min-width: 16px;
  text-align: center;
}

.filter-option-btn.active .option-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-header-tab {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .filter-options-panel {
    padding: 16px;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.5);
  }
  
  .filter-options-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
  }
  
  .filter-option-btn {
    padding: 10px 12px;
    font-size: 12px;
    min-height: 50px;
  }
}

@media (max-width: 480px) {
  .filter-header-tab {
    padding: 10px 12px;
    font-size: 12px;
    gap: 6px;
  }
  
  .filter-options-panel {
    padding: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  }
  
  .filter-options-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }
  
  .filter-option-btn {
    padding: 8px 10px;
    font-size: 11px;
    min-height: 45px;
  }
  
  .option-count {
    font-size: 10px;
    padding: 1px 4px;
  }
}

/* 已移除专区Tab导航样式，改为筛选区域 */

/* 内容区域 */
.content-section {
  margin: 24px 0;
}

/* 统计信息栏 */
.stats-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  backdrop-filter: blur(15px);
  border-radius: 12px;
  margin: 20px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.5);
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.stats-text {
  font-size: 14px;
  color: var(--text-secondary);
}

.stats-text strong {
  color: var(--primary-color);
  font-weight: 700;
}

.sort-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.sort-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.sort-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.sort-btn:hover::before {
  left: 100%;
}

.sort-btn:hover {
  background: rgba(255, 255, 255, 0.12);
  border-color: var(--primary-color);
  color: var(--text-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.sort-btn.active {
  background: rgba(212, 165, 116, 0.2);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
}

.sort-btn.active::before {
  display: none;
}

.sort-btn i {
  font-size: 10px;
  color: inherit;
  transition: all 0.3s ease;
}

.sort-btn:hover i,
.sort-btn.active i {
  transform: scale(1.1);
}

.sort-btn span {
  font-weight: 500;
}

/* 藏品列表 */
.assets-list {
  margin: 24px 0;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  font-size: 32px;
  color: var(--primary-color);
}

.loading-text {
  font-size: 16px;
  color: var(--text-tertiary);
  margin: 0;
}

/* 藏品容器 */
.assets-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 紧凑卡片式设计 */
.asset-card-large {
  background: linear-gradient(135deg, rgba(42, 42, 42, 0.98) 0%, rgba(36, 36, 36, 0.95) 50%, rgba(46, 46, 46, 0.92) 100%);
  backdrop-filter: blur(25px);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  gap: 16px;
  cursor: pointer;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(212, 165, 116, 0.15);
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.4),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
  margin: 8px 0;
}

.asset-card-large::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(110deg, transparent 30%, rgba(212, 165, 116, 0.08) 50%, transparent 70%);
  transition: left 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.asset-card-large::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.asset-card-large:hover::before {
  left: 100%;
}

.asset-card-large:hover::after {
  opacity: 1;
}

.asset-card-large:hover {
  transform: translateY(-6px) scale(1.03);
  border-color: rgba(212, 165, 116, 0.35);
  box-shadow: 
    0 20px 50px rgba(0, 0, 0, 0.6),
    0 8px 25px rgba(212, 165, 116, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 左侧图片区域 - 紧凑设计 */
.asset-image-section {
  flex: 0 0 140px;
  position: relative;
}

.image-container {
  width: 100%;
  height: 140px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.asset-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  filter: brightness(0.95) contrast(1.05);
}

.asset-card-large:hover .asset-image {
  transform: scale(1.08) rotate(0.5deg);
  filter: brightness(1.05) contrast(1.1);
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  gap: 8px;
}

.image-placeholder i {
  font-size: 32px;
  color: var(--primary-color);
}

.image-placeholder span {
  font-size: 12px;
}

/* 状态标签 */
.status-tag {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  color: white;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 3;
}

.status-tag.on-sale {
  background: rgba(16, 185, 129, 0.9);
}

.status-tag.limited {
  background: rgba(212, 165, 116, 0.9);
}

.status-tag.sold-out {
  background: rgba(107, 114, 128, 0.9);
}

.status-tag.default {
  background: rgba(239, 68, 68, 0.9);
}

/* 收藏按钮 */
.favorite-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3;
}

.favorite-btn:hover {
  background: rgba(212, 165, 116, 0.8);
  color: white;
  transform: scale(1.1);
}

/* 右侧信息区域 - 紧凑布局 */
.asset-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0;
  justify-content: space-between;
}

/* 标题和描述 */
.asset-header {
  flex: 0 0 auto;
}

.asset-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 6px 0;
  line-height: 1.25;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.asset-card-large:hover .asset-title {
  color: var(--primary-light);
}

.asset-desc {
  font-size: 13px;
  color: var(--text-tertiary);
  line-height: 1.35;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.asset-card-large:hover .asset-desc {
  opacity: 1;
}

/* 关键词标签区域 - 微型标签 */
.keywords-section {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin: 4px 0;
  align-items: center;
}

.keyword-tag {
  padding: 2px 6px;
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(212, 165, 116, 0.25);
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  color: var(--primary-color);
  transition: all 0.25s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.keyword-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.2), transparent);
  transition: left 0.4s ease;
}

.keyword-tag:hover::before {
  left: 100%;
}

.keyword-tag:hover {
  background: rgba(212, 165, 116, 0.25);
  border-color: var(--primary-color);
  transform: translateY(-1px) scale(1.05);
  box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);
}

/* 发行方信息 - 精简设计 */
.issuer-section {
  margin: 6px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.issuer-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 6px 10px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.issuer-info:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.3), transparent);
}

.issuer-info::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent, rgba(212, 165, 116, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.issuer-info:hover::before {
  opacity: 1;
}

.issuer-info:hover {
  background: rgba(212, 165, 116, 0.08);
  border-color: rgba(212, 165, 116, 0.2);
  transform: translateX(2px);
}

.issuer-info:hover:not(:last-child)::after {
  background: linear-gradient(90deg, transparent, rgba(212, 165, 116, 0.5), transparent);
}

.issuer-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
  background: rgba(212, 165, 116, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(212, 165, 116, 0.25);
  transition: transform 0.3s ease;
}

.issuer-info:hover .issuer-avatar {
  transform: scale(1.1);
}

.issuer-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-issuer-icon {
  color: var(--primary-color);
  font-size: 12px;
}

.issuer-details {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.issuer-label {
  font-size: 9px;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.issuer-name {
  font-size: 12px;
  color: var(--primary-light);
  font-weight: 600;
}

/* 价格和时间信息 - 精简底部栏 */
.asset-footer {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 10px 0 0 0;
  border-top: 1px solid rgba(212, 165, 116, 0.1);
  margin-top: auto;
  position: relative;
  gap: 12px;
  flex-wrap: wrap;
}

.asset-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.asset-card-large:hover .asset-footer::before {
  opacity: 0.6;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.price-label {
  font-size: 9px;
  color: var(--text-muted);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.price-value {
  font-size: 16px;
  font-weight: 800;
  color: var(--primary-color);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.asset-card-large:hover .price-value {
  color: var(--primary-light);
  transform: scale(1.05);
  text-shadow: 0 2px 6px rgba(212, 165, 116, 0.4);
}

/* 库存信息样式 */
.inventory-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 70px;
}

.inventory-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, transparent, rgba(16, 185, 129, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.asset-card-large:hover .inventory-section::before {
  opacity: 1;
}

.asset-card-large:hover .inventory-section {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.15);
  transform: translateY(-1px);
}

.inventory-label {
  font-size: 9px;
  color: var(--text-muted);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.inventory-value {
  font-size: 16px;
  font-weight: 800;
  color: var(--success-color);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  line-height: 1;
  white-space: nowrap;
}

.inventory-unit {
  font-size: 10px;
  color: var(--text-muted);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

/* 低库存状态 */
.inventory-value.low-stock {
  color: var(--warning-color);
  animation: pulse-warning 2s infinite;
}

.inventory-section:has(.low-stock)::before {
  background: linear-gradient(135deg, transparent, rgba(249, 132, 74, 0.05), transparent);
}

.asset-card-large:hover .inventory-section:has(.low-stock) {
  background: rgba(249, 132, 74, 0.05);
  border-color: rgba(249, 132, 74, 0.15);
}

.inventory-section:has(.low-stock) .inventory-label {
  color: var(--warning-color);
}

/* 缺货状态 */
.inventory-value.out-of-stock {
  color: var(--error-color);
  animation: pulse-error 2s infinite;
}

.inventory-section:has(.out-of-stock)::before {
  background: linear-gradient(135deg, transparent, rgba(230, 57, 70, 0.05), transparent);
}

.asset-card-large:hover .inventory-section:has(.out-of-stock) {
  background: rgba(230, 57, 70, 0.05);
  border-color: rgba(230, 57, 70, 0.15);
}

.inventory-section:has(.out-of-stock) .inventory-label {
  color: var(--error-color);
}

.inventory-section:has(.out-of-stock) .inventory-unit {
  color: var(--error-color);
}

/* 库存状态动画 */
@keyframes pulse-warning {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  50% {
    opacity: 0.7;
    text-shadow: 0 0 8px rgba(249, 132, 74, 0.5);
  }
}

@keyframes pulse-error {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 12px rgba(230, 57, 70, 0.6);
  }
}

.asset-card-large:hover .inventory-value {
  transform: scale(1.05);
}

.time-section {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.06);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.time-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, transparent, rgba(212, 165, 116, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.asset-card-large:hover .time-section::before {
  opacity: 1;
}

.asset-card-large:hover .time-section {
  background: rgba(212, 165, 116, 0.05);
  border-color: rgba(212, 165, 116, 0.15);
  transform: translateX(-2px);
}

.time-icon {
  color: var(--text-muted);
  font-size: 10px;
  transition: color 0.3s ease;
}

.asset-card-large:hover .time-icon {
  color: var(--primary-color);
}

.time-text {
  font-size: 11px;
  color: var(--text-tertiary);
  font-weight: 600;
  transition: color 0.3s ease;
}

.asset-card-large:hover .time-text {
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
  color: var(--text-muted);
  margin-bottom: 8px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 0;
}

.empty-desc {
  font-size: 14px;
  color: var(--text-muted);
  line-height: 1.5;
  max-width: 300px;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section {
    gap: 16px;
    margin: 20px 0;
  }
  
  .filter-tabs {
    padding: 10px;
    border-radius: 10px;
  }

  .filter-tab-group {
    position: static;
    width: 100%;
  }

  .filter-tab-header {
    margin-bottom: 10px;
    padding: 0 2px;
  }
  
  .tab-label {
    font-size: 13px;
  }
  
  .filter-tab-content {
    gap: 6px;
    padding: 2px 0;
  }
  
  .tab-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 36px;
    gap: 4px;
  }
  
  .tab-count {
    padding: 1px 4px;
    font-size: 10px;
    min-width: 16px;
  }

  .more-btn {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 36px;
    gap: 4px;
  }
  
  .reset-filter-btn {
    padding: 10px 20px;
    font-size: 12px;
  }
  
  .stats-bar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .sort-options {
    justify-content: center;
  }
  
  .sort-btn {
    flex: 1;
    min-width: 0;
    justify-content: center;
  }
  
  .asset-card-large {
    flex-direction: column;
    gap: 12px;
    padding: 14px;
  }
  
  .asset-image-section {
    flex: none;
    width: 100%;
  }
  
  .image-container {
    height: 180px;
  }
  
  .asset-title {
    font-size: 16px;
  }
  
  .asset-footer {
    flex-direction: row;
    gap: 8px;
    align-items: flex-end;
    padding: 8px 0 0 0;
  }
  
  .price-section {
    flex: 1;
  }
  
  .inventory-section {
    flex: 0 0 auto;
    min-width: 65px;
    gap: 3px;
  }
  
  .time-section {
    flex: 0 0 auto;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }
  
  .asset-card-large {
    padding: 12px;
    border-radius: 14px;
    gap: 10px;
  }
  
  .image-container {
    height: 450px;
    border-radius: 10px;
  }
  
  .asset-title {
    font-size: 15px;
  }
  
  .asset-info-section {
    gap: 8px;
  }
  
  .issuer-section {
    gap: 6px;
  }
  
  .issuer-info {
    padding: 5px 8px;
    gap: 8px;
  }
  
  .issuer-avatar {
    width: 24px;
    height: 24px;
  }
  
  .issuer-name {
    font-size: 11px;
  }
  
  .issuer-label {
    font-size: 8px;
  }
  
  .filter-section {
    gap: 12px;
    margin: 16px 0;
  }
  
  .filter-tabs {
    padding: 8px;
    border-radius: 8px;
  }

  .filter-tab-group {
    position: static;
    width: 100%;
  }

  .filter-tab-header {
    margin-bottom: 8px;
    padding: 0;
  }
  
  .tab-label {
    font-size: 12px;
  }
  
  .filter-tab-content {
    gap: 4px;
    padding: 2px 0;
  }
  
  .tab-btn {
    padding: 6px 10px;
    font-size: 11px;
    min-height: 32px;
    gap: 3px;
  }
  
  .tab-count {
    padding: 1px 3px;
    font-size: 9px;
    min-width: 14px;
  }

  .more-btn {
    padding: 6px 10px;
    font-size: 11px;
    min-height: 32px;
    gap: 3px;
  }
  
  .reset-filter-btn {
    padding: 8px 16px;
    font-size: 11px;
  }
  
  .assets-container {
    gap: 12px;
  }
  
  .keywords-section {
    gap: 6px;
  }
  
  .keyword-tag {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .inventory-section {
    min-width: 55px;
    padding: 3px 6px;
    gap: 3px;
  }
  
  .inventory-label {
    font-size: 8px;
  }
  
  .inventory-value {
    font-size: 14px;
  }
  
  .inventory-unit {
    font-size: 9px;
  }
  
  .sort-btn {
    padding: 6px 8px;
    font-size: 11px;
    gap: 4px;
  }
  
  .sort-btn i {
    font-size: 9px;
  }
}

/* 页面布局 */
.main-content {
  /* 为底部固定导航预留空间，防止内容被遮挡 */
  padding: 20px 0 80px 0;
}


</style> 