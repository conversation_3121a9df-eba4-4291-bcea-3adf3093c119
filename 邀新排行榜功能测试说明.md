# 邀新排行榜功能测试说明

## 功能概述

本次新增了活动邀新排行榜功能，包括：

1. **活动详情页面**：新增"查看邀新排行榜"按钮
2. **邀新排行榜页面**：展示活动邀请排行榜，支持分页加载
3. **API接口**：新增邀新排行榜数据接口

## 功能特性

### 🎯 核心功能
- ✅ 在活动详情页面显示"查看邀新排行榜"按钮
- ✅ 点击按钮跳转到排行榜页面
- ✅ 排行榜展示用户头像、昵称、邀请数量
- ✅ 前三名特殊样式（金、银、铜牌）
- ✅ 支持上拉加载更多数据
- ✅ 显示"数据已到底"提示

### 📱 用户体验
- ✅ 响应式设计，适配移动端
- ✅ 加载状态提示
- ✅ 空状态处理
- ✅ 错误状态处理
- ✅ 流畅的滚动加载体验

### 🔧 技术实现
- ✅ TypeScript类型安全
- ✅ Vue 3 Composition API
- ✅ 分页数据管理
- ✅ 滚动事件监听
- ✅ 模拟数据降级方案

## 测试步骤

### 1. 访问活动详情页面
```
http://localhost:5173/activity/1
```

### 2. 查看新增按钮
- 在活动详情页面底部，应该能看到"查看邀新排行榜"按钮
- 按钮样式为边框样式，带有奖杯图标

### 3. 点击进入排行榜
- 点击"查看邀新排行榜"按钮
- 页面应该跳转到：`http://localhost:5173/activity/1/invite-rank`

### 4. 查看排行榜页面
- 页面顶部显示活动封面（全宽展示，与活动详情页面一致）
- 用户列表，包含：
  - 排名（前三名有特殊图标）
  - 用户头像（统一默认头像）
  - 用户昵称
  - 邀请数量（优化徽章样式）

### 5. 测试分页加载
- 滚动到页面底部
- 应该自动加载更多数据
- 加载完成后显示"数据已到底"

### 6. 测试响应式
- 在不同屏幕尺寸下测试
- 确保移动端显示正常

## API接口说明

### 邀新排行榜接口
```
GET /activity/activity/client/inviteRank/{activityId}
```

**请求参数：**
- `pageNum`: 页码（默认1）
- `pageSize`: 每页数量（默认20）

**响应数据：**
```typescript
interface InviteRankResponse {
  code: number;
  msg: string;
  rows: InviteRankItem[];
  total: number;
}

interface InviteRankItem {
  userId: number;
  nickName: string;
  avatar: string;
  inviteNum: string;
}
```

## 模拟数据

当前使用模拟数据进行测试，包含8个用户：
- 张三：10人
- 李四：8人
- 王五：6人
- 赵六：4人
- 钱七：2人
- 孙八：1人
- 周九：1人
- 吴十：1人

**注意：** 所有用户都使用统一的默认头像，便于测试默认头像显示效果。

## 文件结构

```
src/
├── api/
│   └── activity.ts          # 新增邀新排行榜API
├── views/
│   ├── ActivityDetailView.vue    # 新增排行榜按钮
│   └── InviteRankView.vue        # 新增排行榜页面（内联默认头像）
└── router/
    └── index.ts             # 新增路由配置
```

## 注意事项

1. **默认头像**：使用内联SVG默认头像，确保在所有环境下都能正常显示
2. **分页逻辑**：模拟数据支持分页，每页显示5条数据
3. **错误处理**：API失败时会显示模拟数据
4. **性能优化**：滚动事件使用防抖处理
5. **响应式设计**：适配不同屏幕尺寸，移动端优化显示

## 后续优化建议

1. **真实API集成**：连接真实的后端API
2. **缓存机制**：添加数据缓存，提升加载速度
3. **搜索功能**：支持搜索特定用户
4. **筛选功能**：按邀请数量排序
5. **分享功能**：支持分享排行榜到社交媒体 