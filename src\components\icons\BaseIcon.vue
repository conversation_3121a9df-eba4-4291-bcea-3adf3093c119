<script setup lang="ts">
import { computed } from 'vue'
const props = defineProps<{ name: string; className?: string }>()

// 动态导入本地SVG
const iconPath = computed(() => {
  // 假设所有SVG都放在src/assets/icons/下，命名如fa-fire.svg
  return new URL(`../../assets/icons/${props.name}.svg`, import.meta.url).href
})
</script>
<template>
  <span :class="['base-icon', props.className]">
    <img :src="iconPath" :alt="props.name" />
  </span>
</template>
<style scoped>
.base-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
  vertical-align: middle;
}
.base-icon img {
  width: 1em;
  height: 1em;
  display: block;
}
</style> 