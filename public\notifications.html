<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>消息通知 - 四川省数字资产发行平�?/title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: #f8f0e0;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .message-tabs {
            display: flex;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 24px;
            padding: 4px;
            margin: 16px 0;
            border: 1px solid rgba(218, 165, 32, 0.2);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .message-tabs::-webkit-scrollbar {
            display: none;
        }
        
        .message-tab {
            flex: 1;
            min-width: 80px;
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: rgba(240, 224, 208, 0.7);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 13px;
            font-weight: 500;
            white-space: nowrap;
            position: relative;
        }
        
        .message-tab.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            font-weight: 600;
        }
        
        .tab-badge {
            position: absolute;
            top: 2px;
            right: 8px;
            background: #b22222;
            color: #f8f0e0;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .message-list {
            margin: 16px 0;
        }
        
        .message-item {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
        }
        
        .message-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 18px rgba(218, 165, 32, 0.3);
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .message-item.unread {
            border-left: 4px solid #daa520;
            background: linear-gradient(145deg, #2a201e, #352520);
        }
        
        .message-item.unread::before {
            content: '';
            position: absolute;
            top: 16px;
            right: 16px;
            width: 8px;
            height: 8px;
            background: #b22222;
            border-radius: 50%;
        }
        
        .message-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .message-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
        }
        
        .icon-system {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
        }
        
        .icon-transaction {
            background: linear-gradient(135deg, #8fbc8f, #7aa86f);
            color: #f8f0e0;
        }
        
        .icon-activity {
            background: linear-gradient(135deg, #b22222, #8b1a1a);
            color: #f8f0e0;
        }
        
        .icon-promotion {
            background: linear-gradient(135deg, #cd853f, #a0651f);
            color: #f8f0e0;
        }
        
        .message-info {
            flex: 1;
            min-width: 0;
        }
        
        .message-title {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .message-content {
            font-size: 13px;
            color: rgba(240, 224, 208, 0.7);
            line-height: 1.4;
            margin-bottom: 6px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .message-time {
            font-size: 11px;
            color: rgba(240, 224, 208, 0.5);
        }
        
        .message-actions {
            position: absolute;
            top: 16px;
            right: 16px;
            display: none;
            gap: 8px;
        }
        
        .message-item:hover .message-actions {
            display: flex;
        }
        
        .action-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.3s;
            background: rgba(0, 0, 0, 0.6);
            color: #f8f0e0;
            backdrop-filter: blur(5px);
        }
        
        .action-btn:hover {
            transform: scale(1.1);
        }
        
        .action-btn.read {
            background: rgba(218, 165, 32, 0.8);
            color: #2a1616;
        }
        
        .action-btn.delete {
            background: rgba(178, 34, 34, 0.8);
            color: #f8f0e0;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: rgba(218, 165, 32, 0.5);
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #f0e0d0;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 24px;
        }
        
        .load-more {
            text-align: center;
            margin: 24px 0;
        }
        
        .batch-actions {
            position: fixed;
            bottom: 80px;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.95);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 16px;
            transform: translateY(100%);
            transition: transform 0.3s;
            z-index: 40;
            display: none;
        }
        
        .batch-actions.show {
            transform: translateY(0);
            display: block;
        }
        
        .batch-content {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .batch-info {
            flex: 1;
            font-size: 14px;
            color: #daa520;
        }
        
        .batch-buttons {
            display: flex;
            gap: 8px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .checkbox-input {
            position: absolute;
            opacity: 0;
            pointer-events: none;
        }
        
        .message-checkbox {
            position: absolute;
            top: 16px;
            left: 16px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.6);
            border: 2px solid #f8f0e0;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 3;
        }
        
        .message-checkbox.show {
            display: flex;
        }
        
        .message-checkbox.checked {
            background: #daa520;
            border-color: #daa520;
        }
        
        .message-checkbox i {
            font-size: 10px;
            color: #2a1616;
        }
        
        .message-item.selected {
            border-color: #daa520;
            box-shadow: 0 0 0 2px rgba(218, 165, 32, 0.3);
        }
        
        .message-item.selected .message-header {
            padding-left: 40px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">消息通知</h1>
                <div class="header-actions">
                    <button class="btn btn-small btn-secondary" onclick="markAllAsRead()" id="mark-all-btn">全部已读</button>
                    <button class="btn btn-small btn-secondary" onclick="toggleSelectMode()" id="select-btn">选择</button>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 消息分类标签 -->
            <section class="message-tabs">
                <button class="message-tab active" data-type="all">
                    全部
                    <span class="tab-badge" id="all-badge">5</span>
                </button>
                <button class="message-tab" data-type="system">
                    系统通知
                    <span class="tab-badge" id="system-badge">2</span>
                </button>
                <button class="message-tab" data-type="transaction">
                    交易消息
                    <span class="tab-badge" id="transaction-badge">1</span>
                </button>
                <button class="message-tab" data-type="activity">
                    活动通知
                    <span class="tab-badge" id="activity-badge">1</span>
                </button>
                <button class="message-tab" data-type="promotion">
                    推广消息
                    <span class="tab-badge" id="promotion-badge">1</span>
                </button>
            </section>

            <!-- 消息列表 -->
            <section class="message-list" id="message-list">
                <!-- 系统通知 -->
                <div class="message-item unread" data-type="system" data-id="msg001">
                    <div class="message-checkbox" onclick="toggleMessageSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="message-header">
                        <div class="message-icon icon-system">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-title">实名认证审核通过</div>
                            <div class="message-content">恭喜您！您的实名认证已审核通过，现在可以参与所有平台功能和交易活动�?/div>
                            <div class="message-time">2小时�?/div>
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn read" onclick="markAsRead('msg001')" title="标记已读">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteMessage('msg001')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- 交易消息 -->
                <div class="message-item unread" data-type="transaction" data-id="msg002">
                    <div class="message-checkbox" onclick="toggleMessageSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="message-header">
                        <div class="message-icon icon-transaction">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-title">购买成功通知</div>
                            <div class="message-content">您成功购买了"金沙太阳神鸟金饰数字藏品"，支付金额￥299.00，数字藏品已发放至您的账户�?/div>
                            <div class="message-time">5小时�?/div>
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn read" onclick="markAsRead('msg002')" title="标记已读">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteMessage('msg002')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- 活动通知 -->
                <div class="message-item unread" data-type="activity" data-id="msg003">
                    <div class="message-checkbox" onclick="toggleMessageSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="message-header">
                        <div class="message-icon icon-activity">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-title">新用户专享福�?/div>
                            <div class="message-content">欢迎加入四川数字资产平台！新用户专享：首�?折优惠，免费获得价�?0元平台积分�?/div>
                            <div class="message-time">1天前</div>
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn read" onclick="markAsRead('msg003')" title="标记已读">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteMessage('msg003')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- 推广消息 -->
                <div class="message-item unread" data-type="promotion" data-id="msg004">
                    <div class="message-checkbox" onclick="toggleMessageSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="message-header">
                        <div class="message-icon icon-promotion">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-title">三星堆新品限时预�?/div>
                            <div class="message-content">三星堆博物馆联合发布限量数字藏品，现已开启预售！限量500份，预售价格�?折优惠�?/div>
                            <div class="message-time">2天前</div>
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn read" onclick="markAsRead('msg004')" title="标记已读">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteMessage('msg004')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>

                <!-- 系统通知 -->
                <div class="message-item unread" data-type="system" data-id="msg005">
                    <div class="message-checkbox" onclick="toggleMessageSelection(this)">
                        <i class="fas fa-check"></i>
                    </div>
                    <div class="message-header">
                        <div class="message-icon icon-system">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="message-info">
                            <div class="message-title">系统维护通知</div>
                            <div class="message-content">系统将于今晚23:00-次日02:00进行维护升级，期间部分功能可能受影响，请您提前安排�?/div>
                            <div class="message-time">3天前</div>
                        </div>
                    </div>
                    <div class="message-actions">
                        <button class="action-btn read" onclick="markAsRead('msg005')" title="标记已读">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="action-btn delete" onclick="deleteMessage('msg005')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </section>

            <!-- 空状�?-->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-inbox"></i>
                </div>
                <div class="empty-title">暂无消息</div>
                <div class="empty-description">
                    您当前没有任何消息通知<br>
                    我们会及时为您推送重要信�?
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" id="load-more">
                <button class="btn btn-secondary">
                    <i class="fas fa-plus"></i> 加载更多消息
                </button>
            </div>
        </div>
    </main>

    <!-- 批量操作�?-->
    <div class="batch-actions" id="batch-actions">
        <div class="container">
            <div class="batch-content">
                <div class="batch-info">
                    已选择 <span id="selected-count">0</span> 条消�?
                </div>
                <div class="batch-buttons">
                    <button class="btn btn-small btn-secondary" onclick="cancelSelection()">取消</button>
                    <button class="btn btn-small btn-primary" onclick="batchMarkAsRead()">标记已读</button>
                    <button class="btn btn-small btn-secondary" onclick="batchDelete()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        let isSelectMode = false;
        let selectedMessages = new Set();

        // 消息分类切换
        document.querySelectorAll('.message-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.message-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const type = this.getAttribute('data-type');
                filterMessagesByType(type);
            });
        });

        function filterMessagesByType(type) {
            const messages = document.querySelectorAll('.message-item');
            let visibleCount = 0;
            
            messages.forEach(message => {
                const messageType = message.getAttribute('data-type');
                
                if (type === 'all' || messageType === type) {
                    message.style.display = 'block';
                    visibleCount++;
                } else {
                    message.style.display = 'none';
                }
            });
            
            // 显示空状�?
            const emptyState = document.getElementById('empty-state');
            const messageList = document.getElementById('message-list');
            const loadMore = document.getElementById('load-more');
            
            if (visibleCount === 0) {
                messageList.style.display = 'none';
                loadMore.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                messageList.style.display = 'block';
                loadMore.style.display = 'block';
                emptyState.style.display = 'none';
            }
        }

        // 选择模式
        function toggleSelectMode() {
            isSelectMode = !isSelectMode;
            const selectBtn = document.getElementById('select-btn');
            const checkboxes = document.querySelectorAll('.message-checkbox');
            
            if (isSelectMode) {
                selectBtn.textContent = '取消';
                selectBtn.classList.remove('btn-secondary');
                selectBtn.classList.add('btn-primary');
                checkboxes.forEach(cb => cb.classList.add('show'));
                document.querySelectorAll('.message-item').forEach(item => {
                    item.querySelector('.message-header').style.paddingLeft = '40px';
                });
            } else {
                selectBtn.textContent = '选择';
                selectBtn.classList.remove('btn-primary');
                selectBtn.classList.add('btn-secondary');
                checkboxes.forEach(cb => {
                    cb.classList.remove('show', 'checked');
                });
                selectedMessages.clear();
                document.querySelectorAll('.message-item').forEach(item => {
                    item.classList.remove('selected');
                    item.querySelector('.message-header').style.paddingLeft = '';
                });
                hideBatchActions();
            }
        }

        // 切换消息选择状�?
        function toggleMessageSelection(checkbox) {
            if (!isSelectMode) return;
            
            const messageItem = checkbox.closest('.message-item');
            const messageId = messageItem.getAttribute('data-id');
            
            if (selectedMessages.has(messageId)) {
                selectedMessages.delete(messageId);
                checkbox.classList.remove('checked');
                messageItem.classList.remove('selected');
            } else {
                selectedMessages.add(messageId);
                checkbox.classList.add('checked');
                messageItem.classList.add('selected');
            }
            
            updateBatchActions();
        }

        // 更新批量操作
        function updateBatchActions() {
            const batchActions = document.getElementById('batch-actions');
            const selectedCount = document.getElementById('selected-count');
            
            selectedCount.textContent = selectedMessages.size;
            
            if (selectedMessages.size > 0) {
                batchActions.classList.add('show');
            } else {
                batchActions.classList.remove('show');
            }
        }

        function hideBatchActions() {
            document.getElementById('batch-actions').classList.remove('show');
        }

        // 取消选择
        function cancelSelection() {
            toggleSelectMode();
        }

        // 批量标记已读
        function batchMarkAsRead() {
            if (selectedMessages.size === 0) return;
            
            selectedMessages.forEach(messageId => {
                markAsRead(messageId, false);
            });
            
            selectedMessages.clear();
            hideBatchActions();
            updateUnreadCounts();
            alert(`已标�?{selectedMessages.size}条消息为已读`);
        }

        // 批量删除
        function batchDelete() {
            if (selectedMessages.size === 0) return;
            
            if (confirm(`确定要删除选中�?{selectedMessages.size}条消息吗？`)) {
                selectedMessages.forEach(messageId => {
                    deleteMessage(messageId, false);
                });
                
                selectedMessages.clear();
                hideBatchActions();
                updateUnreadCounts();
                alert('已删除选中的消�?);
            }
        }

        // 单条消息操作
        function markAsRead(messageId, showAlert = true) {
            const messageElement = document.querySelector(`[data-id="${messageId}"]`);
            if (messageElement) {
                messageElement.classList.remove('unread');
                messageElement.querySelector('.message-actions .read').style.display = 'none';
                
                if (showAlert) {
                    updateUnreadCounts();
                    alert('已标记为已读');
                }
            }
        }

        function deleteMessage(messageId, showAlert = true) {
            const messageElement = document.querySelector(`[data-id="${messageId}"]`);
            if (messageElement) {
                messageElement.remove();
                
                if (showAlert) {
                    updateUnreadCounts();
                    alert('消息已删�?);
                    
                    // 检查是否需要显示空状�?
                    const visibleMessages = document.querySelectorAll('.message-item[style*="block"], .message-item:not([style])');
                    if (visibleMessages.length === 0) {
                        const activeTab = document.querySelector('.message-tab.active');
                        filterMessagesByType(activeTab.getAttribute('data-type'));
                    }
                }
            }
        }

        // 全部标记已读
        function markAllAsRead() {
            const unreadMessages = document.querySelectorAll('.message-item.unread');
            
            if (unreadMessages.length === 0) {
                alert('暂无未读消息');
                return;
            }
            
            if (confirm(`确定要将所�?{unreadMessages.length}条未读消息标记为已读吗？`)) {
                unreadMessages.forEach(message => {
                    message.classList.remove('unread');
                });
                
                updateUnreadCounts();
                alert('所有消息已标记为已�?);
            }
        }

        // 更新未读数量
        function updateUnreadCounts() {
            const allUnread = document.querySelectorAll('.message-item.unread').length;
            const systemUnread = document.querySelectorAll('.message-item.unread[data-type="system"]').length;
            const transactionUnread = document.querySelectorAll('.message-item.unread[data-type="transaction"]').length;
            const activityUnread = document.querySelectorAll('.message-item.unread[data-type="activity"]').length;
            const promotionUnread = document.querySelectorAll('.message-item.unread[data-type="promotion"]').length;
            
            updateBadge('all-badge', allUnread);
            updateBadge('system-badge', systemUnread);
            updateBadge('transaction-badge', transactionUnread);
            updateBadge('activity-badge', activityUnread);
            updateBadge('promotion-badge', promotionUnread);
        }

        function updateBadge(badgeId, count) {
            const badge = document.getElementById(badgeId);
            if (count > 0) {
                badge.textContent = count;
                badge.style.display = 'flex';
            } else {
                badge.style.display = 'none';
            }
        }

        // 消息点击
        document.querySelectorAll('.message-item').forEach(item => {
            item.addEventListener('click', function(e) {
                // 如果点击的是操作按钮或复选框，不展开消息
                if (e.target.closest('.message-actions') || e.target.closest('.message-checkbox')) {
                    e.stopPropagation();
                    return;
                }
                
                // 在选择模式下，点击消息等同于点击复选框
                if (isSelectMode) {
                    const checkbox = this.querySelector('.message-checkbox');
                    toggleMessageSelection(checkbox);
                } else {
                    const messageId = this.getAttribute('data-id');
                    const title = this.querySelector('.message-title').textContent;
                    alert(`查看消息详情�?{title}`);
                    
                    // 自动标记为已�?
                    if (this.classList.contains('unread')) {
                        markAsRead(messageId, false);
                        updateUnreadCounts();
                    }
                }
            });
        });

        // 加载更多
        document.querySelector('.load-more button').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载�?..';
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-plus"></i> 加载更多消息';
                alert('已加载更多消�?);
            }, 1500);
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .message-tab, .message-item').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });

        // 初始化未读数�?
        updateUnreadCounts();
    </script>
</body>
</html> 
