<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>数字资产市场 - 凌云数资</title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sichuan-red': '#d4313b',
                        'sichuan-gold': '#daa520',
                        'sichuan-dark': '#1a1a1a',
                        'sichuan-gray': '#2a2a2a'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            
            /* 主色系 - 基于太阳神鸟的金色演化 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 深邃科技蓝 */
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            /* 背景色系 - 现代深色调 */
            --bg-primary: #0F0F23;             
            --bg-secondary: #1A1B3A;           
            --bg-tertiary: #252759;            
            --bg-glass: rgba(37, 39, 89, 0.8); 
            --bg-card: rgba(37, 39, 89, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 27, 58, 0.9) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-blue: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: var(--gradient-hero);
            color: var(--neutral-100);
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--bg-primary);
            min-height: 100vh;
            position: relative;
        }

        /* 顶部导航栏 */
        .top-nav {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }

        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .nav-title {
            font-size: 20px;
            font-weight: 700;
            color: var(--primary-gold);
        }

        .nav-btn {
            background: none;
            border: none;
            color: var(--neutral-100);
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            color: var(--primary-gold-light);
            background: var(--primary-gold-alpha);
        }

        /* 搜索栏 */
        .search-container {
            padding: 16px 20px;
            background: var(--gradient-card);
            backdrop-filter: blur(10px);
        }

        .search-bar {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: 24px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-sm);
        }

        .search-dropdown {
            background: none;
            border: none;
            color: var(--neutral-100);
            padding: 12px 16px;
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            outline: none;
            font-size: 14px;
            cursor: pointer;
        }

        .search-input {
            flex: 1;
            background: none;
            border: none;
            color: var(--neutral-100);
            padding: 12px 16px;
            outline: none;
            font-size: 14px;
        }

        .search-input::placeholder {
            color: var(--neutral-400);
        }

        .search-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .search-btn:hover {
            background: var(--gradient-primary);
            box-shadow: var(--shadow-gold);
        }

        /* 分类标签 */
        .category-tabs {
            display: flex;
            gap: 8px;
            padding: 16px 20px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .category-tabs::-webkit-scrollbar {
            display: none;
        }

        .category-tab {
            background: var(--bg-tertiary);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .category-tab.active, .category-tab:hover {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-gold);
            box-shadow: var(--shadow-gold);
        }

        /* 内容区域 */
        .content {
            padding: 0 20px 100px;
        }

        /* 区域标题 */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-gold-light);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-title i {
            color: var(--primary-gold);
        }

        .section-more {
            color: var(--primary-gold);
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .section-more:hover {
            color: var(--primary-gold-light);
        }

        /* 促销横幅 */
        .promo-banner {
            background: var(--gradient-secondary);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-blue);
        }

        .promo-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20px;
            width: 100px;
            height: 200%;
            background: rgba(255, 255, 255, 0.1);
            transform: rotate(12deg);
        }

        .promo-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .promo-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 产品网格 */
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 32px;
        }

        .product-card {
            background: var(--gradient-card);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-gold-alpha);
        }

        .product-image {
            width: 100%;
            height: 140px;
            position: relative;
            overflow: hidden;
                            background: linear-gradient(135deg, var(--primary-gold-alpha), var(--secondary-purple-alpha));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-gold);
            font-size: 36px;
        }

        .product-badge {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--secondary-blue);
            color: #fff;
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            z-index: 2;
            box-shadow: var(--shadow-sm);
        }

        .product-info {
            padding: 12px;
        }

        .product-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 6px;
            color: var(--neutral-100);
            line-height: 1.3;
        }

        .product-price {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-gold);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .product-status {
            font-size: 11px;
            color: var(--neutral-400);
            background: var(--primary-gold-alpha);
            padding: 2px 6px;
            border-radius: 8px;
        }

        /* 热门推荐横向滚动 */
        .featured-section {
            margin-bottom: 32px;
        }

        .featured-carousel {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding-bottom: 10px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .featured-carousel::-webkit-scrollbar {
            display: none;
        }

        .featured-card {
            flex-shrink: 0;
            width: 260px;
            background: var(--gradient-card);
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
        }

        .featured-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .featured-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, var(--primary-gold-alpha), var(--secondary-blue-alpha));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: var(--primary-gold);
            position: relative;
        }

        .featured-content {
            padding: 16px;
        }

        .featured-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 8px;
        }

        .featured-desc {
            font-size: 12px;
            color: var(--neutral-400);
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .featured-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .featured-price {
            font-size: 18px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .featured-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: var(--accent-green);
            color: white;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: var(--gradient-card);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 8px 0 28px;
            z-index: 50;
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: var(--neutral-400);
            transition: color 0.3s;
            padding: 8px 4px;
        }

        .nav-item.active {
            color: var(--primary-gold);
        }

        .nav-item:hover {
            color: var(--primary-gold-light);
        }

        .nav-item i {
            font-size: 18px;
            margin-bottom: 4px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .container {
                max-width: 100%;
            }
            
            .content {
                padding: 0 16px 100px;
            }
            
            .search-container {
                padding: 16px;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .product-card, .featured-card {
            animation: fadeIn 0.6s ease forwards;
        }

        .product-card:nth-child(odd) { animation-delay: 0.1s; }
        .product-card:nth-child(even) { animation-delay: 0.2s; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="nav-content">
                <button class="nav-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="nav-title">数字资产市场</h1>
                <button class="nav-btn">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="search-container">
            <div class="search-bar">
                <select class="search-dropdown">
                    <option>全部</option>
                    <option>古蜀文化</option>
                    <option>三星堆</option>
                    <option>金沙</option>
                </select>
                <input type="text" class="search-input" placeholder="搜索数字资产...">
                <button class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- 分类标签 -->
        <div class="category-tabs">
            <a href="#" class="category-tab active">全部</a>
            <a href="#" class="category-tab">古蜀文化</a>
            <a href="#" class="category-tab">三星堆</a>
            <a href="#" class="category-tab">金沙遗址</a>
            <a href="#" class="category-tab">熊猫文化</a>
            <a href="#" class="category-tab">川剧脸谱</a>
            <a href="#" class="category-tab">蜀锦</a>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 促销横幅 -->
            <div class="promo-banner">
                <div class="promo-title">🎉 新春特惠</div>
                <div class="promo-desc">精选古蜀文化数字资产，限时优惠50%起</div>
            </div>

            <!-- 热门推荐 -->
            <div class="featured-section">
                <div class="section-header">
                    <h2 class="section-title">
                        <i class="fas fa-fire"></i>
                        热门推荐
                    </h2>
                    <a href="#" class="section-more">
                        查看全部 <i class="fas fa-chevron-right"></i>
                    </a>
                </div>
                <div class="featured-carousel">
                    <div class="featured-card">
                        <div class="featured-image">
                            <i class="fas fa-mask"></i>
                        </div>
                        <div class="featured-content">
                            <div class="featured-title">三星堆青铜面具</div>
                            <div class="featured-desc">古蜀文明的神秘象征，数字化复原的青铜面具艺术品</div>
                            <div class="featured-footer">
                                <div class="featured-price">¥2,880</div>
                                <div class="featured-status">热销</div>
                            </div>
                        </div>
                    </div>

                    <div class="featured-card">
                        <div class="featured-image">
                            <i class="fas fa-sun"></i>
                        </div>
                        <div class="featured-content">
                            <div class="featured-title">太阳神鸟金饰</div>
                            <div class="featured-desc">成都城市标志，四川文化的瑰宝象征</div>
                            <div class="featured-footer">
                                <div class="featured-price">¥1,688</div>
                                <div class="featured-status">可购</div>
                            </div>
                        </div>
                    </div>

                    <div class="featured-card">
                        <div class="featured-image">
                            <i class="fas fa-gem"></i>
                        </div>
                        <div class="featured-content">
                            <div class="featured-title">金沙遗址文物</div>
                            <div class="featured-desc">古代四川王国的珍贵文物数字化收藏</div>
                            <div class="featured-footer">
                                <div class="featured-price">¥3,200</div>
                                <div class="featured-status">稀有</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最新发布 -->
            <div class="section-header">
                <h2 class="section-title">
                    <i class="fas fa-clock"></i>
                    最新发布
                </h2>
                <a href="#" class="section-more">
                    查看全部 <i class="fas fa-chevron-right"></i>
                </a>
            </div>

            <div class="product-grid">
                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-theater-masks"></i>
                        <div class="product-badge">新品</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">川剧变脸面具</div>
                        <div class="product-price">
                            ¥1,288
                            <span class="product-status">可购</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-mountain"></i>
                        <div class="product-badge">热门</div>
                    </div>
                    <div class="product-info">
                        <div class="product-title">峨眉山金顶</div>
                        <div class="product-price">
                            ¥2,588
                            <span class="product-status">限量</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-title">蜀锦工艺品</div>
                        <div class="product-price">
                            ¥988
                            <span class="product-status">可购</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-seedling"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-title">青城山茶艺</div>
                        <div class="product-price">
                            ¥688
                            <span class="product-status">可购</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-fire"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-title">都江堰水利</div>
                        <div class="product-price">
                            ¥1,888
                            <span class="product-status">稀有</span>
                        </div>
                    </div>
                </div>

                <div class="product-card">
                    <div class="product-image">
                        <i class="fas fa-dragon"></i>
                    </div>
                    <div class="product-info">
                        <div class="product-title">古蜀青铜器</div>
                        <div class="product-price">
                            ¥3,888
                            <span class="product-status">珍藏</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>首页</span>
                </a>
                <a href="#" class="nav-item active">
                    <i class="fas fa-store"></i>
                    <span>市场</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-plus-circle"></i>
                    <span>发布</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-heart"></i>
                    <span>收藏</span>
                </a>
                <a href="#" class="nav-item">
                    <i class="fas fa-user-circle"></i>
                    <span>我的</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            // 这里可以添加搜索逻辑
            console.log('搜索:', searchTerm);
        });

        // 分类标签切换
        document.querySelectorAll('.category-tab').forEach(tab => {
            tab.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有active类
                document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                
                // 为当前标签添加active类
                this.classList.add('active');
                
                // 这里可以添加分类筛选逻辑
                console.log('切换分类:', this.textContent);
            });
        });

        // 产品卡片点击效果
        document.querySelectorAll('.product-card, .featured-card').forEach(card => {
            card.addEventListener('click', function() {
                // 这里可以添加产品详情页面跳转逻辑
                console.log('点击产品:', this.querySelector('.product-title, .featured-title').textContent);
            });
        });

        // 轮播图自动滚动
        const carousel = document.querySelector('.featured-carousel');
        let scrollAmount = 0;
        const scrollSpeed = 1;
        const maxScroll = carousel.scrollWidth - carousel.clientWidth;

        function autoScroll() {
            if (scrollAmount >= maxScroll) {
                scrollAmount = 0;
            } else {
                scrollAmount += scrollSpeed;
            }
            carousel.scrollLeft = scrollAmount;
        }

        // 每8秒自动滚动一次
        setInterval(autoScroll, 8000);

        // 添加触摸反馈
        document.querySelectorAll('.product-card, .nav-item, .category-tab').forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
            });
            
            element.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    </script>
</body>
</html> 
