<template>
  <AppPageHeader :title="'重置密码'" @back="$router.back()" />
  <div class="auth-page" :style="{ paddingTop: '40px' }">
    <div class="auth-container">

      <!-- Logo区域 -->
      <div class="logo-section">
        <!-- <div class="welcome-text">凌云数资</div>
        <div class="subtitle">重置您的账户密码</div> -->
      </div>
      <!-- 重置密码表单 -->
      <div class="auth-form">
        <form @submit.prevent="handleSubmit">
          <div class="form-group">
            <label class="form-label"><i class="fas fa-mobile"></i> 手机号</label>
            <div class="input-wrapper">
              <input v-model="phone" type="tel" maxlength="11" class="form-input" placeholder="请输入手机号" required />
            </div>
          </div>
          <div class="form-group">
            <label class="form-label"><i class="fas fa-shield"></i> 验证码</label>
            <div class="sms-container">
              <input v-model="code" type="text" maxlength="6" class="form-input" placeholder="请输入验证码" required />
              <button type="button" class="sms-btn" :disabled="sendingCode || !isPhoneValid" @click="sendCode">
                {{ sendingCode ? `${countdown}s后重发` : '发送验证码' }}
              </button>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label"><i class="fas fa-lock"></i> 新密码</label>
            <div class="input-wrapper password-input-wrapper">
              <input
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                maxlength="12"
                class="form-input password-input"
                placeholder="8-12位数字+大小写字母"
                required
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePassword"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
          </div>
          <div class="form-group">
            <label class="form-label"><i class="fas fa-lock"></i> 确认密码</label>
            <div class="input-wrapper password-input-wrapper">
              <input
                v-model="confirmPassword"
                :type="showConfirmPassword ? 'text' : 'password'"
                maxlength="12"
                class="form-input password-input"
                placeholder="请再次输入新密码"
                required
              />
              <button
                type="button"
                class="password-toggle"
                @click="toggleConfirmPassword"
              >
                <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
          </div>
          <button class="btn btn-primary" type="submit" :disabled="submitting">提交</button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import AppPageHeader from '@/components/AppPageHeader.vue'
import { validatePhoneNumber as validatePhone, getVirtualOperatorInfo } from '@/utils/phoneValidator'

const router = useRouter()
const { success, error } = useNotification()

const phone = ref('')
const code = ref('')
const password = ref('')
const confirmPassword = ref('')
const sendingCode = ref(false)
const countdown = ref(0)
const submitting = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
let timer: any = null

const isPhoneValid = computed(() => {
  const result = validatePhone(phone.value, false) // 不允许虚拟运营商
  return result.isValid
})

const goBack = () => router.back()

/**
 * 切换密码显示状态
 */
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

/**
 * 切换确认密码显示状态
 */
const toggleConfirmPassword = () => {
  showConfirmPassword.value = !showConfirmPassword.value
}

const sendCode = async () => {
  // 验证手机号（包含虚拟运营商检测）
  const result = validatePhone(phone.value, false) // 不允许虚拟运营商
  if (!result.isValid) {
    // 如果是虚拟运营商，显示详细提示
    if (result.virtualInfo?.isVirtual) {
      const detailInfo = getVirtualOperatorInfo(phone.value)
      error(`${result.error}\n\n${detailInfo}`, 8000)
    } else {
      error(result.error || '请输入正确的手机号')
    }
    return
  }
  sendingCode.value = true
  try {
    await AuthAPI.sendSmsCode({ phone: phone.value, scene: 'H5resetPassword' })
    success('验证码已发送')
    countdown.value = 60
    timer = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(timer)
        sendingCode.value = false
      }
    }, 1000)
  } catch (err) {
    error('验证码发送失败')
    sendingCode.value = false
  }
}

const validatePassword = (pwd: string) => {
  // 8-12位，包含数字、大小写字母
  if (pwd.length < 8 || pwd.length > 12) {
    return false
  }
  //包含数字
  if (!/\d/.test(pwd)) {
    return false
  }
  //包含大小写字母
  if (!/[a-z]/.test(pwd) || !/[A-Z]/.test(pwd)) {
    return false
  }

  return true
}

const handleSubmit = async () => {
  // 验证手机号（包含虚拟运营商检测）
  const result = validatePhone(phone.value, false) // 不允许虚拟运营商
  if (!result.isValid) {
    error(result.error || '请输入正确的手机号')
    return
  }
  if (!code.value) {
    error('请输入验证码')
    return
  }
  if (!validatePassword(password.value)) {
    error('密码需8-12位，包含数字、大小写字母')
    return
  }
  if (password.value !== confirmPassword.value) {
    error('两次输入的密码不一致')
    return
  }
  submitting.value = true
  try {
    await AuthAPI.updatePwd({ phone: phone.value, smsCode: code.value, password: password.value })
    success('密码重置成功，请重新登录')
    setTimeout(() => {
      router.push('/login')
    }, 1200)
  } catch (err) {
    error('重置密码失败')
  } finally {
    submitting.value = false
  }
}

// 生命周期钩子
onMounted(() => {
  // 自动填入缓存中的手机号
  const localUserInfo = AuthAPI.getLocalUserInfo()
  if (localUserInfo && localUserInfo.phonenumber) {
    phone.value = localUserInfo.phonenumber
  }
})
</script>

<style scoped>
/* CSS变量定义 */
.auth-page {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --neutral-100: #f8f6f0;
  --neutral-200: #e0ddd4;
  --neutral-300: #c4c0b1;
  --neutral-400: #a39d8e;

  min-height: 100vh;
  background: linear-gradient(135deg, #232323 0%, #181818 100%);
  color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.auth-container {
  width: 100%;
  max-width: 420px;
  margin: 0 auto;
  padding: 0 12px 32px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.auth-header {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 32px;
  margin-bottom: 12px;
}
.back-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255,255,255,0.08);
  border: none;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: background 0.2s;
}
.back-btn:hover {
  background: #d4a574;
  color: #232323;
}
.logo-section {
  width: 100%;
  text-align: center;
  margin-bottom: 18px;
}
.welcome-text {
  font-size: 24px;
  font-weight: 700;
  color: #d4a574;
  margin-bottom: 4px;
}
.subtitle {
  font-size: 14px;
  color: #e0ddd4;
  margin-bottom: 2px;
}
.auth-form {
  width: 100%;
  background: rgba(42,42,42,0.95);
  border-radius: 18px;
  box-shadow: 0 4px 18px rgba(212,165,116,0.08);
  padding: 32px 20px 24px 20px;
  display: flex;
  flex-direction: column;
  gap: 22px;
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 10px;
}
.form-label {
  font-size: 14px;
  color: #e0ddd4;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
}
.input-wrapper {
  position: relative;
  width: 100%;
}
.form-input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid #393939;
  background: #181818;
  color: #fff;
  font-size: 15px;
  outline: none;
  transition: border 0.2s;
}

/* 密码输入框特殊样式 */
.password-input {
  padding-right: 40px;
}
.form-input:focus {
  border-color: #d4a574;
}

.password-toggle {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(212, 165, 116, 0.15);
  border: 1px solid rgba(212, 165, 116, 0.3);
  color: var(--primary-color);
  cursor: pointer;
  font-size: 14px;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.3s ease;
  z-index: 100;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  opacity: 1;
  visibility: visible;
}

.password-toggle:hover {
  background: rgba(212, 165, 116, 0.25);
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-50%) scale(1.05);
}

.password-toggle:focus {
  outline: none;
  background: rgba(212, 165, 116, 0.3);
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.password-toggle:active {
  background: rgba(212, 165, 116, 0.4);
  transform: translateY(-50%) scale(0.95);
}

.sms-container {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}
.sms-btn {
  padding: 6px 14px;
  border-radius: 8px;
  background: #d4a574;
  color: #232323;
  border: none;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s;
}
.sms-btn:disabled {
  background: #aaa;
  color: #fff;
  cursor: not-allowed;
}
.btn.btn-primary {
  margin-top: 10px;
  padding: 12px 0;
  border-radius: 8px;
  background: #d4a574;
  color: #232323;
  border: none;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: background 0.2s;
  width: 100%;
}
.btn.btn-primary:disabled {
  background: #aaa;
  color: #fff;
  cursor: not-allowed;
}
@media (max-width: 600px) {
  .auth-container {
    padding: 0 4px 18px 4px;
  }
  .auth-form {
    padding: 18px 8px 14px 8px;
    border-radius: 12px;
  }

  .password-toggle {
    padding: 6px;
    min-width: 28px;
    min-height: 28px;
    font-size: 16px;
  }

  .password-input {
    padding-right: 44px;
  }
}
</style>
