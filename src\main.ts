import './assets/main.css'
import './assets/styles/design-system.css'
// 直接引入Font Awesome核心样式，提升加载性能
import '@fortawesome/fontawesome-free/css/all.min.css'
import './assets/styles/fontawesome-fix.css'

import { createApp } from 'vue'
import App from './App.vue'

import { createPinia } from 'pinia'

import router from './router'

// 防止AudioContext错误（如果有第三方库或浏览器扩展尝试使用音频）
if (typeof window !== 'undefined') {
  // 创建一个静默的AudioContext处理器
  let userInteracted = false

  const handleUserInteraction = () => {
    userInteracted = true
    document.removeEventListener('click', handleUserInteraction)
    document.removeEventListener('touchstart', handleUserInteraction)
  }

  document.addEventListener('click', handleUserInteraction)
  document.addEventListener('touchstart', handleUserInteraction)

  // 如果有代码尝试创建AudioContext，确保在用户交互后
  const originalAudioContext = window.AudioContext || (window as any).webkitAudioContext
  if (originalAudioContext) {
    const CompatibleAudioContext = function(this: any, ...args: any[]) {
      if (!userInteracted) {
        console.warn('AudioContext requires user interaction. Delaying creation.')
        return new Proxy({}, {
          get() {
            return () => {}
          }
        })
      }
      return new originalAudioContext(...args)
    }
    CompatibleAudioContext.prototype = originalAudioContext.prototype
    window.AudioContext = CompatibleAudioContext as any
  }
}

const app = createApp(App)

app.use(createPinia())
app.use(router)

app.mount('#app')
