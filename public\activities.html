<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>活动列表 - 凌云数资</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            
            /* 主色系 - 基于太阳神鸟的金色演化 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 深邃科技蓝 */
            --secondary-blue: #1E3A8A;         
            --secondary-blue-light: #3B82F6;   
            --secondary-blue-dark: #1E40AF;    
            --secondary-blue-alpha: rgba(30, 58, 138, 0.1);
            
            /* 背景色系 - 现代深色调 */
            --bg-primary: #0F0F23;             
            --bg-secondary: #1A1B3A;           
            --bg-tertiary: #252759;            
            --bg-glass: rgba(37, 39, 89, 0.8); 
            --bg-card: rgba(37, 39, 89, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-blue) 0%, var(--secondary-blue-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 27, 58, 0.9) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-blue: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: var(--neutral-100);
        }
        
        .text-primary {
            color: var(--primary-gold);
        }
        
        .search-section {
            padding: 16px 0;
        }
        
        .search-container {
            position: relative;
            margin-bottom: 16px;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 20px 12px 44px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            background: var(--bg-tertiary);
            color: var(--neutral-100);
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            background: var(--bg-secondary);
        }
        
        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-gold);
            font-size: 16px;
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 8px;
        }
        
        .filter-tab {
            background: var(--bg-tertiary);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--neutral-200);
            padding: 8px 16px;
            border-radius: 16px;
            white-space: nowrap;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-tab.active {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-gold);
        }
        
        .activity-card {
            background: var(--gradient-card);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
        }
        
        .activity-card:hover {
            transform: translateY(-2px);
            border-color: var(--primary-gold-alpha);
        }
        
        .activity-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: 8px;
        }
        
        .activity-desc {
            font-size: 13px;
            color: var(--neutral-400);
            margin-bottom: 12px;
        }
        
        .activity-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-ongoing {
            background: var(--secondary-blue-alpha);
            color: var(--secondary-blue-light);
        }
        
        .status-upcoming {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
        }
        
        .status-ended {
            background: rgba(148, 163, 184, 0.2);
            color: var(--neutral-400);
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: var(--neutral-400);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">活动列表</h1>
                <div></div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <section class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索活动名称或关键词">
                    <i class="fas fa-search search-icon"></i>
                </div>
                
                <div class="filter-tabs">
                    <div class="filter-tab active">全部</div>
                    <div class="filter-tab">进行中</div>
                    <div class="filter-tab">即将开始</div>
                    <div class="filter-tab">已结束</div>
                    <div class="filter-tab">文化展览</div>
                    <div class="filter-tab">收藏活动</div>
                </div>
            </section>

            <section class="activities-list">
                <div class="activity-card">
                    <div class="activity-title">三星堆数字藏品展</div>
                    <div class="activity-desc">展示三星堆、金沙遗址等古蜀文明文物的数字藏品，感受千年文化的现代魅力</div>
                    <div class="activity-status status-ongoing">进行中</div>
                </div>
                
                <div class="activity-card">
                    <div class="activity-title">蜀锦织品藏品拍卖</div>
                    <div class="activity-desc">汇集传统蜀锦织品大师作品，展现千年织造工艺文化内涵</div>
                    <div class="activity-status status-upcoming">即将开始</div>
                </div>
                
                <div class="activity-card">
                    <div class="activity-title">新春数字藏品节</div>
                    <div class="activity-desc">庆祝新春佳节，限时推出古蜀文明数字藏品</div>
                    <div class="activity-status status-ongoing">进行中</div>
                </div>
                
                <div class="activity-card">
                    <div class="activity-title">金沙印象文化专场</div>
                    <div class="activity-desc">探索金沙遗址的印象文化，领略历史沉淀，展示不同时期印象器物的独特魅力</div>
                    <div class="activity-status status-ended">已结束</div>
                </div>
                
                <div class="activity-card">
                    <div class="activity-title">青铜器收藏家聚会</div>
                    <div class="activity-desc">青铜器收藏爱好者分享收藏经验，交流青铜器文化知识的聚会活动</div>
                    <div class="activity-status status-upcoming">即将开始</div>
                </div>
            </section>
        </div>
    </main>

    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 筛选标签点击事件
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                alert('筛选功能：' + this.textContent);
            });
        });

        // 活动卡片点击事件
        document.querySelectorAll('.activity-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.activity-title').textContent;
                alert('查看活动：' + title);
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const activityCards = document.querySelectorAll('.activity-card');
            
            activityCards.forEach(card => {
                const title = card.querySelector('.activity-title').textContent.toLowerCase();
                const desc = card.querySelector('.activity-desc').textContent.toLowerCase();
                
                if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
