<template>
  <AppPageHeader :title="'专区详情'" @back="$router.back()" />
  <div class="zone-detail-container">


    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <i class="fas fa-spinner fa-spin"></i>
      <p>正在加载专区详情...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <i class="fas fa-exclamation-triangle"></i>
      <p>{{ error }}</p>
      <button class="retry-btn" @click="fetchData">重试</button>
    </div>

    <!-- 专区详情内容 -->
    <div v-else-if="zoneDetail" class="zone-content">
      <!-- 专区封面区域 -->
      <section class="zone-cover-section">
        <div class="cover-image" v-if="zoneDetail.zoneCover || zoneDetail.zoneImage">
          <img 
            :src="zoneDetail.zoneCover || zoneDetail.zoneImage" 
            :alt="zoneDetail.zoneName"
            class="cover-img"
          >
        </div>
        <div v-else class="cover-placeholder">
          <i class="fas fa-map-marked-alt"></i>
          <p>暂无封面图</p>
        </div>
        <div class="cover-overlay">
          <div class="zone-info">
            <h1 class="zone-title">{{ zoneDetail.zoneName }}</h1>
            <div v-if="zoneDetail.assetCount" class="zone-stats">
              <span class="stats-item">
                <i class="fas fa-cube"></i>
                {{ zoneDetail.assetCount }} 个资产
              </span>
            </div>
          </div>
        </div>
      </section>

      <!-- 专区介绍 -->
      <section v-if="zoneDetail.zoneDesc" class="zone-description">
        <div class="section-header">
          <h3 class="section-title">专区介绍</h3>
          <div class="section-divider"></div>
        </div>
        <div class="description-content">
          {{ zoneDetail.zoneDesc }}
        </div>
      </section>

      <!-- 专区资产列表 -->
      <section class="zone-assets-section">
        <div class="section-header">
          <h3 class="section-title">专区资产</h3>
          <div class="section-divider"></div>
        </div>

        <!-- 资产列表加载状态 -->
        <div v-if="assetsLoading" class="assets-loading">
          <i class="fas fa-spinner fa-spin"></i>
          <p>正在加载资产列表...</p>
        </div>

        <!-- 资产列表 -->
        <div v-else-if="assetList.length > 0" class="products-grid">
          <AssetCard 
            v-for="(product, index) in assetList" 
            :key="product.assetId || index"
            :asset="product as any"
            :show-asset-type="true"
            :show-issuer-label="true"
            :asset-types-list="assetTypesList"
            @click="handleAssetClick"
          />
        </div>

        <!-- 空状态 -->
        <div v-else class="empty-state">
          <i class="fas fa-inbox"></i>
          <p>该专区暂无资产</p>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore && !assetsLoading" class="load-more-container">
          <button class="load-more-btn" @click="loadMoreAssets">
            <i class="fas fa-plus"></i>
            加载更多
          </button>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import AssetCard from '@/components/AssetCard.vue'
import { ZoneAPI, type ZoneDetail, type AssetItem } from '@/api/zone'
import { DictionaryAPI } from '@/api'
import type { DictionaryItem } from '@/api'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由相关
const route = useRoute()
const router = useRouter()

// 数据状态
const loading = ref(true)
const error = ref('')
const zoneDetail = ref<ZoneDetail | null>(null)

// 资产列表相关
const assetsLoading = ref(false)
const assetList = ref<AssetItem[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalAssets = ref(0)

// 资产类型字典数据
const assetTypesList = ref<DictionaryItem[]>([])
const isLoadingAssetTypes = ref(false)

// 计算属性
const zoneId = computed(() => route.params.zoneId as string)
const hasMore = computed(() => assetList.value.length < totalAssets.value)

// 获取专区详情
const fetchZoneDetail = async () => {
  try {
    const response = await ZoneAPI.getZoneDetail(zoneId.value)
    if (response && response.code === 200 && response.data) {
      zoneDetail.value = response.data
    } else {
      error.value = response?.msg || '获取专区详情失败'
    }
  } catch (err) {
    console.error('获取专区详情失败:', err)
    error.value = '网络错误，请稍后重试'
  }
}

// 获取专区资产列表
const fetchAssetList = async (page: number = 1, append: boolean = false) => {
  try {
    assetsLoading.value = true
    const response = await ZoneAPI.getZoneAssetList({
      zoneId: zoneId.value,
      pageNum: page,
      pageSize: pageSize.value
    })
    
    if (response && response.code === 200 && response.rows) {
      const { rows, total } = response
      if (append) {
        assetList.value.push(...rows)
      } else {
        assetList.value = rows
      }
      
      totalAssets.value = total
      currentPage.value = page
    } else {
      console.error('获取资产列表失败:', response?.msg)
    }
  } catch (err) {
    console.error('获取资产列表失败:', err)
  } finally {
    assetsLoading.value = false
  }
}

// 获取资产类型字典数据
const loadAssetTypes = async () => {
  try {
    isLoadingAssetTypes.value = true
    console.log('📡 开始请求资产类型字典数据...')
    const response = await DictionaryAPI.getDictionary('dig_asset_type')
    
    console.log('📥 资产类型字典API响应:', response)
    
    if (response.code === 200 && response.data && response.data.length > 0) {
      assetTypesList.value = response.data
      console.log('✅ 资产类型字典数据加载成功:', assetTypesList.value.length, '条')
    } else {
      assetTypesList.value = []
      console.warn('⚠️ 资产类型字典接口返回空数据')
    }
  } catch (error) {
    console.error('❌ 获取资产类型字典失败:', error)
    assetTypesList.value = []
  } finally {
    isLoadingAssetTypes.value = false
  }
}

// 加载更多资产
const loadMoreAssets = () => {
  if (!assetsLoading.value && hasMore.value) {
    fetchAssetList(currentPage.value + 1, true)
  }
}

// 处理资产卡片点击事件
const handleAssetClick = (asset: any) => {
  const assetId = asset.assetId || asset.id
  if (assetId) {
    router.push(`/asset/${assetId}`)
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await Promise.all([
      fetchZoneDetail(),
      fetchAssetList(1, false),
      loadAssetTypes()
    ])
  } catch (err) {
    console.error('获取数据失败:', err)
    error.value = '获取数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.zone-detail-container {
  background: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  color: #f8f6f0;
  font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  min-height: 100vh;
}

/* 页面头部导航 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 16px;
  position: sticky;
  top: 0;
  background: linear-gradient(180deg, rgba(26, 26, 26, 0.95) 0%, rgba(36, 36, 36, 0.9) 100%);
  backdrop-filter: blur(20px);
  z-index: 10;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.back-btn {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #f8f6f0;
  font-size: 18px;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background-color: rgba(212, 165, 116, 0.2);
  color: #d4a574;
  transform: translateY(-50%) scale(1.05);
  box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #f8f6f0;
  margin: 0;
  text-align: center;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  color: #e0ddd4;
}

.loading-container i, .error-container i {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d4a574;
}

.retry-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(212, 165, 116, 0.3);
}

/* 专区封面区域 */
.zone-cover-section {
  position: relative;
  height: 300px;
  margin-bottom: 24px;
  border-radius: 0 0 20px 20px;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  position: relative;
}

.cover-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: #242424;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #a39d8e;
  border: 2px dashed rgba(255, 255, 255, 0.1);
}

.cover-placeholder i {
  font-size: 48px;
  margin-bottom: 12px;
}

.cover-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 32px 20px 20px;
}

.zone-title {
  font-size: 28px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.zone-stats {
  display: flex;
  gap: 16px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #e8c49a;
  font-size: 14px;
  font-weight: 500;
}

/* 公共区域样式 */
.zone-description,
.zone-assets-section {
  padding: 0 20px;
  margin-bottom: 32px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #f8f6f0;
  margin: 0 0 12px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.section-divider {
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, #d4a574 0%, transparent 50%, #d4a574 100%);
  opacity: 0.3;
}

.description-content {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border-radius: 12px;
  border: 1px solid rgba(248, 246, 240, 0.1);
  padding: 20px;
  font-size: 14px;
  line-height: 1.6;
  color: #e0ddd4;
}

/* 资产网格和卡片 */
.assets-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 0;
  color: #e0ddd4;
}

.assets-loading i {
  font-size: 32px;
  margin-bottom: 12px;
  color: #d4a574;
}

/* 产品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin: 24px 0;
  padding-bottom: 65px;
}

/* 其他状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #a39d8e;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.load-more-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
}

.load-more-btn {
  background: linear-gradient(145deg, rgba(42, 42, 42, 0.9) 0%, rgba(46, 46, 46, 0.8) 100%);
  border: 1px solid rgba(248, 246, 240, 0.1);
  color: #e0ddd4;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.load-more-btn:hover {
  background: rgba(50, 50, 50, 0.95);
  border-color: rgba(212, 165, 116, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px 12px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .back-btn {
    left: 12px;
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .back-btn:hover {
    transform: translateY(-50%);
    box-shadow: none;
  }
  
  .zone-cover-section {
    height: 250px;
  }
  
  .zone-title {
    font-size: 24px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding-bottom: 60px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 14px 10px;
  }
  
  .page-title {
    font-size: 15px;
  }
  
  .back-btn {
    left: 10px;
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  
  .back-btn:hover {
    transform: translateY(-50%);
    box-shadow: none;
  }
  
  .zone-cover-section {
    height: 200px;
  }
  
  .cover-overlay {
    padding: 20px 16px 16px;
  }
  
  .zone-title {
    font-size: 20px;
  }
  
  .zone-description,
  .zone-assets-section {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  /* 保持两列布局，但调整间距和大小 */
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding-bottom: 50px;
  }
}
</style> 