{"openapi": "3.0.1", "info": {"title": "川文数服", "description": "", "version": "1.0.0"}, "tags": [{"name": "通用"}], "paths": {"/system/dict/data/type/{dictType}": {"get": {"summary": "字典查询", "x-apifox-folder": "通用", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["通用"], "parameters": [{"name": "dictType", "in": "path", "description": "字典类型", "required": true, "example": "unionLegalOccupation", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string"}, "code": {"type": "integer"}, "data": {"type": "array", "items": {"type": "object", "properties": {"createBy": {"type": "string"}, "createTime": {"type": "string"}, "updateBy": {"type": "null"}, "updateTime": {"type": "null"}, "remark": {"type": "string"}, "params": {"type": "object", "properties": {"@type": {"type": "string"}}, "required": ["@type"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["@type"]}, "dictCode": {"type": "integer"}, "dictSort": {"type": "integer"}, "dictLabel": {"type": "string"}, "dictValue": {"type": "string"}, "dictType": {"type": "string"}, "cssClass": {"type": "null"}, "listClass": {"type": "string"}, "isDefault": {"type": "string"}, "status": {"type": "string"}, "default": {"type": "boolean"}}, "required": ["createBy", "createTime", "updateBy", "updateTime", "remark", "params", "dictCode", "dictSort", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "dictType", "cssClass", "listClass", "isDefault", "status", "default"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["createBy", "createTime", "updateBy", "updateTime", "remark", "params", "dictCode", "dictSort", "dict<PERSON><PERSON>l", "dict<PERSON><PERSON>ue", "dictType", "cssClass", "listClass", "isDefault", "status", "default"]}}}, "required": ["msg", "code", "data"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["msg", "code", "data"]}, "examples": {"1": {"summary": "成功示例", "value": {"msg": "操作成功", "code": 200, "data": [{"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 197, "dictSort": 1, "dictLabel": "各类专业、技术人员", "dictValue": "0", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 198, "dictSort": 2, "dictLabel": "国家机关、党群组织、企事业单位的负责人", "dictValue": "1", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 199, "dictSort": 3, "dictLabel": "办事人员和有关人员", "dictValue": "2", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 200, "dictSort": 4, "dictLabel": "商业工作人员", "dictValue": "3", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 201, "dictSort": 5, "dictLabel": "服务性工作人员", "dictValue": "4", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 202, "dictSort": 6, "dictLabel": "农林牧渔劳动者", "dictValue": "5", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 203, "dictSort": 7, "dictLabel": "生产工作、运输工作和部分体力劳动者", "dictValue": "6", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}, {"createBy": "admin", "createTime": "2024-04-08 15:42:34", "updateBy": null, "updateTime": null, "remark": null, "params": {"@type": "java.util.HashMap"}, "dictCode": 204, "dictSort": 8, "dictLabel": "不便分类的其他劳动者", "dictValue": "7", "dictType": "unionLegalOccupation", "cssClass": null, "listClass": null, "isDefault": "N", "status": "0", "default": false}]}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-225640576-run", "security": []}}, "/ali/cor": {"post": {"summary": "OCR证件识别", "x-apifox-folder": "通用", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["通用"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"idType": {"type": "string", "description": "证件类型"}, "side": {"type": "string", "description": "正反面"}, "base64": {"type": "string", "description": "图片base64编码"}}, "x-apifox-orders": ["idType", "side", "base64"], "required": ["idType", "side", "base64"], "x-apifox-ignore-properties": []}, "example": {"idType": "1", "side": "face", "base64": "data:image/png;base64,iVBORw0KGgo"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}, "examples": {"1": {"summary": "成功示例", "value": {"code": 200, "msg": "操作成功", "data": "{\"address\":\"中国(四川)自由贸易试验区成都高新区益州大道1999号15栋13层1301号\",\"angle\":90,\"business\":\"计算机软硬件研发:计算机系统集成;设计、制作、代理、发布(不含气球广告)国内各类广告;会议及展览展示服务;影视创作:公共关系服务:礼仪服务:组织策划文化艺术交流活动;货物及技术进出口(国家禁止或涉及行政审批的货物和技术进出口除外);销售:电子产品、数码产品、金属制品、金银制品、珠宝首饰、通讯设备(不含无线广播电视发射及卫星地面接收设备)并提供技术服务;房地产营销策划:房地产信息咨询;工程项目管理:电子与智能化工程施工:企业管理咨询;商务信息咨询(不含证券、期货、金融类及投资咨询);文化艺术咨询;教育咨询;安防设备(国家有专项规定的除外)的开发、销售及技术服务;广播电视节目制作:经营演出及经纪业务:综合文艺表演;增值电信业务经营;国内贸易代理:出版物零售;销售:办公用品、包装材料;房地产租赁。(涉及许可的未取得相关行政许可(审批),不得开展经营活动)(涉及资质的凭资质许可证经营)(依法须经批准的项目,经相关部门批准后方可展开经营活动)。\",\"capital\":\"伍佰万元整\",\"captial\":\"伍佰万元整\",\"companyForm\":\"FailInRecognition\",\"config_str\":\"{}\",\"emblem\":{\"height\":152,\"left\":1003,\"top\":217,\"width\":215},\"establish_date\":\"20190918\",\"is_gray\":false,\"issue_date\":\"2020年7月7日\",\"name\":\"成都九天星空科技有限公司\",\"person\":\"王宇\",\"qrcode\":{\"height\":150,\"left\":1596,\"top\":448,\"width\":151},\"reg_num\":\"91510100MA6CW5A198\",\"request_id\":\"6249EA60-56FE-4727-B2F6-916B8954D2CB\",\"stamp\":{\"height\":301,\"left\":1674,\"top\":1061,\"width\":298},\"stamps\":[{\"height\":301,\"left\":1674,\"top\":1061,\"width\":298}],\"success\":true,\"title\":{\"height\":117,\"left\":846,\"top\":408,\"width\":554},\"title_content\":\"营业执照\",\"type\":\"其他有限责任公司\",\"valid_period\":\"29991231\"}"}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-*********-run", "security": []}}, "/copyright/region/page": {"get": {"summary": "省市区查询", "x-apifox-folder": "通用", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["通用"], "parameters": [{"name": "parRegionId", "in": "query", "description": "上级ID", "required": false, "example": "510000", "schema": {"type": "string"}}, {"name": "pageNum", "in": "query", "description": "页码", "required": false, "example": "1", "schema": {"type": "string"}}, {"name": "regionLevel", "in": "query", "description": "层级", "required": false, "example": "2", "schema": {"type": "string"}}, {"name": "pageSize", "in": "query", "description": "条数", "required": false, "example": "100", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer"}, "rows": {"type": "array", "items": {"type": "object", "properties": {"regionId": {"type": "integer"}, "parRegionId": {"type": "integer"}, "regionName": {"type": "string"}, "regionLevel": {"type": "integer"}, "regionType": {"type": "integer"}, "regionDesc": {"type": "string"}, "regionSort": {"type": "integer"}, "regionLong": {"type": "integer"}, "createStaff": {"type": "integer"}, "updateStaff": {"type": "integer"}, "createDate": {"type": "string"}, "statusDate": {"type": "string"}, "updateDate": {"type": "string"}, "remark": {"type": "string"}}, "required": ["regionId", "parRegionId", "regionName", "regionLevel", "regionType", "regionDesc", "regionSort", "regionLong", "createStaff", "updateStaff", "createDate", "statusDate", "updateDate", "remark"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["regionId", "parRegionId", "regionName", "regionLevel", "regionType", "regionDesc", "regionSort", "regionLong", "createStaff", "updateStaff", "createDate", "statusDate", "updateDate", "remark"]}}, "code": {"type": "integer"}, "msg": {"type": "string"}}, "required": ["total", "rows", "code", "msg"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["total", "rows", "code", "msg"]}, "examples": {"1": {"summary": "成功示例", "value": {"total": 21, "rows": [{"regionId": 510100, "parRegionId": 510000, "regionName": "成都市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 1, "regionLong": 28, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510300, "parRegionId": 510000, "regionName": "自贡市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 3, "regionLong": 813, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510400, "parRegionId": 510000, "regionName": "攀枝花市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 4, "regionLong": 812, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510500, "parRegionId": 510000, "regionName": "泸州市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 5, "regionLong": 830, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510600, "parRegionId": 510000, "regionName": "德阳市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 6, "regionLong": 838, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510700, "parRegionId": 510000, "regionName": "绵阳市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 7, "regionLong": 816, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510800, "parRegionId": 510000, "regionName": "广元市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 8, "regionLong": 839, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 510900, "parRegionId": 510000, "regionName": "遂宁市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 9, "regionLong": 825, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 511000, "parRegionId": 510000, "regionName": "内江市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 10, "regionLong": 832, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}, {"regionId": 511100, "parRegionId": 510000, "regionName": "乐山市", "regionLevel": 2, "regionType": 1300, "regionDesc": "", "regionSort": 11, "regionLong": 833, "createStaff": 0, "updateStaff": 0, "createDate": "2019-08-07T18:27:20", "statusDate": "2019-08-07T18:27:20", "updateDate": "2019-08-07T18:27:20", "remark": ""}], "code": 200, "msg": "查询成功"}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-230836913-run", "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": []}