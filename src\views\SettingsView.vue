<template>
  <AppPageHeader :title="'设置'" @back="$router.back()" />
  <div class="settings-page" :style="{ paddingTop: '40px' }">
    <main class="main-content">


      <!-- 设置菜单 -->
      <section class="menu-section">
        <!-- <div class="section-title">账户设置</div> -->
        <div class="menu-list">
          <!-- 实名认证 -->
          <button class="menu-item" @click="navigateTo('/verification')">
            <div class="menu-icon">
              <i class="fas fa-user-check"></i>
            </div>
            <div class="menu-content">
              <div class="menu-title">实名认证</div>
            </div>
            <div class="menu-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </button>
          
          <!-- 重置密码 -->
          <button class="menu-item" @click="navigateTo('/reset-password')">
            <div class="menu-icon">
              <i class="fas fa-key"></i>
            </div>
            <div class="menu-content">
              <div class="menu-title">重置密码</div>
            </div>
            <div class="menu-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </button>
          
          <!-- 消息通知 -->
          <!-- <button class="menu-item" @click="navigateTo('/notifications')">
            <div class="menu-icon">
              <i class="fas fa-bell"></i>
            </div>
            <div class="menu-content">
              <div class="menu-title">消息通知</div>
            </div>
            <div class="menu-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </button> -->
          
          <!-- 关于我们 -->
          <button class="menu-item" @click="navigateTo('/about')">
            <div class="menu-icon">
              <i class="fas fa-info-circle"></i>
            </div>
            <div class="menu-content">
              <div class="menu-title">关于我们</div>
            </div>
            <div class="menu-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </button>


          <!-- 隐私政策 -->
          <button class="menu-item" @click="navigateTo('/agreement-list')">
            <div class="menu-icon">
              <i class="fas fa-file-contract"></i>
            </div>
            <div class="menu-content">
              <div class="menu-title">隐私政策</div>
            </div>
            <div class="menu-arrow">
              <i class="fas fa-chevron-right"></i>
            </div>
          </button>
        </div>
      </section>

      <!-- 退出登录 -->
      <button class="logout-btn" @click="handleLogout">
        <i class="fas fa-sign-out-alt"></i> 退出登录
      </button>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import AppPageHeader from '@/components/AppPageHeader.vue'

const router = useRouter()

// 通知系统
const { success, error } = useNotification()

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 导航到指定页面
 */
const navigateTo = (path: string) => {
  if (path === '/verification') {
    router.push('/verification')
  } else if (path === '/notifications') {
    router.push('/notifications')
  } else if (path === '/about') {
    router.push('/about')
  } else if (path === '/privacy') {
    router.push('/privacy')
  } else if (path === '/reset-password') {
    router.push('/reset-password')
  } else if (path === '/agreement-list') {
    router.push('/agreement-list')
  } else {
    // TODO: 实现其他页面路由
    console.log('Page not implemented yet:', path)
  }
}

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    // 这里可以添加确认对话框
    const confirmed = confirm('确定要退出登录吗？')
    if (!confirmed) return
    
    // 调用登出API
    AuthAPI.logout()
    
    success('退出登录成功')
    
    // 跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 1000)
  } catch (err) {
    error('退出登录失败')
    console.error('Logout error:', err)
  }
}
</script>

<style scoped>
/* CSS变量定义 - 深色主题配色方案 */
.settings-page {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --accent-red: var(--error-color);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
  
  /* 页面样式 */
  background: var(--gradient-hero);
  color: var(--neutral-100);
  font-size: 14px;
  line-height: 1.6;
  min-height: 100vh;
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
  padding: 20px 16px 20px 16px;
  position: relative;
  z-index: 1;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding: 0 4px;
}

.back-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: var(--neutral-100);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.back-btn:hover {
  background: rgba(212, 165, 116, 0.2);
  transform: scale(1.1);
}

.back-btn i {
  font-size: 16px;
}

.page-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--neutral-100);
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-placeholder {
  width: 40px;
}

/* 菜单部分 - 参考ProfileView的资产管理样式 */
.menu-section {
  margin: 20px 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-200);
  margin-bottom: 12px;
  padding: 0 4px;
}

.menu-list {
  background: var(--gradient-card);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: var(--neutral-100);
  cursor: pointer;
  transition: all 0.3s;
  width: 100%;
  text-align: left;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.menu-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #d4a574;
  margin-right: 12px;
  font-size: 18px;
  font-weight: bold;
}

.menu-content {
  flex: 1;
}

.menu-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 2px;
}

.menu-desc {
  font-size: 12px;
  color: var(--neutral-400);
}

.menu-arrow {
  color: var(--neutral-400);
  font-size: 14px;
}

/* 退出登录按钮 */
.logout-btn {
  background: var(--accent-red);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  margin: 20px 0;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.logout-btn:hover {
  background: #B91C1C;
  transform: translateY(-2px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .main-content {
    padding: 16px 12px 16px 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
  
  .menu-item {
    padding: 16px;
  }
  
  .menu-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 12px 8px 12px 8px;
  }
  
  .page-header {
    margin-bottom: 20px;
  }
  
  .back-btn {
    width: 36px;
    height: 36px;
  }
  
  .back-btn i {
    font-size: 14px;
  }
  
  .page-title {
    font-size: 16px;
  }
  
  .menu-item {
    padding: 12px;
  }
  
  .menu-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
    margin-right: 10px;
  }
  
  .menu-title {
    font-size: 14px;
  }
  
  .menu-desc {
    font-size: 11px;
  }
}
</style> 