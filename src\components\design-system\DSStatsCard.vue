<template>
  <div class="stats-card">
    <div 
      v-for="(item, index) in stats" 
      :key="item.label"
      class="stat-item-wrapper"
    >
      <div class="stat-item">
        <div class="stat-value">{{ item.value }}</div>
        <div class="stat-label">{{ item.label }}</div>
      </div>
      <div v-if="index < stats.length - 1" class="divider"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface StatItem {
  value: string | number
  label: string
}

interface Props {
  stats: StatItem[]
}

defineProps<Props>()
</script>

<style scoped>
.stats-card {
  display: flex;
  justify-content: space-between;
  background: rgba(60, 40, 30, 0.7);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  border: 1px solid var(--border-primary);
  max-width: 100%;
  margin: 0 auto;
}

.stat-item-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 12px;
  color: var(--text-tertiary);
}

.divider {
  height: 32px;
  width: 1px;
  background: var(--border-primary);
  margin: 0 var(--spacing-sm);
}
</style> 