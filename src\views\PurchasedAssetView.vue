<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import UserAPI, { type AssetData } from '@/api/user'
import BottomNavigation from '@/components/BottomNavigation.vue'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 路由和通知
const route = useRoute()
const router = useRouter()
const { success, error: showError, warning, info } = useNotification()

// 响应式数据
const loading = ref(false)
const error = ref('')
const assetDetail = ref<AssetData | null>(null)

/**
 * 加载资产详情
 */
const loadAssetDetail = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const dataAssetId = Number(route.params.dataAssetId)
    if (!dataAssetId || isNaN(dataAssetId)) {
      throw new Error('无效的资产ID')
    }

    const response = await UserAPI.getAssetDetail(dataAssetId)
    
    if (response.code === 200 && response.data) {
      assetDetail.value = response.data
    } else {
      throw new Error(response.msg || '获取资产详情失败')
    }
  } catch (err) {
    console.error('Failed to load asset detail:', err)
    error.value = err instanceof Error ? err.message : '获取资产详情失败'
  } finally {
    loading.value = false
  }
}

/**
 * 格式化链上哈希
 */
const formatChainHash = (hash: string): string => {
  if (!hash || hash.length < 10) return hash
  return `${hash.slice(0, 8)}...${hash.slice(-8)}`
}

/**
 * 格式化完整时间
 */
const formatFullTime = (timeString: string): string => {
  if (!timeString) return ''
  try {
    const date = new Date(timeString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return timeString
  }
}

/**
 * 获取状态样式类
 */
const getStatusClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1000': 'status-active',
    '1100': 'status-pending',
    'active': 'status-active',
    'inactive': 'status-inactive',
    'pending': 'status-pending',
    'expired': 'status-expired'
  }
  return statusMap[status] || 'status-default'
}

/**
 * 获取状态文字
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    '1000': '已上链',
    '1100': '上链中',
    'active': '正常',
    'inactive': '停用',
    'pending': '待审核',
    'expired': '已过期'
  }
  return statusMap[status] || status || '未知'
}

/**
 * 计算持有天数
 */
const ownedDays = computed(() => {
  if (!assetDetail.value?.createDate) return 0
  
  try {
    const createDate = new Date(assetDetail.value.createDate)
    const today = new Date()
    const diffTime = Math.abs(today.getTime() - createDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  } catch {
    return 0
  }
})

/**
 * 处理图片加载失败
 */
const handleImageError = (event: Event) => {
  const target = event.target as HTMLImageElement
  target.style.display = 'none'
  const placeholder = target.parentNode?.querySelector('.cover-placeholder') as HTMLElement
  if (placeholder) {
    placeholder.style.display = 'flex'
  }
}

/**
 * 复制资产编号
 */
const copyAssetCode = async () => {
  if (!assetDetail.value) return
  
  try {
    await navigator.clipboard.writeText(assetDetail.value.assetCode)
    success('资产编号已复制到剪贴板')
  } catch (err) {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = assetDetail.value.assetCode
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('资产编号已复制到剪贴板')
  }
}

/**
 * 复制链上哈希
 */
const copyChainHash = async () => {
  if (!assetDetail.value?.chainHash) return
  
  try {
    await navigator.clipboard.writeText(assetDetail.value.chainHash)
    success('链上哈希已复制到剪贴板')
  } catch (err) {
    // 备用复制方法
    const textArea = document.createElement('textarea')
    textArea.value = assetDetail.value.chainHash
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    success('链上哈希已复制到剪贴板')
  }
}

/**
 * 查看3D模型
 */
const view3DModel = () => {
  if (!assetDetail.value) return
  
  router.push({
    name: 'asset-model',
    params: { assetId: assetDetail.value.assetId.toString() },
    query: {
      name: assetDetail.value.assetName,
      code: assetDetail.value.assetCode,
      time: assetDetail.value.chainTime
    }
  })
}

/**
 * 转赠资产
 */
const transferAsset = () => {
  if (!assetDetail.value) return
  
  // TODO: 实现转赠功能
  info('转赠功能开发中...')
}

/**
 * 分享资产
 */
const shareAsset = () => {
  if (!assetDetail.value) return
  
  // TODO: 实现分享功能
  info('分享功能开发中...')
}

/**
 * 兑换权益
 */
const redeemBenefit = () => {
  if (!assetDetail.value) return
  
  // TODO: 实现权益兑换功能
  info('权益兑换功能开发中...')
}

/**
 * 查看所有权证明
 */
const viewOwnershipCertificate = () => {
  if (!assetDetail.value) return
  
  // TODO: 实现所有权证明查看功能
  info('所有权证明功能开发中...')
}

/**
 * 查看交易历史
 */
const viewTransactionHistory = () => {
  if (!assetDetail.value) return
  
  // TODO: 实现交易历史功能
  info('交易历史功能开发中...')
}

// 生命周期
onMounted(() => {
  loadAssetDetail()
})
</script>

<template>
  <AppPageHeader :title="'我的资产'" @back="$router.back()" />
  <div class="purchased-asset-page with-bottom-nav">

    <main class="main-content" style="padding-top: 50px;">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
        </div>
        <p class="loading-text">加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <p class="error-text">{{ error }}</p>
        <button @click="loadAssetDetail" class="retry-btn">
          重新加载
        </button>
      </div>

      <!-- 资产详情内容 -->
      <div v-else-if="assetDetail" class="asset-detail-content">
        <!-- 资产封面和所有权信息 -->
        <section class="asset-hero-section">
          <div class="cover-container">
            <img
              v-if="assetDetail.assetCoverThumbnail"
              :src="assetDetail.assetCoverThumbnail"
              :alt="assetDetail.assetName"
              class="cover-image"
              @error="handleImageError"
            />
            <div v-else class="cover-placeholder">
              <i class="fas fa-gem"></i>
              <span class="placeholder-text">暂无封面</span>
            </div>
            <!-- 所有权徽章 -->
            <div class="ownership-overlay">
              <div class="ownership-badge">
                <i class="fas fa-crown"></i>
                <span>{{ assetDetail.assetCode }}</span>
              </div>
              <div class="status-badge" :class="getStatusClass(assetDetail.statusCd)">
                <i class="fas fa-circle status-dot"></i>
                <span>{{ getStatusText(assetDetail.statusCd) }}</span>
              </div>
            </div>
          </div>
          <!-- 新增：资产名称 -->
          <div class="asset-name-main">{{ assetDetail.assetName }}</div>
        </section>

        <!-- 快速操作区域 -->
        <section class="quick-actions-section">
          <div class="actions-row">
            <button class="quick-action model-action" @click="view3DModel">
              <div class="action-icon">
                <i class="fas fa-cube"></i>
              </div>
              <span class="action-text">3D模型</span>
            </button>
            
            <button class="quick-action transfer-action" @click="transferAsset">
              <div class="action-icon">
                <i class="fas fa-exchange-alt"></i>
              </div>
              <span class="action-text">转赠</span>
            </button>
            
            <!-- <button class="quick-action share-action" @click="shareAsset">
              <div class="action-icon">
                <i class="fas fa-share-alt"></i>
              </div>
              <span class="action-text">分享</span>
            </button>
            
            <button 
              v-if="assetDetail.isRedeemed === 0" 
              class="quick-action redeem-action" 
              @click="redeemBenefit"
            >
              <div class="action-icon">
                <i class="fas fa-gift"></i>
              </div>
              <span class="action-text">权益</span>
            </button> -->
          </div>
        </section>

        <!-- 所有权信息 -->
        <section class="ownership-section">
          <h3 class="section-title">
            <i class="fas fa-shield-alt"></i>
            所有权信息
          </h3>
          
          <div class="ownership-cards">
            <!-- 链上证明 -->
            <div class="ownership-card">
              <div class="card-icon">
                <i class="fas fa-link"></i>
              </div>
              <div class="card-content">
                <div class="card-title">链上哈希</div>
                <div class="card-value" v-if="assetDetail.chainHash">
                  <span class="hash-text" :title="assetDetail.chainHash">
                    {{ formatChainHash(assetDetail.chainHash) }}
                  </span>
                  <button class="copy-btn" @click="copyChainHash" title="复制完整哈希">
                    <i class="fas fa-external-link-alt"></i>
                  </button>
                </div>
                <div class="card-value" v-else>
                  <span class="empty-text">暂未上链</span>
                </div>
              </div>
            </div>

            <!-- 上链时间 -->
            <div class="ownership-card">
              <div class="card-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="card-content">
                <div class="card-title">上链时间</div>
                <div class="card-value">{{ formatFullTime(assetDetail.chainTime) }}</div>
              </div>
            </div>

            <!-- 获得时间 -->
            <div class="ownership-card">
              <div class="card-icon">
                <i class="fas fa-calendar-plus"></i>
              </div>
              <div class="card-content">
                <div class="card-title">获得时间</div>
                <div class="card-value">{{ formatFullTime(assetDetail.createDate) }}</div>
              </div>
            </div>

            <!-- 权益状态 -->
            <!-- <div class="ownership-card">
              <div class="card-icon">
                <i class="fas fa-gift"></i>
              </div>
              <div class="card-content">
                <div class="card-title">权益状态</div>
                <div class="card-value">
                  <span :class="['status-text', assetDetail.isRedeemed === 1 ? 'redeemed' : 'unredeemed']">
                    {{ assetDetail.isRedeemed === 1 ? '已兑换' : '未兑换' }}
                  </span>
                </div>
              </div>
            </div> -->
          </div>
        </section>

        <!-- 更多操作 -->
        <!-- <section class="more-actions-section">
          <h3 class="section-title">
            <i class="fas fa-tools"></i>
            更多操作
          </h3>
          
          <div class="action-list">
            <button class="action-item" @click="viewOwnershipCertificate">
              <div class="action-left">
                <div class="action-icon">
                  <i class="fas fa-certificate"></i>
                </div>
                <div class="action-info">
                  <div class="action-title">所有权证明</div>
                  <div class="action-desc">查看或下载所有权证明文件</div>
                </div>
              </div>
              <div class="action-right">
                <i class="fas fa-chevron-right"></i>
              </div>
            </button>

            <button class="action-item" @click="viewTransactionHistory">
              <div class="action-left">
                <div class="action-icon">
                  <i class="fas fa-history"></i>
                </div>
                <div class="action-info">
                  <div class="action-title">交易历史</div>
                  <div class="action-desc">查看此资产的所有交易记录</div>
                </div>
              </div>
              <div class="action-right">
                <i class="fas fa-chevron-right"></i>
              </div>
            </button>

            <button class="action-item" @click="view3DModel">
              <div class="action-left">
                <div class="action-icon">
                  <i class="fas fa-cube"></i>
                </div>
                <div class="action-info">
                  <div class="action-title">3D展示</div>
                  <div class="action-desc">立体查看您的数字资产</div>
                </div>
              </div>
              <div class="action-right">
                <i class="fas fa-chevron-right"></i>
              </div>
            </button>
          </div>
        </section> -->
      </div>
    </main>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<style scoped>
/* CSS变量定义 */
.purchased-asset-page {
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --accent-color: #f4a261;
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #3498db;
  --ownership-color: #9b59b6;
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --border-light: rgba(248, 246, 240, 0.1);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-ownership: linear-gradient(135deg, var(--ownership-color) 0%, #8e44ad 100%);

  background: var(--gradient-hero);
  color: var(--text-primary);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 顶部导航 */
.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--gradient-card);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-light);
  padding: 12px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  max-width: 100%;
  margin: 0 auto;
}

.back-btn, .share-btn {
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 18px;
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover, .share-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--primary-color);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 主要内容 */
.main-content {
  padding: 0 0 100px 0;
}

/* 加载和错误状态 */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.loading-spinner, .error-icon {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.loading-text, .error-text {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 24px;
}

.retry-btn {
  background: var(--primary-color);
  color: var(--bg-primary);
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
}

/* 资产详情内容 */
.asset-detail-content {
  padding: 0 16px;
}

/* 资产英雄区域 */
.asset-hero-section {
  margin-bottom: 24px;
  background: var(--gradient-card);
  border-radius: 20px;
  padding: 20px;
  border: 1px solid var(--border-light);
}

.cover-container {
  position: relative;
  width: 100%;
  height: 350px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 20px;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.cover-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: var(--text-muted);
}

.cover-placeholder i {
  font-size: 48px;
}

.placeholder-text {
  font-size: 14px;
}

.ownership-overlay {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
}

.ownership-badge {
  background: var(--gradient-ownership);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
}

.status-badge {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 6px;
  backdrop-filter: blur(10px);
}

.status-dot {
  font-size: 8px;
}

.status-active .status-dot {
  color: var(--success-color);
}

.status-pending .status-dot {
  color: var(--warning-color);
}

/* 资产信息 */
.asset-info {
  text-align: center;
}

.asset-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: var(--text-primary);
}

.asset-meta {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--text-secondary);
}

.meta-item i {
  color: var(--primary-color);
}

/* 快速操作区域 */
.quick-actions-section {
  margin-bottom: 24px;
}

.actions-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.quick-action {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 16px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: var(--text-primary);
}

.quick-action:hover {
  transform: translateY(-4px);
  border-color: var(--primary-color);
  box-shadow: 0 8px 20px rgba(212, 165, 116, 0.2);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.model-action .action-icon {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

.transfer-action .action-icon {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
}

.share-action .action-icon {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.redeem-action .action-icon {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.action-text {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
}

/* 所有权信息区域 */
.ownership-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title i {
  color: var(--primary-color);
}

.ownership-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.ownership-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  gap: 12px;
}

.card-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 4px;
}

.card-value {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.hash-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.empty-text {
  color: var(--text-muted);
  font-style: italic;
}

.status-text.redeemed {
  color: var(--success-color);
}

.status-text.unredeemed {
  color: var(--warning-color);
}

/* 更多操作区域 */
.more-actions-section {
  margin-bottom: 24px;
}

.action-list {
  background: var(--gradient-card);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  overflow: hidden;
}

.action-item {
  width: 100%;
  background: transparent;
  border: none;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
}

.action-item:last-child {
  border-bottom: none;
}

.action-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-item .action-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.action-info {
  text-align: left;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.action-desc {
  font-size: 14px;
  color: var(--text-secondary);
}

.action-right {
  color: var(--text-muted);
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .actions-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .ownership-cards {
    grid-template-columns: 1fr;
  }
  
  .asset-meta {
    flex-direction: column;
    gap: 8px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .purchased-asset-page {
    padding-bottom: max(100px, env(safe-area-inset-bottom));
  }
}
.asset-name-main {
  text-align: center;
  font-size: 22px;
  font-weight: 700;
  color: #fff;
  margin: 0 0 12px 0;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 8px rgba(0,0,0,0.18), 0 1px 0 #222;
}
</style> 