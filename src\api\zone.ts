/**
 * 专区相关API
 */
import request from './request'
import type { ApiResponse } from './types'

/**
 * 专区详情类型
 */
export interface ZoneDetail {
  zoneId: string | number
  zoneName: string
  zoneDesc?: string
  zoneCover?: string
  zoneImage?: string
  assetCount?: number
  createTime?: string
  updateTime?: string
  status?: string | number
  [key: string]: any
}

/**
 * 专区资产项类型（复用AssetItem结构）
 */
export interface AssetItem {
  assetId: string | number
  assetName: string
  assetCover?: string
  assetImage?: string
  assetDesc?: string
  issuePrice?: number
  currentPrice?: number
  price?: number
  assetType?: string
  seriesId?: string | number
  seriesName?: string
  zoneId?: string | number
  zoneName?: string
  issueQuantity?: number
  totalQuantity?: number
  saleStartTime?: string
  saleTime?: string
  createTime?: string
  createDate?: string
  status?: number | string
  statusCd?: string
  // 发行方信息
  issuers?: Array<{
    issuerName?: string
    name?: string
    issuerLogo?: string
    logo?: string
  }>
  issuerIds?: string
  issuerName?: string
  issuerLogo?: string
  publisherName?: string
  // 其他字段
  assetKeywords?: string
  keywords?: string[]
  introImages?: string
  assetLevel?: string
  individualLimit?: number
  enterpriseLimit?: number
  airdropQuantity?: number
  activityQuantity?: number
  popularity?: number
  [key: string]: any
}

/**
 * 专区资产列表请求参数
 */
export interface ZoneAssetListParams {
  zoneId: string | number
  page?: number
  pageNum?: number
  size?: number
  pageSize?: number
  assetName?: string
  assetType?: string
  [key: string]: any
}

/**
 * 专区资产列表响应
 */
export interface ZoneAssetListResponse {
  rows: AssetItem[]
  total: number
  page?: number
  pageNum?: number
  pageSize?: number
  code: number
  msg: string
}

/**
 * 专区列表项类型
 */
export interface ZoneListItem {
  zoneId: string | number
  zoneName: string
  assetCount?: number
  [key: string]: any
}

/**
 * 专区API类
 */
export class ZoneAPI {
  /**
   * 获取专区列表名称
   */
  static async getZoneListName(): Promise<ApiResponse<ZoneListItem[]>> {
    return request.get('/zone/zone/listName')
  }

  /**
   * 获取专区详情
   */
  static async getZoneDetail(zoneId: string | number): Promise<ApiResponse<ZoneDetail>> {
    return request.get(`/zone/zone/client/${zoneId}`)
  }

  /**
   * 获取专区资产列表
   */
  static async getZoneAssetList(params: ZoneAssetListParams): Promise<ZoneAssetListResponse> {
    const requestParams: Record<string, any> = {
      zoneId: params.zoneId
    }

    // 添加分页参数
    if (params.page !== undefined) {
      requestParams.page = params.page
    }
    if (params.pageNum !== undefined) {
      requestParams.pageNum = params.pageNum
    }
    if (params.size !== undefined) {
      requestParams.size = params.size
    }
    if (params.pageSize !== undefined) {
      requestParams.pageSize = params.pageSize
    }

    // 添加搜索和筛选参数
    if (params.assetName?.trim()) {
      requestParams.assetName = params.assetName.trim()
    }
    if (params.assetType?.trim()) {
      requestParams.assetType = params.assetType.trim()
    }

    console.log('专区资产列表请求参数:', requestParams)
    return request.get('/asset/asset/client/list', requestParams)
  }
}

// 导出API类
export default ZoneAPI 