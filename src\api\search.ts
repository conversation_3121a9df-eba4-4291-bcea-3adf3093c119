/**
 * 搜索相关API
 */
import { request } from './request'
import type { ApiResponse } from './types'

/**
 * 搜索请求参数类型
 */
export interface DigSearchVo {
  searchValue: string
  [key: string]: any
}

/**
 * 资产搜索结果类型
 */
export interface AssetSearchResult {
  assetId: string | number
  assetName: string
  assetCover?: string
  assetImage?: string
  [key: string]: any
}

/**
 * 系列搜索结果类型
 */
export interface SeriesSearchResult {
  seriesId: string | number
  seriesName: string
  seriesCover?: string
  seriesImage?: string
  [key: string]: any
}

/**
 * 专区搜索结果类型
 */
export interface ZoneSearchResult {
  zoneId: string | number
  zoneName: string
  zoneCover?: string
  zoneImage?: string
  [key: string]: any
}

/**
 * 搜索结果数据类型
 */
export interface SearchData {
  assets?: AssetSearchResult[]     // 资产列表
  series?: SeriesSearchResult[]    // 系列列表
  zones?: ZoneSearchResult[]       // 专区列表
}

/**
 * 搜索结果响应类型
 */
export interface SearchResponse {
  data?: SearchData               // 搜索结果数据
  code: number
  msg: string
}

/**
 * 搜索API类
 */
export class SearchAPI {
  /**
   * 统一搜索接口
   * @param searchParams 搜索参数
   * @returns 搜索结果
   */
  static async search(searchParams: DigSearchVo): Promise<SearchResponse> {
    console.log('🔍 发起搜索请求:', searchParams)
    
    // 构建查询参数
    const queryParams: Record<string, any> = {}
    if (searchParams.searchValue?.trim()) {
      queryParams.searchValue = searchParams.searchValue.trim()
    }
    
    // 添加其他可能的参数
    Object.keys(searchParams).forEach(key => {
      if (key !== 'searchValue' && searchParams[key] !== undefined) {
        queryParams[key] = searchParams[key]
      }
    })
    
    console.log('🔍 搜索查询参数:', queryParams)
    return request.get('/asset/asset/client/search', queryParams)
  }
}

// 导出API类
export default SearchAPI 