/* 登录注册页面样式 */

:root {
    /* 深色金黄主题配色方案 */
    /* 主色系 - 优雅金黄 */
    --primary-color: #d4a574;
    --primary-dark: #b8956a;
    --primary-light: #e8c49a;
    --primary-alpha: rgba(212, 165, 116, 0.15);
    
    /* 辅助色系 - 温暖橙色 */
    --accent-color: #f4a261;
    --accent-dark: #e76f51;
    --accent-light: #f9c74f;
    --accent-alpha: rgba(244, 162, 97, 0.15);
    
    /* 功能色系 */
    --success-color: #90a955;
    --warning-color: #f9844a;
    --error-color: #e63946;
    --info-color: #457b9d;
    
    /* 文字颜色系统 */
    --text-primary: #f8f6f0;
    --text-secondary: #e0ddd4;
    --text-tertiary: #c4c0b1;
    --text-muted: #a39d8e;
    --text-inverse: #2d2a24;
    
    /* 背景色系统 */
    --bg-primary: #1a1a1a;
    --bg-secondary: #242424;
    --bg-tertiary: #2e2e2e;
    --bg-card: rgba(42, 42, 42, 0.9);
    --bg-card-hover: rgba(50, 50, 50, 0.95);
    --bg-glass: rgba(42, 42, 42, 0.8);
    
    /* 边框和阴影 */
    --border-primary: rgba(212, 165, 116, 0.3);
    --border-light: rgba(248, 246, 240, 0.1);
    --border-hover: rgba(212, 165, 116, 0.5);
    
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
    --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
    --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
    
    /* 渐变系统 */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
    --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
    --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
    --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
    --gradient-tech: linear-gradient(45deg, var(--accent-alpha) 0%, var(--primary-alpha) 50%, var(--accent-alpha) 100%);
    
    /* 兼容旧变量名 */
    --primary-gold: var(--primary-color);
    --primary-gold-light: var(--primary-light);
    --primary-gold-dark: var(--primary-dark);
    --primary-gold-alpha: var(--primary-alpha);
    --secondary-purple: var(--accent-color);
    --secondary-purple-light: var(--accent-light);
    --secondary-purple-dark: var(--accent-dark);
    --secondary-purple-alpha: var(--accent-alpha);
    --neutral-100: var(--text-primary);
    --neutral-200: var(--text-secondary);
    --neutral-400: var(--text-tertiary);
    --neutral-600: var(--text-muted);
    --accent-red: var(--error-color);
    --accent-orange: var(--warning-color);
    --accent-green: var(--success-color);
    --shadow-gold: var(--shadow-primary);
    --shadow-purple: var(--shadow-primary);
    --gradient-secondary: var(--gradient-accent);
}

.auth-page {
    background: var(--gradient-hero);
    color: var(--neutral-100);
    font-size: 14px;
    line-height: 1.6;
    min-height: 100vh;
    padding: 0 !important;
    margin: 0 !important;
    overflow-y: visible !important;
    height: auto !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    /* 键盘适配 */
    position: relative;
    transition: height 0.3s ease-out;
}

/* 键盘弹出时的适配 */
.auth-page.keyboard-visible {
    height: 100vh !important;
    overflow-y: auto !important;
}

/* 优化滚动性能 */
.auth-page {
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: transform;
}

/* 移除复杂的背景效果，简化布局 */
.tech-particles {
    display: none;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: var(--primary-gold);
    border-radius: 50%;
    animation: particle-float 6s linear infinite;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(1);
        opacity: 0;
    }
}

.auth-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
    /* 键盘适配优化 */
    transition: padding 0.3s ease-out;
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

/* 键盘弹出时调整容器布局 */
.keyboard-visible .auth-container {
    padding: 10px 20px;
    min-height: auto;
    /* 确保内容在键盘弹出时能够正确显示 */
    padding-bottom: 20px;
}

.auth-header {
    padding: 20px 0;
    text-align: center;
    position: relative;
}

.back-btn {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    background: var(--gradient-card);
    border: none;
    color: var(--primary-gold);
    font-size: 18px;
    cursor: pointer;
    padding: 12px;
    border-radius: 12px;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.back-btn:hover {
    transform: translateY(-50%) scale(1.05);
    box-shadow: var(--shadow-gold);
}

.logo-section {
    text-align: center;
    padding: 40px 0 50px;
    position: relative;
}

.logo-container {
    position: relative;
    display: inline-block;
}

.logo {
    width: 90px;
    height: 90px;
    background: var(--gradient-primary);
    border-radius: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    color: white;
    margin-bottom: 20px;
    box-shadow: var(--shadow-gold), var(--shadow-glow);
    position: relative;
    overflow: hidden;
    animation: logo-pulse 3s ease-in-out infinite;
}

.logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shine 2s linear infinite;
}

@keyframes logo-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.welcome-text {
    font-size: 24px;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
}

.subtitle {
    font-size: 15px;
    color: var(--neutral-400);
    font-weight: 400;
}

.auth-form {
    background: var(--gradient-card);
    border-radius: 24px;
    padding: 32px;
    margin-bottom: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.auth-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-secondary);
    opacity: 0.5;
}

.tab-switcher {
    display: flex;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 4px;
    margin-bottom: 32px;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    background: transparent;
    color: var(--neutral-400);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.tab-btn.active {
    color: white;
    background: var(--gradient-secondary);
    box-shadow: var(--shadow-purple);
    transform: scale(1.02);
}

.form-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.form-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-group {
    margin-bottom: 24px;
    position: relative;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--neutral-200);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-input {
    width: 100%;
    padding: 16px 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--neutral-100);
    font-size: 16px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    position: relative;
    /* 键盘适配优化 */
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
    /* 确保在iOS上输入框获得焦点时不会被缩放 */
    -webkit-text-size-adjust: 100%;
    /* 优化移动端输入体验 */
    touch-action: manipulation;
}

/* 键盘弹出时的特殊处理 */
.keyboard-visible .form-input:focus {
    /* 确保焦点输入框有更明显的视觉反馈 */
    transform: translateY(-2px);
    z-index: 10;
    position: relative;
}

/* 密码输入框特殊处理 */
.form-input[type="password"] {
    /* 确保密码输入框在键盘弹出时能够正确显示 */
    position: relative;
    z-index: 1;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px var(--primary-gold-alpha), var(--shadow-gold);
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.form-input.error {
    border-color: var(--accent-red);
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.form-input::placeholder {
    color: var(--neutral-400);
}

.input-wrapper {
    position: relative;
    z-index: 1;
}

/* 密码输入框特殊样式 */
.password-input {
    padding-right: 52px;
}

.password-input-wrapper {
    position: relative;
    z-index: 1;
}

.password-toggle {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(212, 165, 116, 0.15);
    border: 1px solid rgba(212, 165, 116, 0.3);
    color: var(--primary-color);
    cursor: pointer;
    font-size: 14px;
    padding: 6px;
    border-radius: 6px;
    transition: all 0.3s ease;
    z-index: 100;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    opacity: 1;
    visibility: visible;
}

.password-toggle:hover {
    background: rgba(212, 165, 116, 0.25);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-50%) scale(1.05);
}

.password-toggle:focus {
    outline: none;
    background: rgba(212, 165, 116, 0.3);
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.password-toggle:active {
    background: rgba(212, 165, 116, 0.4);
    transform: translateY(-50%) scale(0.95);
}

.sms-container {
    display: flex;
    gap: 12px;
    align-items: stretch;
}

.sms-container .form-input {
    flex: 1;
}

.sms-btn {
    background: var(--gradient-secondary);
    border: none;
    color: white;
    padding: 16px 20px;
    border-radius: 16px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-purple);
    position: relative;
    overflow: hidden;
}

.sms-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.sms-btn:hover::before {
    left: 100%;
}

.sms-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
}

.sms-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.forgot-password {
    text-align: right;
    margin-top: 8px;
}

.forgot-password a {
    color: var(--secondary-purple-light);
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.forgot-password a:hover {
    color: var(--secondary-purple);
    text-decoration: underline;
}

/* 密码校验样式 */
.validation-message {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    line-height: 1.4;
    display: flex;
    align-items: center;
    gap: 6px;
    animation: fadeIn 0.2s ease-out;
}

.validation-success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

.validation-error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #F44336;
}

/* 输入框状态样式 */
.input-success {
    border-color: #4CAF50 !important;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1) !important;
}

.input-error {
    border-color: #F44336 !important;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.1) !important;
}

.btn {
    display: inline-block;
    padding: 16px 24px;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    margin: 8px 0;
    width: 100%;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-gold);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(200, 134, 13, 0.4);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.divider {
    text-align: center;
    margin: 32px 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--neutral-600), transparent);
}

.divider span {
    background: var(--bg-primary);
    padding: 0 20px;
    color: var(--neutral-400);
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.prompt-card {
    text-align: center;
    padding: 24px 32px;
}

/* 协议勾选区域样式 */
.agreement-section {
    margin-bottom: 20px;
    text-align: left;
}

.agreement-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.5;
    color: var(--text-secondary);
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.checkbox-custom {
    position: relative;
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-light);
    border-radius: 4px;
    background: var(--bg-secondary);
    flex-shrink: 0;
    margin-top: 2px;
    transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-input:checked + .checkbox-custom::after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-input:focus + .checkbox-custom {
    box-shadow: 0 0 0 3px var(--primary-alpha);
}

.agreement-text {
    flex: 1;
    line-height: 1.6;
}

.agreement-text a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.agreement-text a:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

.register-prompt {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--border-light);
}

.prompt-card p {
    color: var(--neutral-400);
    font-size: 14px;
    margin-bottom: 12px;
}

.prompt-card a {
    color: var(--secondary-purple-light);
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
}

.prompt-card a:hover {
    color: var(--secondary-purple);
    text-decoration: underline;
}

/* 注册页面特定样式 */
.password-strength {
    margin-top: 8px;
    display: flex;
    gap: 4px;
    align-items: center;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    background: rgba(255, 255, 255, 0.1);
    flex: 1;
    transition: all 0.3s ease;
}

.strength-bar.active {
    background: var(--accent-green);
}

.strength-bar.medium {
    background: var(--accent-orange);
}

.strength-bar.weak {
    background: var(--accent-red);
}

.strength-text {
    font-size: 12px;
    color: var(--neutral-400);
    margin-left: 8px;
}

.error-message {
    color: var(--accent-red);
    font-size: 12px;
    margin-top: 6px;
    display: none;
}

.error-message.show {
    display: block;
}

.agreement {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin: 24px 0;
    font-size: 14px;
    color: var(--neutral-400);
}

.checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox.checked {
    background: var(--gradient-primary);
    border-color: var(--primary-gold);
}

.checkbox.checked::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.agreement a {
    color: var(--secondary-purple-light);
    text-decoration: none;
    transition: all 0.3s ease;
}

.agreement a:hover {
    color: var(--secondary-purple);
    text-decoration: underline;
}

/* 动画 */
@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .auth-container {
        padding: 15px;
    }
    
    .logo-section {
        padding: 30px 0 40px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .welcome-text {
        font-size: 20px;
    }
    
    .subtitle {
        font-size: 12px;
    }
    
    .auth-form {
        padding: 20px 15px;
    }
    
    .form-group {
        margin-bottom: 16px;
    }
    
    .btn {
        padding: 14px 20px;
        font-size: 15px;
    }
    
    .tab-switcher {
        margin-bottom: 20px;
    }
    
    .tab-btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .password-toggle {
        padding: 6px;
        min-width: 28px;
        min-height: 28px;
        font-size: 14px;
    }
    
    .password-input {
        padding-right: 44px;
    }
}

/* 针对更小屏幕的优化 */
@media (max-width: 375px) {
    .auth-container {
        padding: 12px;
    }
    
    .auth-form {
        padding: 16px;
        border-radius: 20px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
    }
    
    .welcome-text {
        font-size: 18px;
    }
}

/* 针对很高内容的处理 */
@media (max-height: 700px) {
    .logo-section {
        padding: 15px 0 25px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
        font-size: 24px;
        margin-bottom: 12px;
    }
    
    .welcome-text {
        font-size: 18px;
        margin-bottom: 4px;
    }
    
    .subtitle {
        font-size: 13px;
    }
    
    .auth-form {
        padding: 20px;
        margin-bottom: 16px;
    }
}

/* 键盘弹出时的特殊适配 */
@media (max-height: 600px) {
    .keyboard-visible .logo-section {
        padding: 10px 0 20px;
    }
    
    .keyboard-visible .logo {
        width: 50px;
        height: 50px;
        font-size: 20px;
        margin-bottom: 8px;
    }
    
    .keyboard-visible .welcome-text {
        font-size: 16px;
    }
    
    .keyboard-visible .subtitle {
        font-size: 11px;
    }
    
    .keyboard-visible .auth-form {
        padding: 20px;
        margin-bottom: 16px;
    }
    
    .keyboard-visible .form-group {
        margin-bottom: 16px;
    }
}

/* 极小屏幕或键盘弹出时的紧凑布局 */
@media (max-height: 500px) {
    .keyboard-visible .logo-section {
        padding: 5px 0 10px;
    }
    
    .keyboard-visible .logo {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-bottom: 6px;
    }
    
    .keyboard-visible .welcome-text {
        font-size: 14px;
        margin-bottom: 4px;
    }
    
    .keyboard-visible .subtitle {
        font-size: 10px;
    }
    
    .keyboard-visible .auth-form {
        padding: 16px;
        margin-bottom: 12px;
    }
    
    .keyboard-visible .form-group {
        margin-bottom: 12px;
    }
    
    .keyboard-visible .form-input {
        padding: 12px 16px;
    }
} 