<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>发行平台</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #e0e0e0;
            font-size: 14px;
            line-height: 1.6;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 480px;
            margin: 0 auto;
            padding: 0 12px 30px;
        }
        
        /* 顶部导航 */
        .app-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 0 15px;
            position: sticky;
            top: 0;
            z-index: 100;
            background: rgba(15, 15, 15, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid #222;
        }
        
        .app-title {
            font-size: 22px;
            font-weight: 700;
            color: #fff;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .app-title i {
            color: #4dabf7;
            font-size: 24px;
        }
        
        .user-actions {
            display: flex;
            gap: 16px;
        }
        
        .user-actions i {
            font-size: 18px;
            color: #aaa;
            transition: all 0.3s;
        }
        
        .user-actions i:hover {
            color: #4dabf7;
            transform: scale(1.1);
        }
        
        /* 倒计时卡�?*/
        .countdown-card {
            background: linear-gradient(135deg, #1a2b3c, #0d1a26);
            margin: 15px 0;
            border-radius: 18px;
            padding: 22px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0, 40, 80, 0.4);
            border: 1px solid rgba(77, 171, 247, 0.25);
            position: relative;
            overflow: hidden;
        }
        
        .countdown-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(77, 171, 247, 0.1) 0%, transparent 70%);
            z-index: 0;
        }
        
        .countdown-title {
            font-size: 18px;
            font-weight: bold;
            color: #4dabf7;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }
        
        .countdown-timer {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 32px;
            font-weight: bold;
            color: #fff;
            position: relative;
            z-index: 1;
            letter-spacing: 1px;
            font-family: 'Courier New', monospace;
            text-shadow: 0 0 15px rgba(77, 171, 247, 0.7);
            margin: 10px 0;
        }
        
        .countdown-timer span {
            display: inline-block;
            min-width: 44px;
            text-align: center;
            background: rgba(0, 30, 60, 0.7);
            padding: 6px;
            border-radius: 8px;
            margin: 0 3px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .countdown-label {
            font-size: 14px;
            color: #4dabf7;
            margin-top: 5px;
            position: relative;
            z-index: 1;
        }
        
        /* 商品列表 */
        .section-title {
            font-size: 20px;
            font-weight: bold;
            padding: 25px 0 18px;
            color: #fff;
            position: relative;
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        
        .section-title::before {
            content: '';
            display: inline-block;
            width: 5px;
            height: 20px;
            background: linear-gradient(to bottom, #4dabf7, #1890ff);
            border-radius: 3px;
            margin-right: 12px;
        }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 14px;
            margin-bottom: 10px;
        }
        
        .product-card {
            background: linear-gradient(145deg, #1e1e1e, #252525);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
            border: 1px solid #333;
            position: relative;
            display: flex;
            flex-direction: column;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(77, 171, 247, 0.4);
            border-color: rgba(77, 171, 247, 0.5);
        }
        
        .product-image {
            width: 100%;
            height: 160px;
            position: relative;
            overflow: hidden;
        }
        
        .product-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }
        
        .product-card:hover .product-image img {
            transform: scale(1.08);
        }
        
        .product-tag {
            position: absolute;
            top: 10px;
            left: 10px;
            background: #ff4d4f;
            color: #fff;
            font-size: 12px;
            padding: 4px 10px;
            border-radius: 20px;
            font-weight: 500;
            z-index: 2;
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }
        
        .product-info {
            flex: 1;
            padding: 14px;
            display: flex;
            flex-direction: column;
        }
        
        .product-series {
            font-size: 12px;
            color: #8a8a8a;
            margin-bottom: 6px;
        }
        
        .product-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #fff;
            line-height: 1.4;
        }
        
        .product-theme {
            font-size: 13px;
            color: #aaa;
            margin-bottom: 10px;
            flex-grow: 1;
        }
        
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 12px;
        }
        
        .tag {
            font-size: 11px;
            padding: 4px 10px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .tag.original {
            background: rgba(77, 171, 247, 0.2);
            color: #4dabf7;
            border: 1px solid rgba(77, 171, 247, 0.4);
        }
        
        .tag.priority {
            background: rgba(250, 140, 22, 0.2);
            color: #fa8c16;
            border: 1px solid rgba(250, 140, 22, 0.4);
        }
        
        .tag.whale {
            background: rgba(82, 196, 26, 0.2);
            color: #52c41a;
            border: 1px solid rgba(82, 196, 26, 0.4);
        }
        
        .product-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 12px;
            color: #8a8a8a;
        }
        
        .price {
            font-size: 18px;
            font-weight: bold;
            color: #ff6b6b;
            display: flex;
            align-items: center;
            margin-top: 5px;
        }
        
        .sale-time {
            font-size: 12px;
            color: #aaa;
            background: rgba(100, 100, 100, 0.3);
            padding: 4px 10px;
            border-radius: 12px;
            margin-left: auto;
        }
        
        .badge {
            display: inline-block;
            padding: 3px 8px;
            background: #ff4d4f;
            color: white;
            font-size: 10px;
            border-radius: 10px;
            margin-left: 6px;
            vertical-align: middle;
        }
        
        .footer {
            text-align: center;
            padding: 25px 0 15px;
            color: #666;
            font-size: 13px;
            margin-top: 20px;
            border-top: 1px solid #222;
        }
        
        .footer p {
            margin: 5px 0;
        }
        
        /* 响应式调�?*/
        @media (max-width: 380px) {
            .product-grid {
                gap: 10px;
            }
            
            .product-image {
                height: 140px;
            }
            
            .countdown-timer {
                font-size: 28px;
            }
            
            .countdown-timer span {
                min-width: 36px;
            }
        }
        
        /* 加载动画 */
        @keyframes pulse {
            0% { transform: scale(0.95); opacity: 0.7; }
            50% { transform: scale(1); opacity: 1; }
            100% { transform: scale(0.95); opacity: 0.7; }
        }
        
        .loading {
            animation: pulse 1.5s infinite;
            text-align: center;
            padding: 30px;
            color: #4dabf7;
            font-size: 15px;
        }
        
        /* 日期筛�?*/
        .date-filter {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            overflow-x: auto;
            padding: 5px 0 10px;
            scrollbar-width: none;
        }
        
        .date-filter::-webkit-scrollbar {
            display: none;
        }
        
        .date-item {
            flex: 0 0 auto;
            padding: 10px 16px;
            background: rgba(40, 40, 40, 0.7);
            border-radius: 12px;
            text-align: center;
            border: 1px solid #333;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .date-item.active {
            background: rgba(77, 171, 247, 0.2);
            border-color: rgba(77, 171, 247, 0.5);
            color: #4dabf7;
        }
        
        .date-item .day {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .date-item .date {
            font-size: 13px;
            color: #aaa;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航 -->
        <div class="app-bar">
            <div class="app-title">
                <i class="fas fa-calendar-star"></i>
                <span>资产</span>
            </div>
            <div class="user-actions">
                <i class="fas fa-search"></i>
                <i class="fas fa-bell"></i>
                <i class="fas fa-user-circle"></i>
            </div>
        </div>
        
        <!-- 倒计时卡�?-->

        <div class="section-title">今日发售</div>
        
        <!-- 商品网格 -->
        <div class="product-grid">
            <!-- 商品卡片1 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="兆域�?>
                    <div class="product-tag">限量</div>
                </div>
                <div class="product-info">
                    <div class="product-name">比例尺制图的先驱</div>
                    <div class="tags">
                        <span class="tag original">原创设计</span>
                        <span class="tag priority">优先�?/span>
                    </div>
                    <div class="product-stats">
                        <div>限量8000�?/div>
                    </div>
                    <div class="price">
                        �?0.00
                        <span class="sale-time">12:00开�?/span>
                    </div>
                </div>
            </div>
            
            <!-- 商品卡片2 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1470225620780-dba8ba36b745?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="九天音乐">
                    <div class="product-tag">热门</div>
                </div>
                <div class="product-info">
                    <div class="product-name">只因流浪·九天音乐TING</div>
                    <div class="tags">
                        <span class="tag whale">精�?/span>
                        <span class="tag priority">优先�?/span>
                    </div>
                    <div class="product-stats">
                        <div>限量8000�?/div>
                    </div>
                    <div class="price">
                        �?0.00
                        <span class="sale-time">14:00开�?/span>
                    </div>
                </div>
            </div>
            
            <!-- 商品卡片3 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1552422535-c45813c61732?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="非遗">
                    <div class="product-tag">新品</div>
                </div>
                <div class="product-info">
                    <div class="product-name">错金银铜版兆域图 </div>
                    <div class="tags">
                        <span class="tag whale">精�?/span>
                        <span class="tag priority">优先�?/span>
                    </div>
                    <div class="product-stats">
                        <div>限量8000�?/div>
                    </div>
                    <div class="price">
                        �?0.00
                        <span class="sale-time">14:00开�?/span>
                    </div>
                </div>
            </div>
            
            <!-- 商品卡片4 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1563089145-599997674d42?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="敦煌">
                    <div class="product-tag">推荐</div>
                </div>
                <div class="product-info">
                    <div class="product-name">敦煌飞天 · 数字壁画</div>
                    <div class="tags">
                        <span class="tag whale">精�?/span>
                        <span class="tag original">原创设计</span>
                    </div>
                    <div class="product-stats">
                        <div>限量10000�?/div>
                    </div>
                    <div class="price">
                        �?5.00
                        <span class="sale-time">10:00开�?/span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section-title">即将上线</div>
        
        <div class="product-grid">
            <!-- 商品卡片5 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1579546929662-711aa81148cf?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="关山�?>
                    <div class="product-tag">热卖</div>
                </div>
                <div class="product-info">
                    <div class="product-name">关山�?/div>
                    <div class="tags">
                        <span class="tag original">原创设计</span>
                        <span class="tag priority">优先�?/span>
                    </div>
                    <div class="product-stats">
                        <div>限量5000�?/div>
                    </div>
                    <div class="price">
                        �?5.00
                        <span class="sale-time">12:00开�?/span>
                    </div>
                </div>
            </div>
            
            <!-- 商品卡片6 -->
            <div class="product-card">
                <div class="product-image">
                    <img src="https://images.unsplash.com/photo-1533738363-b7f9aef128ce?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="神蕉侠侣">
                    <div class="product-tag">新品</div>
                </div>
                <div class="product-info">
                    <div class="product-name">赛柏蕉子：神蕉侠侣系列盲�?/div>
                    <div class="tags">
                        <span class="tag priority">优先�?/span>
                    </div>
                    <div class="product-stats">
                        <div>限量12000�?/div>
                    </div>
                    <div class="price">
                        �?0.00
                        <span class="sale-time">11:00开�?/span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="section-title">往期发�?/div>
        
        <div class="loading">
            <i class="fas fa-spinner fa-spin"></i> 更多精彩商品即将上线...
        </div>
        
        <div class="footer">
            <p>© 2025 数字资产发行平台</p>
        </div>
    </div>

    <script>
        // 倒计时效�?        function updateCountdown() {
            const timerElement = document.querySelector('.countdown-timer');
            const spans = timerElement.querySelectorAll('span');
            
            // 获取当前时间
            const now = new Date();
            
            // 设置开售时间为当前时间+28小时56�?5�?            const targetTime = new Date(now.getTime() + (28 * 60 * 60 * 1000) + (56 * 60 * 1000) + (15 * 1000));
            
            // 计算时间�?            const diff = targetTime - now;
            
            if (diff <= 0) {
                timerElement.innerHTML = '<span>00</span>:<span>00</span>:<span>00</span>';
                return;
            }
            
            // 计算小时、分钟、秒
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);
            
            // 更新显示
            spans[0].textContent = hours.toString().padStart(2, '0');
            spans[1].textContent = minutes.toString().padStart(2, '0');
            spans[2].textContent = seconds.toString().padStart(2, '0');
        }
        
        // 商品卡片悬停效果
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // 日期筛选效�?        const dateItems = document.querySelectorAll('.date-item');
        dateItems.forEach(item => {
            item.addEventListener('click', function() {
                dateItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 初始调用并设置定时器
        updateCountdown();
        setInterval(updateCountdown, 1000);
    </script>
</body>
</html>
