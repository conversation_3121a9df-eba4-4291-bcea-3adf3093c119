<template>
  <AppPageHeader :title="'注册'" @back="$router.back()" />
  <div class="auth-page" :class="{ 'keyboard-visible': isKeyboardVisible }">
    <div class="auth-container">
      <!-- Logo区域 -->
      <div class="logo-section" @click="goBack">
        <!-- <div class="logo">
                      <i class="fas fa-sun" style="font-size: 32px;"></i>
        </div> -->
        <div class="welcome-text">凌云数资</div>
        <div class="subtitle">注册您的数字资产账户</div>
      </div>

      <!-- 注册表单 -->
      <div class="auth-form">
        <div class="form-group">
          <label class="form-label">
                          <i class="fas fa-mobile"></i>
              手机号码
          </label>
          <div class="input-wrapper">
            <input
              type="tel"
              class="form-input"
              :class="{ error: errors.phoneNumber }"
              placeholder="请输入手机号码"
              maxlength="11"
              v-model="registerForm.phoneNumber"
              @input="formatPhoneNumber"
              @blur="validatePhoneNumber"
              required
            >
            <div class="error-message" :class="{ show: errors.phoneNumber }">
              {{ errors.phoneNumber }}
            </div>
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
                          <i class="fas fa-shield"></i>
              短信验证码
          </label>
          <div class="sms-container">
            <input
              type="text"
              class="form-input"
              :class="{ error: errors.smsCode }"
              placeholder="请输入验证码"
              maxlength="6"
              v-model="registerForm.smsCode"
              @input="formatSmsCode"
              @blur="validateSmsCode"
              required
            >
            <button
              type="button"
              class="sms-btn"
              :disabled="smsCountdown > 0"
              @click="sendSMS"
            >
              {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '获取验证码' }}
            </button>
          </div>
          <div class="error-message" :class="{ show: errors.smsCode }">
            {{ errors.smsCode }}
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-lock"></i>
            设置密码
          </label>
          <div class="input-wrapper password-input-wrapper">
            <input
              :type="showPassword ? 'text' : 'password'"
              class="form-input password-input"
              :class="{ error: errors.password }"
              placeholder="8-16位，包含大小写字母和数字"
              v-model="registerForm.password"
              @input="onPasswordInput"
              @blur="validatePassword"
              @focus="handleInputFocus"
              @input.capture="handleInputChange"
              required
            >
            <button
              type="button"
              class="password-toggle"
              @click="togglePassword('password')"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <div class="password-strength">
            <div
              class="strength-bar"
              v-for="(bar, index) in strengthBars"
              :key="index"
              :class="bar.class"
            ></div>
            <span class="strength-text">{{ strengthText }}</span>
          </div>
          <div class="error-message" :class="{ show: errors.password }">
            {{ errors.password }}
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">
            <i class="fas fa-lock"></i>
            确认密码
          </label>
          <div class="input-wrapper password-input-wrapper">
            <input
              :type="showConfirmPassword ? 'text' : 'password'"
              class="form-input password-input"
              :class="{ error: errors.confirmPassword }"
              placeholder="请再次输入密码"
              v-model="registerForm.confirmPassword"
              @blur="validateConfirmPassword"
              @focus="handleInputFocus"
              @input.capture="handleInputChange"
              required
            >
            <button
              type="button"
              class="password-toggle"
              @click="togglePassword('confirmPassword')"
            >
              <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <div class="error-message" :class="{ show: errors.confirmPassword }">
            {{ errors.confirmPassword }}
          </div>
        </div>

        <div class="agreement">
          <div
            class="checkbox"
            :class="{ checked: isAgreed }"
            @click="toggleAgreement"
          ></div>
          <div>
            我已阅读并同意
            <a href="#" @click.prevent="showUserAgreement">《用户服务协议》</a>
            和
            <a href="#" @click.prevent="showPrivacyPolicy">《隐私政策》</a>
          </div>
        </div>

        <button
          type="button"
          class="btn btn-primary"
          :disabled="isRegistering"
          @click="handleRegister"
        >
                      <span v-if="isRegistering"><i class="fas fa-spinner fa-spin"></i></span><span v-else><i class="fas fa-plus"></i></span>
          {{ isRegistering ? '注册中...' : '立即注册' }}
        </button>
      </div>

      <!-- 登录提示 -->
      <div class="prompt-card">
        <p>已有账户？</p>
        <router-link to="/login">立即登录 →</router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useKeyboardAdaptation } from '@/composables/useKeyboardAdaptation'
import { useNotification } from '@/composables/useNotification'
import AuthAPI from '@/api/auth'
import { RequestError } from '@/api/request'
import type { RegisterRequest } from '@/api/types'
import AppPageHeader from '@/components/AppPageHeader.vue'
import { validatePhoneNumber as validatePhone, detectVirtualOperator, getVirtualOperatorInfo } from '@/utils/phoneValidator'

// 响应式数据
const router = useRouter()

// 启用键盘适配
const {
  isKeyboardVisible,
  keyboardHeight,
  scrollToElement,
  scrollToActiveElement,
  getSafeAreaHeight
} = useKeyboardAdaptation({
  extraOffset: 30,
  smoothScroll: true,
  scrollDelay: 300,
  adjustViewport: true
})

// 通知系统
const { success, error, info } = useNotification()
const smsCountdown = ref(0)
const isRegistering = ref(false)
const showPassword = ref(false)
const showConfirmPassword = ref(false)
const isAgreed = ref(false)


// 表单数据
const registerForm = reactive({
  phoneNumber: '',
  smsCode: '',
  password: '',
  confirmPassword: ''
})

// 表单验证错误
const errors = reactive({
  phoneNumber: '',
  smsCode: '',
  password: '',
  confirmPassword: ''
})

// 密码强度
const passwordStrength = ref(0)

// 倒计时定时器
let countdownTimer: number | null = null

/**
 * 密码强度条
 */
const strengthBars = computed(() => {
  const bars = []
  for (let i = 0; i < 4; i++) {
    let className = ''
    if (i < passwordStrength.value) {
      if (passwordStrength.value <= 1) className = 'weak'
      else if (passwordStrength.value <= 2) className = 'medium'
      else className = 'active'
    }
    bars.push({ class: className })
  }
  return bars
})

/**
 * 密码强度文本
 */
const strengthText = computed(() => {
  if (!registerForm.password) return '密码强度'
  if (passwordStrength.value === 0) return '不符合要求'

  const texts = ['较弱', '一般', '较强', '很强']
  return texts[passwordStrength.value - 1] || '较弱'
})



/**
 * 格式化手机号输入
 */
const formatPhoneNumber = () => {
  registerForm.phoneNumber = registerForm.phoneNumber.replace(/\D/g, '')
  if (errors.phoneNumber) {
    errors.phoneNumber = ''
  }
}

/**
 * 格式化验证码输入
 */
const formatSmsCode = () => {
  registerForm.smsCode = registerForm.smsCode.replace(/\D/g, '')
  if (errors.smsCode) {
    errors.smsCode = ''
  }
}

/**
 * 密码输入处理
 */
const onPasswordInput = () => {
  checkPasswordStrength(registerForm.password)
  if (errors.password) {
    errors.password = ''
  }
  // 如果确认密码已经填写，重新验证确认密码
  if (registerForm.confirmPassword) {
    validateConfirmPassword()
  }
}

/**
 * 检查密码强度
 */
const checkPasswordStrength = (password: string) => {
  let strength = 0

  // 基础长度要求（8位以上）
  if (password.length >= 8) strength++

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength++

  // 包含小写字母
  if (/[a-z]/.test(password)) strength++

  // 包含数字
  if (/[0-9]/.test(password)) strength++

  // 额外加分：长度更长或包含特殊字符
  if (password.length >= 12) strength = Math.min(4, strength + 0.5)
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength = Math.min(4, strength + 0.5)

  passwordStrength.value = Math.floor(strength)
}

/**
 * 切换密码显示状态
 */
const togglePassword = (type: 'password' | 'confirmPassword') => {
  if (type === 'password') {
    showPassword.value = !showPassword.value
  } else {
    showConfirmPassword.value = !showConfirmPassword.value
  }
}

/**
 * 切换协议同意状态
 */
const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value
}

/**
 * 验证手机号（包含虚拟运营商检测）
 */
const validatePhoneNumber = () => {
  const result = validatePhone(registerForm.phoneNumber, false) // 不允许虚拟运营商

  if (!result.isValid) {
    errors.phoneNumber = result.error || '手机号码验证失败'

    // 如果是虚拟运营商，提供详细信息
    if (result.virtualInfo?.isVirtual) {
      const detailInfo = getVirtualOperatorInfo(registerForm.phoneNumber)
      console.log('虚拟运营商检测:', detailInfo)
    }

    return false
  }

  errors.phoneNumber = ''
  return true
}

/**
 * 验证验证码
 */
const validateSmsCode = () => {
  if (!registerForm.smsCode) {
    errors.smsCode = '请输入验证码'
    return false
  }
  if (registerForm.smsCode.length !== 6) {
    errors.smsCode = '请输入6位验证码'
    return false
  }
  errors.smsCode = ''
  return true
}

/**
 * 验证密码
 */
const validatePassword = () => {
  if (!registerForm.password) {
    errors.password = '请设置密码'
    return false
  }

  // 长度校验：8-16位
  if (registerForm.password.length < 8 || registerForm.password.length > 16) {
    errors.password = '密码长度必须为8-16位'
    return false
  }

  // 大写字母校验
  if (!/[A-Z]/.test(registerForm.password)) {
    errors.password = '密码必须包含大写字母'
    return false
  }

  // 小写字母校验
  if (!/[a-z]/.test(registerForm.password)) {
    errors.password = '密码必须包含小写字母'
    return false
  }

  // 数字校验
  if (!/[0-9]/.test(registerForm.password)) {
    errors.password = '密码必须包含数字'
    return false
  }

  errors.password = ''
  return true
}

/**
 * 验证确认密码
 */
const validateConfirmPassword = () => {
  if (!registerForm.confirmPassword) {
    errors.confirmPassword = '请确认密码'
    return false
  }
  if (registerForm.password !== registerForm.confirmPassword) {
    errors.confirmPassword = '两次密码输入不一致'
    return false
  }
  errors.confirmPassword = ''
  return true
}

/**
 * 验证表单
 */
const validateForm = () => {
  const phoneValid = validatePhoneNumber()
  const smsValid = validateSmsCode()
  const passwordValid = validatePassword()
  const confirmPasswordValid = validateConfirmPassword()

  if (!isAgreed.value) {
    error('请阅读并同意用户协议')
    return false
  }

  return phoneValid && smsValid && passwordValid && confirmPasswordValid
}

/**
 * 发送短信验证码（包含虚拟运营商检测）
 */
const sendSMS = async () => {
  if (!registerForm.phoneNumber) {
    error('请先输入手机号码')
    return
  }

  // 验证手机号（包含虚拟运营商检测）
  if (!validatePhoneNumber()) {
    // 如果是虚拟运营商，显示详细提示
    const virtualInfo = detectVirtualOperator(registerForm.phoneNumber)
    if (virtualInfo.isVirtual) {
      const detailInfo = getVirtualOperatorInfo(registerForm.phoneNumber)
      error(`${errors.phoneNumber}\n\n${detailInfo}`, 8000)
    }
    return
  }

  if (smsCountdown.value > 0) return

  try {
    // 调用真实API发送短信
    await AuthAPI.sendSmsCode({
      phone: registerForm.phoneNumber,
      scene: 'H5register'
    })

    success(`验证码已发送到手机号 ${registerForm.phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}，请注意查收！`, 5000)

    // 额外提示验证码有效期
    setTimeout(() => {
      info('验证码有效期为5分钟，请尽快输入', 3000)
    }, 2000)

    // 开始倒计时
    smsCountdown.value = 60
    countdownTimer = setInterval(() => {
      if (smsCountdown.value <= 0) {
        if (countdownTimer) {
          clearInterval(countdownTimer)
          countdownTimer = null
        }
      } else {
        smsCountdown.value--
      }
    }, 1000)
  } catch (err) {
    console.error('发送验证码失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('发送验证码失败，请稍后重试')
    }
  }
}

/**
 * 处理注册
 */
const handleRegister = async () => {
  if (!validateForm()) return

  isRegistering.value = true

  try {
    // 调用真实API进行注册
    await AuthAPI.register({
      username: registerForm.phoneNumber, // 使用手机号作为用户名
      password: registerForm.password,
      phone: registerForm.phoneNumber,
      smsCode: registerForm.smsCode
    })

    success('注册成功！即将跳转到登录页面')
    sessionStorage.removeItem('registerForm') // 注册成功后清理

    // 注册成功后跳转到登录页面
    setTimeout(() => {
      router.push('/login')
    }, 1500)
  } catch (err) {
    console.error('注册失败:', err)
    if (err instanceof RequestError) {
      error(err.message)
    } else {
      error('注册失败，请稍后重试')
    }
  } finally {
    isRegistering.value = false
  }
}

/**
 * 显示用户协议
 */
const saveFormToSession = () => {
  sessionStorage.setItem('registerForm', JSON.stringify(registerForm))
  console.log('保存registerForm', JSON.stringify(registerForm))
}

const restoreFormFromSession = () => {
  const data = sessionStorage.getItem('registerForm')
  console.log('还原registerForm', data)
  if (data) {
    try {
      const parsed = JSON.parse(data)
      Object.assign(registerForm, parsed)
      console.log('还原registerForm成功', parsed)
    } catch (e) {
      console.error('还原registerForm失败', e)
    }
  }
}

/**
 * 显示用户协议
 */
const showUserAgreement = () => {
  saveFormToSession()
  router.push('/user-agreement')
}

/**
 * 显示隐私政策
 */
const showPrivacyPolicy = () => {
  saveFormToSession()
  router.push('/privacy-policy')
}

/**
 * 返回首页
 */
const goBack = () => {
  router.push('/')
}

/**
 * 处理输入框焦点 - 确保在键盘弹出时能看到输入框
 */
const handleInputFocus = (event: FocusEvent) => {
  const target = event.target as HTMLElement

  // 延迟滚动，等待键盘弹出动画完成
  setTimeout(() => {
    scrollToElement(target)
  }, 100)

  // 如果是密码输入框，额外处理
  if (target instanceof HTMLInputElement) {
    if (target.type === 'password' || target.placeholder.includes('密码')) {
      setTimeout(() => {
        scrollToElement(target)
      }, 400)
    }
  }
}

/**
 * 处理输入框内容变化 - 确保在输入时输入框可见
 */
const handleInputChange = (event: Event) => {
  const target = event.target as HTMLElement

  // 如果键盘已经弹出，确保输入框仍然可见
  if (isKeyboardVisible.value) {
    nextTick(() => {
      scrollToElement(target)
    })
  }
}



// 生命周期钩子
onMounted(() => {
  console.log('RegisterView mounted')
  restoreFormFromSession()
})

onUnmounted(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
  // 不再清理sessionStorage，只有注册成功后才清理
})
</script>

<style scoped>
@import '@/assets/styles/auth.css';

/* CSS变量定义 - 确保在scoped样式中生效 */
.auth-page {
  --primary-color: #d4a574;
  --primary-light: #e8c49a;
  --primary-dark: #b8956a;
}

/* 强制滚动修复 - 使用最高优先级 */
:deep(.auth-page) {
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh !important;
  position: relative !important;
  max-height: none !important;
}

:deep(.auth-container) {
  min-height: 100vh !important;
  padding-bottom: 40px !important;
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
}

/* 确保表单内容可以滚动 */
:deep(.auth-form) {
  overflow: visible !important;
  position: relative !important;
  height: auto !important;
  max-height: none !important;
}

/* 覆盖auth.css中的overflow: hidden */
:deep(.logo) {
  overflow: visible !important;
}

:deep(.tab-switcher) {
  overflow: visible !important;
}

:deep(.btn) {
  overflow: visible !important;
}

/* 确保密码切换按钮正常工作 */
.password-toggle {
  position: absolute !important;
  right: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  background: rgba(212, 165, 116, 0.15) !important;
  border: 1px solid rgba(212, 165, 116, 0.3) !important;
  color: #d4a574 !important;
  cursor: pointer !important;
  font-size: 14px !important;
  padding: 6px !important;
  border-radius: 6px !important;
  transition: all 0.3s ease !important;
  z-index: 100 !important;
  width: 32px !important;
  height: 32px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  pointer-events: auto !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.password-toggle:hover {
  background: rgba(212, 165, 116, 0.25) !important;
  border-color: rgba(212, 165, 116, 0.5) !important;
  color: #e8c49a !important;
}

.password-toggle:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(212, 165, 116, 0.3) !important;
}

.password-toggle:active {
  transform: translateY(-50%) scale(0.95) !important;
}

/* 确保输入框容器可以正常滚动 */
:deep(.input-wrapper) {
  position: relative !important;
  overflow: visible !important;
}

:deep(.password-input-wrapper) {
  position: relative !important;
  overflow: visible !important;
}

/* 全局强制滚动修复 */
:deep(*) {
  overflow: visible !important;
}

:deep(.auth-page) {
  overflow-y: auto !important;
}

/* 确保body和html可以滚动 */
:deep(body) {
  overflow-y: auto !important;
  height: auto !important;
  min-height: 100vh !important;
}

:deep(html) {
  overflow-y: auto !important;
  height: auto !important;
}
</style>
