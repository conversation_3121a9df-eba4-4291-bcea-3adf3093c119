<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的资产 - 四川省数字资产发行平�?/title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        /* 通用样式 */
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 12px;
        }
        
        .text-large {
            font-size: 22px;
            font-weight: 700;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
            margin: 8px 0;
        }
        
        .text-normal {
            font-size: 15px;
            color: #f0e0d0;
            margin: 6px 0;
        }
        
        .text-small {
            font-size: 12px;
            color: #d4af37;
            margin: 6px 0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            margin: 4px;
            text-decoration: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            box-shadow: 0 3px 10px rgba(218, 165, 32, 0.4);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.6);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(80, 60, 40, 0.7);
            border-color: #daa520;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: rgba(218, 165, 32, 0.2);
            color: #daa520;
            border: 1px solid rgba(218, 165, 32, 0.4);
        }
        
        .tag-accent {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
            border: 1px solid rgba(178, 34, 34, 0.4);
        }
        
        .tag-success {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
            border: 1px solid rgba(143, 188, 143, 0.4);
        }
        
        .product-card {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
            border: 1px solid rgba(218, 165, 32, 0.3);
            cursor: pointer;
            position: relative;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 18px rgba(218, 165, 32, 0.4);
            border-color: rgba(218, 165, 32, 0.5);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, #2a1c1c, #3a2a2a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #daa520;
            font-size: 13px;
        }
        
        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #daa520;
            color: #2a1616;
            font-size: 11px;
            padding: 3px 8px;
            border-radius: 16px;
            font-weight: 500;
            z-index: 2;
        }
        
        .asset-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            flex-direction: column;
            gap: 4px;
            z-index: 3;
        }
        
        .action-btn {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: all 0.3s;
            background: rgba(0, 0, 0, 0.6);
            color: #f8f0e0;
            backdrop-filter: blur(5px);
        }
        
        .action-btn:hover {
            transform: scale(1.1);
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 15px;
            font-weight: bold;
            margin-bottom: 6px;
            color: #f8f0e0;
            line-height: 1.4;
        }
        
        .asset-date {
            font-size: 11px;
            color: #d4af37;
            margin-bottom: 6px;
        }
        
        .current-value {
            font-size: 14px;
            font-weight: bold;
            color: #8fbc8f;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .value-change {
            font-size: 11px;
            padding: 2px 6px;
            border-radius: 8px;
        }
        
        .value-up {
            background: rgba(143, 188, 143, 0.2);
            color: #8fbc8f;
        }
        
        .value-down {
            background: rgba(178, 34, 34, 0.2);
            color: #b22222;
        }
        
        /* 页面特定样式 */
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .header-actions {
            display: flex;
            gap: 8px;
        }
        
        .stats-section {
            margin: 16px 0;
            padding: 16px;
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
            text-align: center;
        }
        
        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: #daa520;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
        }
        
        .search-section {
            margin: 16px 0;
        }
        
        .search-container {
            display: flex;
            background: rgba(60, 40, 30, 0.7);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: #f0e0d0;
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: rgba(240, 224, 208, 0.5);
        }
        
        .search-btn {
            background: linear-gradient(135deg, #daa520, #b8860b);
            border: none;
            color: #2a1616;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
        }
        
        .filters-section {
            margin: 16px 0;
        }
        
        .filters-scroll {
            overflow-x: auto;
            padding: 8px 0;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .filters-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .filters-list {
            display: flex;
            gap: 12px;
            padding: 0 4px;
            min-width: max-content;
        }
        
        .filter-tag {
            padding: 8px 16px;
            border-radius: 16px;
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .filter-tag.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            border-color: #daa520;
            box-shadow: 0 3px 8px rgba(218, 165, 32, 0.4);
        }
        
        .assets-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0 16px;
        }
        
        .assets-count {
            font-size: 14px;
            color: #daa520;
            font-weight: 600;
        }
        
        .sort-btn {
            background: rgba(60, 40, 30, 0.7);
            border: 1px solid rgba(218, 165, 32, 0.3);
            color: #f0e0d0;
            padding: 6px 12px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .assets-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            color: rgba(218, 165, 32, 0.5);
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #f0e0d0;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 24px;
        }
        
        .load-more {
            text-align: center;
            margin: 24px 0;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">我的资产</h1>
                <div class="header-actions">
                    <button class="btn btn-small btn-secondary" onclick="exportAssets()">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="btn btn-small btn-secondary" onclick="showMenu()">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 资产统计 -->
            <section class="stats-section">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-assets">8</div>
                        <div class="stat-label">总资�?/div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="total-value">�?,287</div>
                        <div class="stat-label">总价�?/div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="today-change">+12.5%</div>
                        <div class="stat-label">今日涨幅</div>
                    </div>
                </div>
            </section>

            <!-- 搜索�?-->
            <section class="search-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索我的资产..." id="search-input">
                    <button class="search-btn" onclick="searchAssets()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 筛选标�?-->
            <section class="filters-section">
                <div class="filters-scroll">
                    <div class="filters-list">
                        <button class="filter-tag active" data-filter="all">全部</button>
                        <button class="filter-tag" data-filter="ancient-shu">古蜀文明</button>
                        <button class="filter-tag" data-filter="sanxingdui">三星�?/button>
                        <button class="filter-tag" data-filter="jinsha">金沙遗址</button>
                        <button class="filter-tag" data-filter="shu-brocade">蜀锦文�?/button>
                        <button class="filter-tag" data-filter="rising">涨幅�?/button>
                    </div>
                </div>
            </section>

            <!-- 资产列表头部 -->
            <section class="assets-header">
                <div class="assets-count" id="assets-count">�?项资�?/div>
                <button class="sort-btn" onclick="toggleSort()">
                    <span id="sort-text">按价�?/span>
                    <i class="fas fa-chevron-down" id="sort-icon"></i>
                </button>
            </section>

            <!-- 资产网格 -->
            <section class="assets-grid" id="assets-grid">
                <!-- 资产卡片 -->
                <div class="product-card" data-category="ancient-shu" data-value="299" data-purchase-date="2024-01-15">
                    <div class="product-image">
                        <div>太阳神鸟</div>
                        <div class="product-tag">古蜀文明</div>
                    </div>
                    <div class="asset-actions">
                        <button class="action-btn" onclick="shareAsset(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <button class="action-btn" onclick="transferAsset(this)" title="转赠">
                            <i class="fas fa-gift"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="asset-date">获得�?2024-01-15</div>
                        <div class="product-name">金沙太阳神鸟金饰</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">限量</span>
                            <span class="tag tag-accent">稀�?/span>
                        </div>
                        <div class="current-value">
                            �?99.00
                            <span class="value-change value-up">+5.2%</span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="sanxingdui" data-value="199" data-purchase-date="2024-01-12">
                    <div class="product-image">
                        <div>青铜面具</div>
                        <div class="product-tag">三星�?/div>
                    </div>
                    <div class="asset-actions">
                        <button class="action-btn" onclick="shareAsset(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <button class="action-btn" onclick="transferAsset(this)" title="转赠">
                            <i class="fas fa-gift"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="asset-date">获得�?2024-01-12</div>
                        <div class="product-name">三星堆青铜面�?/div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">考古发现</span>
                            <span class="tag tag-success">500�?/span>
                        </div>
                        <div class="current-value">
                            �?99.00
                            <span class="value-change value-up">+8.1%</span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="jinsha" data-value="399" data-purchase-date="2024-01-10">
                    <div class="product-image">
                        <div>金沙遗址</div>
                        <div class="product-tag">金沙遗址</div>
                    </div>
                    <div class="asset-actions">
                        <button class="action-btn" onclick="shareAsset(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <button class="action-btn" onclick="transferAsset(this)" title="转赠">
                            <i class="fas fa-gift"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="asset-date">获得�?2024-01-10</div>
                        <div class="product-name">金沙遗址3D全景</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">3D模型</span>
                            <span class="tag tag-accent">珍藏�?/span>
                        </div>
                        <div class="current-value">
                            �?99.00
                            <span class="value-change value-up">+15.2%</span>
                        </div>
                    </div>
                </div>

                <div class="product-card" data-category="shu-brocade" data-value="89" data-purchase-date="2024-01-08">
                    <div class="product-image">
                        <div>蜀锦纹�?/div>
                        <div class="product-tag">蜀锦文�?/div>
                    </div>
                    <div class="asset-actions">
                        <button class="action-btn" onclick="shareAsset(this)" title="分享">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <button class="action-btn" onclick="transferAsset(this)" title="转赠">
                            <i class="fas fa-gift"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <div class="asset-date">获得�?2024-01-08</div>
                        <div class="product-name">蜀锦织品纹样集</div>
                        <div style="margin: 6px 0;">
                            <span class="tag tag-primary">传统工艺</span>
                            <span class="tag tag-success">800�?/span>
                        </div>
                        <div class="current-value">
                            �?9.00
                            <span class="value-change value-down">-2.1%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 空状�?-->
            <div class="empty-state" id="empty-state" style="display: none;">
                <div class="empty-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="empty-title">暂无资产</div>
                <div class="empty-description">
                    您还没有任何数字资产<br>
                    快去购买心仪的巴蜀文化珍品�?
                </div>
                <button class="btn btn-primary" onclick="goToShop()">去购�?/button>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" id="load-more">
                <button class="btn btn-secondary">
                    <i class="fas fa-plus"></i> 加载更多资产
                </button>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        let currentSort = 'value';
        let sortOrder = 'desc';

        // 搜索功能
        function searchAssets() {
            const searchText = document.getElementById('search-input').value.trim();
            const cards = document.querySelectorAll('.product-card');
            
            cards.forEach(card => {
                const productName = card.querySelector('.product-name').textContent.toLowerCase();
                const isMatch = productName.includes(searchText.toLowerCase());
                card.style.display = isMatch ? 'block' : 'none';
            });
            
            updateAssetsCount();
        }

        // 筛选功�?
        document.querySelectorAll('.filter-tag').forEach(tag => {
            tag.addEventListener('click', function() {
                document.querySelectorAll('.filter-tag').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                filterAssets(filter);
            });
        });

        function filterAssets(filter) {
            const cards = document.querySelectorAll('.product-card');
            let visibleCount = 0;
            
            cards.forEach(card => {
                const category = card.getAttribute('data-category');
                let shouldShow = false;
                
                if (filter === 'all') {
                    shouldShow = true;
                } else if (filter === 'rising') {
                    // 显示涨幅为正的资�?
                    const valueChange = card.querySelector('.value-change');
                    shouldShow = valueChange && valueChange.classList.contains('value-up');
                } else {
                    shouldShow = category === filter;
                }
                
                if (shouldShow) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 显示空状�?
            const emptyState = document.getElementById('empty-state');
            const grid = document.getElementById('assets-grid');
            const loadMore = document.getElementById('load-more');
            
            if (visibleCount === 0) {
                grid.style.display = 'none';
                loadMore.style.display = 'none';
                emptyState.style.display = 'block';
            } else {
                grid.style.display = 'grid';
                loadMore.style.display = 'block';
                emptyState.style.display = 'none';
            }
            
            updateAssetsCount();
        }

        // 更新资产数量
        function updateAssetsCount() {
            const visibleCards = document.querySelectorAll('.product-card[style*="block"], .product-card:not([style])');
            document.getElementById('assets-count').textContent = `�?{visibleCards.length}项资产`;
        }

        // 排序功能
        function toggleSort() {
            const sortOptions = ['value', 'date', 'name'];
            const currentIndex = sortOptions.indexOf(currentSort);
            const nextIndex = (currentIndex + 1) % sortOptions.length;
            
            currentSort = sortOptions[nextIndex];
            
            let sortText = '';
            switch(currentSort) {
                case 'value':
                    sortText = '按价�?;
                    break;
                case 'date':
                    sortText = '按时�?;
                    break;
                case 'name':
                    sortText = '按名�?;
                    break;
            }
            
            document.getElementById('sort-text').textContent = sortText;
            sortAssets();
        }

        function sortAssets() {
            const grid = document.getElementById('assets-grid');
            const cards = Array.from(grid.querySelectorAll('.product-card'));
            
            cards.sort((a, b) => {
                let aValue, bValue;
                
                switch(currentSort) {
                    case 'value':
                        aValue = parseFloat(a.getAttribute('data-value'));
                        bValue = parseFloat(b.getAttribute('data-value'));
                        break;
                    case 'date':
                        aValue = new Date(a.getAttribute('data-purchase-date'));
                        bValue = new Date(b.getAttribute('data-purchase-date'));
                        break;
                    case 'name':
                        aValue = a.querySelector('.product-name').textContent;
                        bValue = b.querySelector('.product-name').textContent;
                        break;
                }
                
                if (sortOrder === 'desc') {
                    return aValue > bValue ? -1 : 1;
                } else {
                    return aValue < bValue ? -1 : 1;
                }
            });
            
            cards.forEach(card => grid.appendChild(card));
        }

        // 资产操作
        function shareAsset(btn) {
            const card = btn.closest('.product-card');
            const productName = card.querySelector('.product-name').textContent;
            alert(`分享"${productName}"`);
            // 这里可以打开分享弹窗
        }

        function transferAsset(btn) {
            const card = btn.closest('.product-card');
            const productName = card.querySelector('.product-name').textContent;
            
            if (confirm(`确定要转�?${productName}"吗？`)) {
                alert(`转赠"${productName}"`);
                // 这里可以打开转赠功能
            }
        }

        function exportAssets() {
            alert('导出资产清单');
            // 这里可以生成PDF或Excel文件
        }

        function showMenu() {
            alert('显示更多选项');
            // 这里可以显示更多操作菜单
        }

        // 资产卡片点击
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是操作按钮，不跳转
                if (e.target.closest('.asset-actions')) {
                    e.stopPropagation();
                    return;
                }
                
                const productName = this.querySelector('.product-name').textContent;
                alert(`查看"${productName}"详情`);
                // 这里可以跳转到资产详情页
            });
        });

        // 其他功能
        function goToShop() {
            window.location.href = 'index.html';
        }

        // 搜索框回车事�?
        document.getElementById('search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchAssets();
            }
        });

        // 加载更多
        document.querySelector('.load-more button').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 加载�?..';
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-plus"></i> 加载更多资产';
                alert('已加载更多资�?);
            }, 1500);
        });

        // 统计数据更新（模拟实时数据）
        function updateStats() {
            const cards = document.querySelectorAll('.product-card[style*="block"], .product-card:not([style])');
            let totalValue = 0;
            let totalAssets = cards.length;
            
            cards.forEach(card => {
                const value = parseFloat(card.getAttribute('data-value'));
                totalValue += value;
            });
            
            document.getElementById('total-assets').textContent = totalAssets;
            document.getElementById('total-value').textContent = `�?{totalValue.toLocaleString()}`;
            
            // 模拟涨幅变化
            const changePercent = (Math.random() * 20 - 10).toFixed(1);
            const changeElement = document.getElementById('today-change');
            changeElement.textContent = `${changePercent > 0 ? '+' : ''}${changePercent}%`;
            changeElement.style.color = changePercent > 0 ? '#8fbc8f' : '#b22222';
        }

        // 初始�?
        updateAssetsCount();
        updateStats();

        // �?0秒更新统计数�?
        setInterval(updateStats, 30000);

        // 添加点击效果
        document.querySelectorAll('.btn, .filter-tag, .product-card').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
