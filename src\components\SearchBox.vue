<template>
  <section class="search-section">
    <div class="search-container">
      <input 
        v-model="searchText"
        type="text" 
        class="search-input" 
        placeholder="搜索资产、系列、专区"
        @keypress.enter="handleSearch"
        @input="handleInput"
      >
      <button class="search-btn" @click="handleSearch" :disabled="loading">
        <i v-if="loading" class="fas fa-spinner fa-spin"></i>
        <i v-else class="fas fa-search"></i>
      </button>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { SearchAPI, type DigSearchVo } from '@/api/search'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  autoClear?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '搜索资产、系列、专区',
  autoClear: true
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'search', searchValue: string): void
  (e: 'input', value: string): void
}

const emit = defineEmits<Emits>()

// Router
const router = useRouter()

// Data
const searchText = ref(props.modelValue)
const loading = ref(false)

// 监听搜索文本变化
watch(() => props.modelValue, (newValue) => {
  searchText.value = newValue
})

watch(searchText, (newValue) => {
  emit('update:modelValue', newValue)
})

// 处理输入事件
const handleInput = () => {
  emit('input', searchText.value)
}

// 处理搜索
const handleSearch = async () => {
  const keyword = searchText.value.trim()
  if (!keyword) {
    return
  }

  // 发送搜索事件
  emit('search', keyword)

  try {
    loading.value = true
    
    // 调用搜索API
    const searchParams: DigSearchVo = {
      searchValue: keyword
    }

    const response = await SearchAPI.search(searchParams)
    
    // 跳转到搜索结果页
    router.push({
      name: 'search-results',
      query: {
        q: keyword
      }
    })

    // 可选：自动清空搜索框
    if (props.autoClear) {
      searchText.value = ''
    }
    
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  handleSearch,
  clearSearch: () => {
    searchText.value = ''
  }
})
</script>

<style scoped>
/* CSS变量定义 - 深色金黄主题配色方案 */
:root {
  /* 主色系 - 优雅金黄 */
  --primary-color: #d4a574;
  --primary-dark: #b8956a;
  --primary-light: #e8c49a;
  --primary-alpha: rgba(212, 165, 116, 0.15);
  
  /* 辅助色系 - 温暖橙色 */
  --accent-color: #f4a261;
  --accent-dark: #e76f51;
  --accent-light: #f9c74f;
  --accent-alpha: rgba(244, 162, 97, 0.15);
  
  /* 功能色系 */
  --success-color: #90a955;
  --warning-color: #f9844a;
  --error-color: #e63946;
  --info-color: #457b9d;
  
  /* 文字颜色系统 */
  --text-primary: #f8f6f0;
  --text-secondary: #e0ddd4;
  --text-tertiary: #c4c0b1;
  --text-muted: #a39d8e;
  --text-inverse: #2d2a24;
  
  /* 背景色系统 */
  --bg-primary: #1a1a1a;
  --bg-secondary: #242424;
  --bg-tertiary: #2e2e2e;
  --bg-card: rgba(42, 42, 42, 0.9);
  --bg-card-hover: rgba(50, 50, 50, 0.95);
  --bg-glass: rgba(42, 42, 42, 0.8);
  
  /* 边框和阴影 */
  --border-primary: rgba(212, 165, 116, 0.3);
  --border-light: rgba(248, 246, 240, 0.1);
  --border-hover: rgba(212, 165, 116, 0.5);
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.6);
  --shadow-primary: 0 4px 16px rgba(212, 165, 116, 0.3);
  --shadow-glow: 0 0 20px rgba(212, 165, 116, 0.4);
  
  /* 渐变系统 */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%);
  --gradient-bg: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
  --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(46, 46, 46, 0.8) 100%);
  --gradient-hero: linear-gradient(180deg, rgba(26, 26, 26, 0.8) 0%, rgba(36, 36, 36, 0.9) 50%, rgba(46, 46, 46, 0.95) 100%);
  
  /* 兼容旧变量名 */
  --primary-gold: var(--primary-color);
  --primary-gold-light: var(--primary-light);
  --primary-gold-dark: var(--primary-dark);
  --primary-gold-alpha: var(--primary-alpha);
  --secondary-purple: var(--accent-color);
  --secondary-purple-light: var(--accent-light);
  --secondary-purple-dark: var(--accent-dark);
  --neutral-100: var(--text-primary);
  --neutral-200: var(--text-secondary);
  --neutral-400: var(--text-tertiary);
  --neutral-600: var(--text-muted);
  --shadow-gold: var(--shadow-primary);
  --shadow-purple: var(--shadow-primary);
}

.search-section {
  margin: 20px 0;
}

.search-container {
  display: flex;
  background: var(--gradient-card);
  border-radius: 24px;
  padding: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.search-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
  opacity: 0.6;
}

.search-container:focus-within {
  border-color: var(--border-hover);
  box-shadow: var(--shadow-primary);
  transform: translateY(-1px);
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--neutral-100);
  padding: 12px 16px;
  font-size: 16px; /* 保证移动端不放大 */
  outline: none;
  transition: all 0.3s ease;
}

.search-input::placeholder {
  color: var(--neutral-400);
  font-style: italic;
}

.search-input:focus {
  color: var(--text-primary);
}

.search-btn {
  background: var(--gradient-primary);
  border: none;
  color: white;
  padding: 10px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 44px;
  position: relative;
  overflow: hidden;
}

.search-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.search-btn:hover::before {
  width: 100%;
  height: 100%;
}

.search-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-gold);
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.search-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.search-btn:disabled {
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

.search-btn:disabled:hover {
  transform: none;
  box-shadow: var(--shadow-md);
}

.search-btn i {
  font-size: 16px;
  position: relative;
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-section {
    margin: 16px 0;
  }
  
  .search-input {
    padding: 10px 14px;
    font-size: 16px; /* 保证移动端不放大 */
  }
  
  .search-btn {
    padding: 8px 14px;
    min-width: 40px;
  }
  
  .search-btn i {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .search-container {
    border-radius: 20px;
  }
  
  .search-input {
    padding: 8px 12px;
    font-size: 16px; /* 保证移动端不放大 */
  }
  
  .search-btn {
    padding: 6px 12px;
    border-radius: 16px;
    min-width: 36px;
  }
}
</style> 