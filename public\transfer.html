<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>转赠页面 - 四川省数字资产发行平�?/title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a0a, #161616);
            color: #f0e0d0;
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(218, 165, 32, 0.3);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #daa520;
            font-size: 18px;
            cursor: pointer;
        }
        
        .text-medium {
            font-size: 18px;
            font-weight: 600;
            color: #f8f0e0;
        }
        
        .text-primary {
            color: #daa520;
        }
        
        .transfer-section {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 12px;
            padding: 20px;
            margin: 16px 0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(218, 165, 32, 0.3);
            border-radius: 8px;
            background: rgba(60, 40, 30, 0.5);
            color: #f0e0d0;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
        }
        
        .asset-selector {
            border: 1px solid rgba(218, 165, 32, 0.3);
            border-radius: 8px;
            background: rgba(60, 40, 30, 0.5);
            padding: 12px;
            cursor: pointer;
            transition: all 0.3s;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(240, 224, 208, 0.6);
        }
        
        .asset-selector:hover {
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
        }
        
        .asset-selector.selected {
            border-color: #daa520;
            background: rgba(80, 60, 40, 0.5);
            color: #f8f0e0;
        }
        
        .selected-asset {
            display: flex;
            align-items: center;
            gap: 12px;
            width: 100%;
        }
        
        .asset-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            background: linear-gradient(135deg, #daa520, #b8860b);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: #2a1616;
        }
        
        .asset-info {
            flex: 1;
        }
        
        .asset-name {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
            margin-bottom: 4px;
        }
        
        .asset-id {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            margin: 16px 0;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(218, 165, 32, 0.4);
        }
        
        .btn-secondary {
            background: rgba(60, 40, 30, 0.7);
            color: #f0e0d0;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .transfer-record {
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border: 1px solid rgba(218, 165, 32, 0.2);
        }
        
        .record-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        
        .record-title {
            font-size: 15px;
            font-weight: 600;
            color: #f8f0e0;
        }
        
        .record-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }
        
        .status-success {
            background: rgba(40, 167, 69, 0.2);
            color: #28a745;
        }
        
        .status-pending {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }
        
        .status-failed {
            background: rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }
        
        .record-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
            margin-top: 8px;
        }
        
        .detail-item {
            font-size: 12px;
            color: rgba(240, 224, 208, 0.7);
        }
        
        .detail-label {
            color: #daa520;
            margin-bottom: 2px;
        }
        
        .tabs {
            display: flex;
            background: rgba(60, 40, 30, 0.5);
            border-radius: 8px;
            padding: 4px;
            margin-bottom: 16px;
        }
        
        .tab {
            flex: 1;
            padding: 8px 16px;
            text-align: center;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            color: rgba(240, 224, 208, 0.7);
        }
        
        .tab.active {
            background: linear-gradient(135deg, #daa520, #b8860b);
            color: #2a1616;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 100;
            padding: 20px;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: linear-gradient(145deg, #2a201e, #352520);
            border-radius: 16px;
            padding: 24px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            border: 1px solid rgba(218, 165, 32, 0.3);
        }
        
        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #daa520;
        }
        
        .modal-close {
            background: none;
            border: none;
            color: #f0e0d0;
            font-size: 20px;
            cursor: pointer;
        }
        
        .asset-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .asset-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 8px;
            border: 1px solid transparent;
        }
        
        .asset-item:hover {
            background: rgba(80, 60, 40, 0.5);
            border-color: rgba(218, 165, 32, 0.3);
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(26, 10, 10, 0.9);
            backdrop-filter: blur(10px);
            border-top: 1px solid rgba(218, 165, 32, 0.3);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: rgba(240, 224, 208, 0.7);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: #daa520;
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-medium text-primary">转赠页面</h1>
                <div></div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="tabs">
                <div class="tab active" onclick="switchTab('transfer')">转赠资产</div>
                <div class="tab" onclick="switchTab('records')">转赠记录</div>
            </div>

            <!-- 转赠表单 -->
            <div id="transferForm" class="tab-content">
                <section class="transfer-section">
                    <div class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        选择要转赠的资产
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">选择资产 <span style="color: #dc3545;">*</span></label>
                        <div class="asset-selector" id="assetSelector" onclick="openAssetModal()">
                            <div style="text-align: center;">
                                <i class="fas fa-plus" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                                点击选择要转赠的数字资产
                            </div>
                        </div>
                    </div>
                </section>

                <section class="transfer-section">
                    <div class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        接收方信�?
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">接收方地址 <span style="color: #dc3545;">*</span></label>
                        <input type="text" class="form-input" id="receiverAddress" placeholder="请输入对方的链上地址">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">转赠备注</label>
                        <input type="text" class="form-input" id="transferNote" placeholder="可选，为本次转赠添加备注信�?>
                    </div>
                </section>

                <section class="transfer-section">
                    <div class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        转赠确认
                    </div>
                    
                    <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 12px; margin-bottom: 16px; font-size: 13px; color: rgba(240, 224, 208, 0.8);">
                        <i class="fas fa-exclamation-triangle" style="color: #ffc107; margin-right: 8px;"></i>
                        转赠操作不可撤销，请确保接收方地址正确。转赠完成后，资产将从您的账户转移到对方账户�?
                    </div>
                    
                    <button class="btn btn-primary" onclick="confirmTransfer()">
                        <i class="fas fa-paper-plane"></i> 确认转赠
                    </button>
                </section>
            </div>

            <!-- 转赠记录 -->
            <div id="transferRecords" class="tab-content" style="display: none;">
                <section class="transfer-section">
                    <div class="section-title">
                        <div class="section-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        转赠记录
                    </div>
                    
                    <div class="transfer-record">
                        <div class="record-header">
                            <div class="record-title">三星堆青铜面�?#001</div>
                            <div class="record-status status-success">转赠成功</div>
                        </div>
                        <div class="record-details">
                            <div class="detail-item">
                                <div class="detail-label">接收�?/div>
                                <div>0x742d35Cc1234567890...</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">转赠时间</div>
                                <div>2024-01-15 14:30</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">交易哈希</div>
                                <div>0xa1b2c3d4...</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">备注</div>
                                <div>新年礼物</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="transfer-record">
                        <div class="record-header">
                            <div class="record-title">金沙太阳神鸟 #025</div>
                            <div class="record-status status-pending">处理�?/div>
                        </div>
                        <div class="record-details">
                            <div class="detail-item">
                                <div class="detail-label">接收�?/div>
                                <div>0x956f82Af987654321...</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">发起时间</div>
                                <div>2024-01-16 09:15</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">状�?/div>
                                <div>区块链确认中</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">备注</div>
                                <div>生日礼物</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="transfer-record">
                        <div class="record-header">
                            <div class="record-title">蜀锦织锦图 #012</div>
                            <div class="record-status status-failed">转赠失败</div>
                        </div>
                        <div class="record-details">
                            <div class="detail-item">
                                <div class="detail-label">接收�?/div>
                                <div>0xInvalidAddress...</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">失败时间</div>
                                <div>2024-01-14 16:45</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">失败原因</div>
                                <div>接收方地址无效</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">操作</div>
                                <div><a href="#" style="color: #daa520;">重新转赠</a></div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <!-- 资产选择模态框 -->
    <div class="modal" id="assetModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">选择要转赠的资产</div>
                <button class="modal-close" onclick="closeAssetModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="asset-list">
                <div class="asset-item" onclick="selectAsset('三星堆青铜面�?#003', 'MASK003')">
                    <div class="asset-image">
                        <i class="fas fa-mask"></i>
                    </div>
                    <div class="asset-info">
                        <div class="asset-name">三星堆青铜面�?#003</div>
                        <div class="asset-id">资产编号：MASK003</div>
                    </div>
                </div>
                
                <div class="asset-item" onclick="selectAsset('金沙太阳神鸟 #028', 'BIRD028')">
                    <div class="asset-image">
                        <i class="fas fa-sun"></i>
                    </div>
                    <div class="asset-info">
                        <div class="asset-name">金沙太阳神鸟 #028</div>
                        <div class="asset-id">资产编号：BIRD028</div>
                    </div>
                </div>
                
                <div class="asset-item" onclick="selectAsset('蜀锦织锦图 #015', 'SILK015')">
                    <div class="asset-image">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="asset-info">
                        <div class="asset-name">蜀锦织锦图 #015</div>
                        <div class="asset-id">资产编号：SILK015</div>
                    </div>
                </div>
                
                <div class="asset-item" onclick="selectAsset('巴蜀印章 #007', 'SEAL007')">
                    <div class="asset-image">
                        <i class="fas fa-stamp"></i>
                    </div>
                    <div class="asset-info">
                        <div class="asset-name">巴蜀印章 #007</div>
                        <div class="asset-id">资产编号：SEAL007</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item active">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        let selectedAsset = null;

        // 切换标签�?
        function switchTab(tabName) {
            // 更新标签状�?
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
            
            // 显示对应内容
            document.querySelectorAll('.tab-content').forEach(content => content.style.display = 'none');
            if (tabName === 'transfer') {
                document.getElementById('transferForm').style.display = 'block';
            } else {
                document.getElementById('transferRecords').style.display = 'block';
            }
        }

        // 打开资产选择模态框
        function openAssetModal() {
            document.getElementById('assetModal').classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        // 关闭资产选择模态框
        function closeAssetModal() {
            document.getElementById('assetModal').classList.remove('show');
            document.body.style.overflow = '';
        }

        // 选择资产
        function selectAsset(name, id) {
            selectedAsset = { name, id };
            
            const selector = document.getElementById('assetSelector');
            selector.classList.add('selected');
            selector.innerHTML = `
                <div class="selected-asset">
                    <div class="asset-image">
                        <i class="fas fa-${getAssetIcon(name)}"></i>
                    </div>
                    <div class="asset-info">
                        <div class="asset-name">${name}</div>
                        <div class="asset-id">资产编号�?{id}</div>
                    </div>
                </div>
            `;
            
            closeAssetModal();
        }

        // 获取资产图标
        function getAssetIcon(name) {
            if (name.includes('面具')) return 'mask';
            if (name.includes('太阳神鸟')) return 'sun';
            if (name.includes('蜀�?)) return 'palette';
            if (name.includes('印章')) return 'stamp';
            return 'gem';
        }

        // 确认转赠
        function confirmTransfer() {
            const receiverAddress = document.getElementById('receiverAddress').value.trim();
            const transferNote = document.getElementById('transferNote').value.trim();
            
            if (!selectedAsset) {
                alert('请选择要转赠的资产');
                return;
            }
            
            if (!receiverAddress) {
                alert('请输入接收方地址');
                return;
            }
            
            if (receiverAddress.length < 20) {
                alert('请输入有效的链上地址');
                return;
            }
            
            if (confirm(`确认�?"${selectedAsset.name}" 转赠给地址 "${receiverAddress}" 吗？\n\n注意：转赠操作不可撤销！`)) {
                // 模拟转赠操作
                alert('转赠请求已提交，正在区块链网络中处理...');
                
                // 重置表单
                selectedAsset = null;
                document.getElementById('assetSelector').classList.remove('selected');
                document.getElementById('assetSelector').innerHTML = `
                    <div style="text-align: center;">
                        <i class="fas fa-plus" style="font-size: 24px; margin-bottom: 8px; display: block;"></i>
                        点击选择要转赠的数字资产
                    </div>
                `;
                document.getElementById('receiverAddress').value = '';
                document.getElementById('transferNote').value = '';
            }
        }

        // 点击模态框外部关闭
        document.getElementById('assetModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAssetModal();
            }
        });

        // 添加点击效果
        document.querySelectorAll('.btn, .asset-item, .transfer-record').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
