import request from './request'

/**
 * 资产相关API接口
 */

// 获取系列列表
export const getSeriesList = () => {
  return request.get('/series/series/listName')
}

// 获取专区列表
export const getZonesList = () => {
  return request.get('/zone/zone/listName')
}

/**
 * 资产接口参数类型
 */
export interface AssetListParams {
  assetName?: string    // 资产名称搜索
  seriesId?: number     // 系列ID (Long类型)
  zoneId?: number       // 专区ID (Long类型)
  assetType?: string    // 资产类型
  page?: number         // 页码
  size?: number         // 每页数量
}

/**
 * 库存状态枚举
 */
export enum StockStatus {
  AVAILABLE = 'AVAILABLE',           // 有库存
  LOW_STOCK = 'LOW_STOCK',          // 库存不足
  OUT_OF_STOCK = 'OUT_OF_STOCK'     // 无库存
}

/**
 * 销售状态枚举
 */
export enum SaleStatus {
  ON_SALE = 'ON_SALE',              // 在售
  NOT_ON_SALE = 'NOT_ON_SALE',      // 未开售
  SOLD_OUT = 'SOLD_OUT'             // 售罄
}

/**
 * 发行方信息类型
 */
export interface IssuerInfo {
  id?: string | number               // 发行方ID
  issuerName?: string                // 发行方名称
  name?: string                      // 通用名称字段
  issuerLogo?: string                // 发行方Logo URL
  logo?: string                      // 通用Logo字段
  [key: string]: any                 // 允许其他字段
}

/**
 * 详细发行方信息类型 - 对应后端完整返回结构
 */
export interface IssuerInfoMore {
  issuerId: number                   // 发行方ID
  issuerName: string                 // 发行方名称
  issuerLogo: string                 // 发行方Logo URL
  statusCd?: string | null           // 状态代码
  statusDate?: string | null         // 状态时间
  createStaff?: string | null        // 创建人
  createDate?: string | null         // 创建时间
  updateStaff?: string | null        // 修改人
  updateDate?: string | null         // 修改时间
  remark?: string | null             // 备注
}

/**
 * 资产详细信息类型 - 对应后端完整返回结构
 */
export interface AssetItemMore {
  // 基础信息
  assetId: number                    // 资产ID
  assetName: string                  // 资产名称
  assetCover: string                 // 资产封面图URL
  assetCoverThumbnail: string        // 资产封面缩略图URL
  introImages: string                // 资产介绍图片URL
  assetDescription?: string | null   // 资产描述
  assetType: string                  // 资产类型
  isBlindBox: string                 // 是否盲盒 ("0"/"1")

  // 发行信息
  issueQuantity: number              // 发行数量
  individualLimit: number            // 个人限购数量
  enterpriseLimit: number            // 企业限购数量
  airdropQuantity: number            // 空投数量
  activityQuantity: number           // 活动数量
  issuePrice: number                 // 发行价格
  issueStartTime?: string | null     // 发行开始时间
  issueEndTime?: string | null       // 发行结束时间

  // 状态信息
  statusCd: string                   // 状态代码
  createDate: string                 // 创建时间

  // 发行方信息
  issuers: IssuerInfoMore[]          // 发行方信息列表

  // 系列和专区信息
  seriesName: string                 // 系列名称
  zoneName: string                   // 专区名称

  // 库存信息
  totalQuantity: number              // 总数量
  availableStock: number             // 可用库存
  soldCount: number                  // 已售数量
  stockStatus: StockStatus           // 库存状态
  stockUpdateTime: string            // 库存更新时间
  isSoldOut: boolean                 // 是否售罄
  stockPercentage: number            // 库存百分比

  // 购买状态
  canPurchase: boolean               // 是否可购买
  cannotPurchaseReason?: string | null // 不可购买原因
  saleStatus: SaleStatus             // 销售状态
}

/**
 * 资产数据类型 - 基于后端 DigDigitalAsset 实体类
 */
export interface AssetItem {
  // 主要字段 - 对应后端实体
  assetId: number | string           // 资产ID，主键
  assetName: string                  // 资产名称
  assetCover?: string                // 资产封面图URL
  assetFile?: string                 // 资产文件URL
  issuerIds?: string                 // 发行方ID列表
  issuers?: IssuerInfo[]             // 发行方信息列表（从接口返回）
  assetType?: string                 // 资产类型
  assetLevel?: number                // 资产等级
  saleStartTime?: string             // 开售时间
  issueQuantity?: number             // 发行数量
  individualLimit?: number           // 个人限购数量
  enterpriseLimit?: number           // 企业限购数量
  airdropQuantity?: number           // 空投数量
  activityQuantity?: number          // 活动数量
  issuePrice?: number | string       // 发行价格
  seriesId?: number | string         // 所属系列ID
  assetKeywords?: string             // 资产关键字
  assetDesc?: string                 // 资产简介
  introImages?: string               // 资产介绍图片URL
  rejectionReason?: string           // 驳回原因
  statusCd?: string                  // 状态代码
  statusDate?: string                // 状态时间
  createStaff?: string               // 创建人
  createDate?: string                // 创建时间
  updateStaff?: string               // 修改人
  updateDate?: string                // 修改时间
  remark?: string                    // 备注

  // 兼容字段 - 保持向后兼容
  id?: string | number               // 通用ID字段
  name?: string                      // 通用名称字段
  image?: string                     // 通用图片字段
  price?: string                     // 通用价格字段
  category?: string                  // 分类
  categoryName?: string              // 分类名称
  seriesName?: string                // 系列名称
  zoneId?: string                    // 专区ID
  zoneName?: string                  // 专区名称
  saleTime?: string                  // 通用销售时间
  stats?: string                     // 统计信息
  popularity?: number                // 热度
  createTime?: string                // 通用创建时间

  // 展示相关字段
  tags?: Array<{
    text: string
    type: 'primary' | 'accent' | 'success' | 'warning'
  }>
  tag?: {
    text: string
    type: 'on-sale' | 'limited' | 'sold-out' | 'default'
  }

  [key: string]: any  // 允许其他字段
}

/**
 * 获取资产详情
 */
export const getAssetDetail = async (assetId: string | number) => {
  const url = `/asset/asset/client/detail/${assetId}`
  console.log('资产详情请求URL:', url)

  return request.get(url)
}

/**
 * 获取资产列表
 */
export const getAssetList = async (params: AssetListParams = {}) => {
  const queryParams = new URLSearchParams()

  // 添加搜索参数
  if (params.assetName?.trim()) {
    queryParams.append('assetName', params.assetName.trim())
  }

  // 添加筛选参数
  if (params.seriesId !== undefined && params.seriesId !== null) {
    queryParams.append('seriesId', String(params.seriesId))
  }

  if (params.zoneId !== undefined && params.zoneId !== null) {
    queryParams.append('zoneId', String(params.zoneId))
  }

  if (params.assetType?.trim()) {
    queryParams.append('assetType', params.assetType.trim())
  }

  // 添加分页参数
  queryParams.append('page', String(params.page || 1))
  queryParams.append('size', String(params.size || 20))

  const url = `/asset/asset/client/list${queryParams.toString() ? '?' + queryParams.toString() : ''}`
  console.log('资产列表请求URL:', url)

  return request.get(url)
}

export default {
  getSeriesList,
  getZonesList,
  getAssetList,
  getAssetDetail
}
