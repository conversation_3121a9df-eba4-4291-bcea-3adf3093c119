<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>资产 - 凌云数资</title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }
        
        .header {
            position: sticky;
            top: 0;
            z-index: 50;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 12px 0;
        }
        
        .nav-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .back-btn:hover {
            color: var(--primary-gold-light);
        }
        
        .search-section {
            margin: 20px 0;
        }
        
        .search-container {
            display: flex;
            background: var(--gradient-card);
            border-radius: 24px;
            padding: 4px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .search-dropdown {
            background: var(--primary-gold-alpha);
            border: none;
            color: var(--primary-gold);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-input {
            flex: 1;
            background: transparent;
            border: none;
            color: var(--neutral-100);
            padding: 8px 16px;
            font-size: 14px;
            outline: none;
        }
        
        .search-input::placeholder {
            color: var(--neutral-400);
        }
        
        .search-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
        }
        
        .search-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-gold);
        }
        
        .filter-section {
            margin: 16px 0;
        }
        
        .filter-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding: 4px 0;
        }
        
        .filter-tab {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--gradient-card);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            min-width: fit-content;
        }
        
        .filter-tab.active {
            background: var(--gradient-primary);
            color: white;
            border-color: var(--primary-gold);
            box-shadow: var(--shadow-gold);
        }
        
        .filter-tab:hover {
            border-color: var(--primary-gold);
        }
        
        .sort-options {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
        
        .sort-btn {
            padding: 6px 12px;
            border-radius: 16px;
            background: var(--bg-card);
            color: var(--neutral-400);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .sort-btn.active {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
            border-color: var(--primary-gold);
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }
        
        .product-card {
            background: var(--gradient-card);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            cursor: pointer;
        }
        
        .product-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary-gold);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            position: relative;
            overflow: hidden;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-gold);
            font-size: 13px;
        }
        
        .product-tag {
            position: absolute;
            top: 8px;
            left: 8px;
            background: var(--accent-red);
            color: white;
            font-size: 11px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
            z-index: 2;
            box-shadow: var(--shadow-sm);
        }
        
        .product-tag.on-sale {
            background: var(--accent-green);
        }
        
        .product-tag.limited {
            background: var(--primary-gold);
        }
        
        .product-tag.sold-out {
            background: var(--neutral-600);
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            font-size: 15px;
            font-weight: 600;
            margin-bottom: 6px;
            color: var(--neutral-100);
            line-height: 1.4;
        }
        
        .product-price {
            font-size: 16px;
            font-weight: bold;
            color: var(--primary-gold);
            margin: 4px 0;
        }
        
        .product-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }
        
        .sale-time {
            font-size: 11px;
            color: var(--neutral-400);
            background: rgba(255, 255, 255, 0.1);
            padding: 3px 8px;
            border-radius: 10px;
        }
        
        .product-stats {
            font-size: 11px;
            color: var(--neutral-400);
        }
        
        .tag {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 500;
            margin: 2px;
        }
        
        .tag-primary {
            background: var(--primary-gold-alpha);
            color: var(--primary-gold);
            border: 1px solid var(--primary-gold);
        }
        
        .tag-accent {
            background: rgba(220, 38, 38, 0.1);
            color: var(--accent-red);
            border: 1px solid var(--accent-red);
        }
        
        .tag-success {
            background: rgba(5, 150, 105, 0.1);
            color: var(--accent-green);
            border: 1px solid var(--accent-green);
        }
        
        .tag-warning {
            background: rgba(234, 88, 12, 0.1);
            color: var(--accent-orange);
            border: 1px solid var(--accent-orange);
        }
        
        .load-more {
            text-align: center;
            margin: 24px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 24px;
            border-radius: 24px;
            font-size: 15px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
            text-decoration: none;
            min-width: 120px;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(200, 134, 13, 0.4);
        }
        
        .btn-secondary {
            background: var(--gradient-card);
            color: var(--neutral-200);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--primary-gold);
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-card);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding: 8px 0;
            z-index: 50;
        }
        
        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px;
            color: var(--neutral-400);
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .nav-item.active {
            color: var(--primary-gold);
        }
        
        .nav-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }
        
        .nav-label {
            font-size: 10px;
        }
        
        .main-content {
            padding-bottom: 80px;
        }
        
        .status-bar {
            background: var(--gradient-card);
            border-radius: 12px;
            padding: 12px;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-text {
            font-size: 14px;
            color: var(--neutral-200);
            text-align: center;
        }
        
        .highlight {
            color: var(--primary-gold);
            font-weight: 600;
        }
        
        /* 响应式调整 */
        @media (max-width: 480px) {
            .container {
                padding: 0 12px;
            }
            
            .products-grid {
                gap: 12px;
            }
            
            .filter-tabs {
                gap: 6px;
            }
            
            .filter-tab {
                padding: 6px 12px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="nav-content">
                <button class="back-btn" onclick="history.back()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 style="font-size: 18px; font-weight: 600; color: var(--primary-gold);">数字资产</h1>
                <button class="back-btn" onclick="showFilter()">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="container">
            <!-- 搜索区域 -->
            <section class="search-section">
                <div class="search-container">
                    <select class="search-dropdown">
                        <option>全部</option>
                        <option>古蜀文明</option>
                        <option>三星堆</option>
                        <option>金沙遗址</option>
                        <option>蜀锦文化</option>
                    </select>
                    <input type="text" class="search-input" placeholder="搜索资产...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </section>

            <!-- 状态栏 -->
            <section class="status-bar">
                <div class="status-text">
                    <i class="fas fa-store" style="color: var(--primary-gold); margin-right: 8px;"></i>
                    资产共有 <span class="highlight">128</span> 件数字藏品正在展出
                </div>
            </section>

            <!-- 分类筛选 -->
            <section class="filter-section">
                <div class="filter-tabs">
                    <div class="filter-tab active" data-category="all">全部资产</div>
                    <div class="filter-tab" data-category="sanxingdui">三星堆</div>
                    <div class="filter-tab" data-category="jinsha">金沙遗址</div>
                    <div class="filter-tab" data-category="shujin">蜀锦文化</div>
                    <div class="filter-tab" data-category="folk">民俗文化</div>
                </div>
                
                <div class="sort-options">
                    <button class="sort-btn active" data-sort="newest">最新</button>
                    <button class="sort-btn" data-sort="popular">热门</button>
                    <button class="sort-btn" data-sort="price">价格</button>
                </div>
            </section>

            <!-- 资产网格 -->
            <section class="products-grid">
                <!-- 三星堆黄金面罩 -->
                <div class="product-card" data-category="sanxingdui">
                    <div class="product-image">
                        <div class="product-tag">限量</div>
                        黄金面罩
                    </div>
                    <div class="product-info">
                        <div class="product-name">三星堆黄金面罩</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">考古文物</span>
                            <span class="tag tag-accent">稀有</span>
                        </div>
                        <div class="product-price">¥888.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.01</div>
                            <div class="product-stats">已售 95/100</div>
                        </div>
                    </div>
                </div>

                <!-- 金沙太阳神鸟 -->
                <div class="product-card" data-category="jinsha">
                    <div class="product-image">
                        <div class="product-tag on-sale">热销</div>
                        太阳神鸟
                    </div>
                    <div class="product-info">
                        <div class="product-name">金沙太阳神鸟</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">文化符号</span>
                            <span class="tag tag-success">在售</span>
                        </div>
                        <div class="product-price">¥666.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.02</div>
                            <div class="product-stats">已售 156/200</div>
                        </div>
                    </div>
                </div>

                <!-- 青铜立人像 -->
                <div class="product-card" data-category="sanxingdui">
                    <div class="product-image">
                        <div class="product-tag">精品</div>
                        青铜立人
                    </div>
                    <div class="product-info">
                        <div class="product-name">三星堆青铜立人像</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">青铜器</span>
                            <span class="tag tag-warning">预售</span>
                        </div>
                        <div class="product-price">¥1,200.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.05</div>
                            <div class="product-stats">预约 45/50</div>
                        </div>
                    </div>
                </div>

                <!-- 蜀锦云纹 -->
                <div class="product-card" data-category="shujin">
                    <div class="product-image">
                        蜀锦纹样
                    </div>
                    <div class="product-info">
                        <div class="product-name">蜀锦云纹织品</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">传统工艺</span>
                            <span class="tag tag-success">在售</span>
                        </div>
                        <div class="product-price">¥299.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.03</div>
                            <div class="product-stats">已售 78/150</div>
                        </div>
                    </div>
                </div>

                <!-- 青铜神树 -->
                <div class="product-card" data-category="sanxingdui">
                    <div class="product-image">
                        <div class="product-tag">经典</div>
                        青铜神树
                    </div>
                    <div class="product-info">
                        <div class="product-name">三星堆青铜神树</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">神秘文物</span>
                            <span class="tag tag-success">在售</span>
                        </div>
                        <div class="product-price">¥999.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.01</div>
                            <div class="product-stats">已售 123/200</div>
                        </div>
                    </div>
                </div>

                <!-- 川剧变脸 -->
                <div class="product-card" data-category="folk">
                    <div class="product-image">
                        川剧脸谱
                    </div>
                    <div class="product-info">
                        <div class="product-name">川剧变脸艺术</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">非遗文化</span>
                            <span class="tag tag-success">在售</span>
                        </div>
                        <div class="product-price">¥188.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.04</div>
                            <div class="product-stats">已售 34/100</div>
                        </div>
                    </div>
                </div>

                <!-- 金沙象牙 -->
                <div class="product-card" data-category="jinsha">
                    <div class="product-image">
                        <div class="product-tag limited">珍藏</div>
                        象牙雕刻
                    </div>
                    <div class="product-info">
                        <div class="product-name">金沙象牙雕刻</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">工艺品</span>
                            <span class="tag tag-accent">稀有</span>
                        </div>
                        <div class="product-price">¥1,588.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.06</div>
                            <div class="product-stats">已售 12/30</div>
                        </div>
                    </div>
                </div>

                <!-- 蜀锦鸳鸯 -->
                <div class="product-card" data-category="shujin">
                    <div class="product-image">
                        鸳鸯织锦
                    </div>
                    <div class="product-info">
                        <div class="product-name">蜀锦鸳鸯戏水</div>
                        <div style="margin: 4px 0;">
                            <span class="tag tag-primary">经典纹样</span>
                            <span class="tag tag-success">在售</span>
                        </div>
                        <div class="product-price">¥388.00</div>
                        <div class="product-meta">
                            <div class="sale-time">2024.01.02</div>
                            <div class="product-stats">已售 67/120</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 加载更多 -->
            <section class="load-more">
                <button class="btn btn-secondary" onclick="loadMore()">
                    <i class="fas fa-sync-alt"></i> 加载更多
                </button>
            </section>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <div class="container">
            <div class="nav-items">
                <a href="index.html" class="nav-item">
                    <i class="nav-icon fas fa-home"></i>
                    <span class="nav-label">首页</span>
                </a>
                <a href="presale.html" class="nav-item">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-label">预售</span>
                </a>
                <a href="exhibition.html" class="nav-item active">
                    <i class="nav-icon fas fa-store"></i>
                    <span class="nav-label">资产</span>
                </a>
                <a href="zones.html" class="nav-item">
                    <i class="nav-icon fas fa-th-large"></i>
                    <span class="nav-label">专区</span>
                </a>
                <a href="profile.html" class="nav-item">
                    <i class="nav-icon fas fa-user"></i>
                    <span class="nav-label">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 搜索功能
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchType = document.querySelector('.search-dropdown').value;
            const searchText = document.querySelector('.search-input').value;
            
            if (searchText.trim()) {
                alert(`在${searchType}中搜索资产: ${searchText}`);
                // 这里可以实现真实的搜索逻辑
            }
        });

        // 搜索框回车事件
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // 筛选标签功能
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有活跃状态
                document.querySelectorAll('.filter-tab').forEach(t => t.classList.remove('active'));
                // 添加当前活跃状态
                this.classList.add('active');
                
                const category = this.dataset.category;
                filterProducts(category);
            });
        });

        // 排序按钮功能
        document.querySelectorAll('.sort-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.sort-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const sortType = this.dataset.sort;
                sortProducts(sortType);
            });
        });

        // 筛选产品
        function filterProducts(category) {
            const products = document.querySelectorAll('.product-card');
            products.forEach(product => {
                if (category === 'all' || product.dataset.category === category) {
                    product.style.display = 'block';
                } else {
                    product.style.display = 'none';
                }
            });
        }

        // 排序产品
        function sortProducts(sortType) {
            alert(`按${getSortName(sortType)}排序`);
            // 这里可以实现真实的排序逻辑
        }

        function getSortName(sortType) {
            const names = {
                'newest': '最新',
                'popular': '热门',
                'price': '价格'
            };
            return names[sortType] || '默认';
        }

        // 产品卡片点击事件
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', function(e) {
                // 如果点击的是标签，不触发卡片点击
                if (e.target.closest('.product-tag')) {
                    return;
                }
                
                const productName = this.querySelector('.product-name').textContent;
                alert(`查看${productName}详情`);
                // window.location.href = 'asset-detail.html';
            });
        });

        // 加载更多
        function loadMore() {
            alert('加载更多资产');
            // 这里可以实现分页加载逻辑
        }

        // 显示筛选
        function showFilter() {
            alert('打开高级筛选');
        }

        // 添加点击效果
        document.querySelectorAll('.product-card, .btn, .filter-tab').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html> 
