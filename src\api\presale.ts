/**
 * 预售资产相关API
 */
import request from './request'
import type { ApiResponse } from './types'

/**
 * 发行方信息类型
 */
export interface Issuer {
  issuerLogo: string
  issuerName: string
}

/**
 * 预售资产数据类型
 */
export interface PresaleAsset {
  id: number
  assetId: string
  assetName: string
  assetCover?: string
  assetKeywords?: string
  assetDescription?: string
  assetType?: string
  assetLevel?: string
  issuePrice: number
  issueQuantity?: number
  saleStartTime?: string
  saleEndTime?: string
  soldQuantity?: number
  remainingQuantity?: number
  issuers?: Issuer[]
  isLimited: boolean
  limitType?: string
  personalLimit?: number
  enterpriseLimit?: number
  createTime: string
  updateTime: string
}

/**
 * 预售资产API响应类型
 */
export interface PresaleListResponse extends ApiResponse {
    rows: PresaleAsset[]
}

/**
 * 预售资产API类
 */
class PresaleAPI {
  /**
   * 获取预售资产列表
   * @param pageSize 每页数量，可选参数
   * @returns 预售资产列表
   */
  async getPresaleList(pageSize?: number): Promise<PresaleListResponse> {
    try {
      const params = pageSize ? { pageSize } : undefined
      
      const response = await request.get<PresaleListResponse>('/asset/asset/client/presale/list', params)
      return response
    } catch (error) {
      console.error('获取预售资产列表失败:', error)
      throw error
    }
  }
}

// 导出单例
export default new PresaleAPI() 