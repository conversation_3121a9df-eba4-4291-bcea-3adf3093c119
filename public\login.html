<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 凌云数资</title>
    
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            
            /* 辅助色系 - 时尚科技紫 */
            --secondary-purple: #7C3AED;       
            --secondary-purple-light: #A855F7; 
            --secondary-purple-dark: #6D28D9;  
            --secondary-purple-alpha: rgba(124, 58, 237, 0.1);
            
            /* 背景色系 - 科技深炭色 */
            --bg-primary: #0F0F15;             
            --bg-secondary: #1A1A25;           
            --bg-tertiary: #252530;            
            --bg-glass: rgba(37, 37, 48, 0.8); 
            --bg-card: rgba(37, 37, 48, 0.9);
            
            /* 中性色系 - 优雅灰色调 */
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --neutral-600: #475569;            
            
            /* 强调色系 - 太阳神鸟灵感 */
            --accent-red: #DC2626;             
            --accent-orange: #EA580C;          
            --accent-green: #059669;           
            
            /* 渐变色系 */
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-secondary: linear-gradient(135deg, var(--secondary-purple) 0%, var(--secondary-purple-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 26, 37, 0.9) 100%);
            --gradient-tech: linear-gradient(45deg, rgba(124, 58, 237, 0.1) 0%, rgba(200, 134, 13, 0.1) 50%, rgba(124, 58, 237, 0.1) 100%);
            
            /* 阴影系统 */
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
            --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
            --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
            --shadow-gold: 0 4px 12px rgba(200, 134, 13, 0.3);
            --shadow-purple: 0 4px 12px rgba(124, 58, 237, 0.3);
            --shadow-glow: 0 0 20px rgba(124, 58, 237, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            background: var(--gradient-hero);
            color: var(--neutral-100);
            font-size: 14px;
            line-height: 1.6;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }
        
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-tech);
            animation: float 8s ease-in-out infinite;
            z-index: -1;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(2deg); }
        }
        
        .tech-particles {
            position: fixed;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
        
        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--primary-gold);
            border-radius: 50%;
            animation: particle-float 6s linear infinite;
        }
        
        @keyframes particle-float {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) scale(1);
                opacity: 0;
            }
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            position: relative;
            z-index: 1;
        }
        
        .header {
            padding: 20px 0;
            text-align: center;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: var(--gradient-card);
            border: none;
            color: var(--primary-gold);
            font-size: 18px;
            cursor: pointer;
            padding: 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .back-btn:hover {
            transform: translateY(-50%) scale(1.05);
            box-shadow: var(--shadow-gold);
        }
        
        .logo-section {
            text-align: center;
            padding: 40px 0 50px;
            position: relative;
        }
        
        .logo-container {
            position: relative;
            display: inline-block;
        }
        
        .logo {
            width: 90px;
            height: 90px;
            background: var(--gradient-primary);
            border-radius: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            color: white;
            margin-bottom: 20px;
            box-shadow: var(--shadow-gold), var(--shadow-glow);
            position: relative;
            overflow: hidden;
            animation: logo-pulse 3s ease-in-out infinite;
        }
        
        .logo::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shine 2s linear infinite;
        }
        
        @keyframes logo-pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .welcome-text {
            font-size: 24px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 15px;
            color: var(--neutral-400);
            font-weight: 400;
        }
        
        .login-form {
            background: var(--gradient-card);
            border-radius: 24px;
            padding: 32px;
            margin-bottom: 24px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-lg);
            position: relative;
            overflow: hidden;
        }
        
        .login-form::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-secondary);
            opacity: 0.5;
        }
        
        .tab-switcher {
            display: flex;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 4px;
            margin-bottom: 32px;
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .tab-btn {
            flex: 1;
            padding: 12px 16px;
            border: none;
            background: transparent;
            color: var(--neutral-400);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
            position: relative;
            z-index: 2;
        }
        
        .tab-btn.active {
            color: white;
            background: var(--gradient-secondary);
            box-shadow: var(--shadow-purple);
            transform: scale(1.02);
        }
        
        .form-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .form-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-group {
            margin-bottom: 24px;
            position: relative;
        }
        
        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: var(--neutral-200);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .form-input {
            width: 100%;
            padding: 16px 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--neutral-100);
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--primary-gold);
            box-shadow: 0 0 0 3px var(--primary-gold-alpha), var(--shadow-gold);
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-1px);
        }
        
        .form-input::placeholder {
            color: var(--neutral-400);
        }
        
        .input-wrapper {
            position: relative;
        }
        
        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--neutral-400);
            font-size: 16px;
            z-index: 2;
        }
        
        .form-input.with-icon {
            padding-left: 50px;
        }
        
        .password-toggle {
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--neutral-400);
            cursor: pointer;
            font-size: 16px;
            padding: 4px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .password-toggle:hover {
            color: var(--primary-gold);
            background: rgba(255, 255, 255, 0.05);
        }
        
        .sms-container {
            display: flex;
            gap: 12px;
            align-items: stretch;
        }
        
        .sms-container .form-input {
            flex: 1;
        }
        
        .sms-btn {
            background: var(--gradient-secondary);
            border: none;
            color: white;
            padding: 16px 20px;
            border-radius: 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-purple);
            position: relative;
            overflow: hidden;
        }
        
        .sms-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .sms-btn:hover::before {
            left: 100%;
        }
        
        .sms-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
        }
        
        .sms-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .forgot-password {
            text-align: right;
            margin-top: 8px;
        }
        
        .forgot-password a {
            color: var(--secondary-purple-light);
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .forgot-password a:hover {
            color: var(--secondary-purple);
            text-decoration: underline;
        }
        
        .btn {
            display: inline-block;
            padding: 16px 24px;
            border-radius: 16px;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            margin: 8px 0;
            width: 100%;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }
        
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover::before {
            left: 100%;
        }
        
        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-gold);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(200, 134, 13, 0.4);
        }
        
        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .divider {
            text-align: center;
            margin: 32px 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--neutral-600), transparent);
        }
        
        .divider span {
            background: var(--bg-primary);
            padding: 0 20px;
            color: var(--neutral-400);
            font-size: 14px;
            position: relative;
            z-index: 1;
        }
        
        .social-login {
            display: flex;
            gap: 16px;
            margin-bottom: 32px;
        }
        
        .btn-social {
            flex: 1;
            padding: 16px;
            background: var(--gradient-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            color: var(--neutral-200);
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }
        
        .btn-social::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .btn-social:hover::before {
            left: 100%;
        }
        
        .btn-social:hover {
            border-color: var(--primary-gold);
            color: var(--neutral-100);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .btn-social.wechat:hover {
            border-color: #00C851;
            color: #00C851;
        }
        
        .btn-social.alipay:hover {
            border-color: #1677FF;
            color: #1677FF;
        }
        
        .register-prompt {
            text-align: center;
            padding: 24px 32px;
            background: var(--gradient-card);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: var(--shadow-md);
        }
        
        .register-prompt p {
            color: var(--neutral-400);
            font-size: 14px;
            margin-bottom: 12px;
        }
        
        .register-prompt a {
            color: var(--secondary-purple-light);
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .register-prompt a:hover {
            color: var(--secondary-purple);
            text-decoration: underline;
        }
        
        @media (max-width: 480px) {
            .container {
                padding: 16px;
            }
            
            .logo-section {
                padding: 30px 0 40px;
            }
            
            .logo {
                width: 80px;
                height: 80px;
                font-size: 32px;
            }
            
            .welcome-text {
                font-size: 22px;
            }
            
            .login-form {
                padding: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="tech-particles" id="techParticles"></div>
    
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 style="font-size: 18px; font-weight: 600; color: var(--neutral-100);">登录</h1>
        </div>
        
        <div class="logo-section">
            <div class="logo-container">
                <div class="logo">
                    <i class="fas fa-sun"></i>
                </div>
            </div>
            <div class="welcome-text">欢迎回来</div>
            <div class="subtitle">登录您的数字资产账户</div>
        </div>
        
        <div class="login-form">
            <div class="tab-switcher">
                <button class="tab-btn active" onclick="switchTab('sms')">
                    <i class="fas fa-mobile"></i> 验证码登录
                </button>
                <button class="tab-btn" onclick="switchTab('password')">
                    <i class="fas fa-lock"></i> 密码登录
                </button>
            </div>
            
            <!-- 验证码登录 -->
            <div class="form-content active" id="smsForm">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-mobile"></i>
                        手机号码
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="tel" 
                            class="form-input" 
                            placeholder="请输入手机号码"
                            maxlength="11"
                            id="phoneNumber"
                            required
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-shield"></i>
                        短信验证码
                    </label>
                    <div class="sms-container">
                        <input 
                            type="text" 
                            class="form-input" 
                            placeholder="请输入验证码"
                            maxlength="6"
                            id="smsCode"
                            required
                        >
                        <button type="button" class="sms-btn" id="sendSmsBtn" onclick="sendSMS()">
                            获取验证码
                        </button>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="loginWithSMS()">
                    <i class="fas fa-sign-in"></i> 登录
                </button>
            </div>
            
            <!-- 密码登录 -->
            <div class="form-content" id="passwordForm">
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i>
                        手机号/用户名
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="text" 
                            class="form-input" 
                            placeholder="请输入手机号或用户名"
                            id="username"
                            required
                        >
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock"></i>
                        登录密码
                    </label>
                    <div class="input-wrapper">
                        <input 
                            type="password" 
                            class="form-input" 
                            placeholder="请输入密码"
                            id="password"
                            required
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordIcon"></i>
                        </button>
                    </div>
                    <div class="forgot-password">
                        <a href="#" onclick="showForgotPassword()">忘记密码？</a>
                    </div>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="loginWithPassword()">
                    <i class="fas fa-sign-in"></i> 登录
                </button>
            </div>
        </div>
        
        <div class="divider">
            <span>或使用第三方登录</span>
        </div>
        
        <div class="social-login">
            <a href="#" class="btn-social wechat" onclick="wechatLogin()">
                <i class="fab fa-weixin"></i>
                微信
            </a>
            <a href="#" class="btn-social alipay" onclick="alipayLogin()">
                <i class="fab fa-alipay"></i>
                支付宝
            </a>
        </div>
        
        <div class="register-prompt">
            <p>还没有账户？</p>
            <a href="register.html">立即注册 <i class="fas fa-arrow-right"></i></a>
        </div>
    </div>

    <script>
        // 创建科技粒子效果
        function createTechParticles() {
            const container = document.getElementById('techParticles');
            const particleCount = 50;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                container.appendChild(particle);
            }
        }
        
        // 页面加载时创建粒子效果
        document.addEventListener('DOMContentLoaded', createTechParticles);
        
        let smsCountdown = 0;
        
        function switchTab(tabType) {
            // 切换标签按钮状态
            document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            // 切换表单内容
            document.querySelectorAll('.form-content').forEach(content => content.classList.remove('active'));
            if (tabType === 'sms') {
                document.getElementById('smsForm').classList.add('active');
            } else {
                document.getElementById('passwordForm').classList.add('active');
            }
        }
        
        function sendSMS() {
            const phone = document.getElementById('phoneNumber').value;
            
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                showNotification('请输入正确的手机号码', 'error');
                return;
            }
            
            if (smsCountdown > 0) return;
            
            // 模拟发送短信
            showNotification('验证码已发送到您的手机', 'success');
            
            // 开始倒计时
            const smsBtn = document.getElementById('sendSmsBtn');
            smsCountdown = 60;
            smsBtn.disabled = true;
            
            const timer = setInterval(() => {
                if (smsCountdown <= 0) {
                    clearInterval(timer);
                    smsBtn.disabled = false;
                    smsBtn.innerHTML = '获取验证码';
                } else {
                    smsBtn.innerHTML = `${smsCountdown}s后重发`;
                    smsCountdown--;
                }
            }, 1000);
        }
        
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('passwordIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }
        
        function loginWithSMS() {
            const phone = document.getElementById('phoneNumber').value;
            const code = document.getElementById('smsCode').value;
            
            if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
                showNotification('请输入正确的手机号码', 'error');
                return;
            }
            
            if (!code || code.length !== 6) {
                showNotification('请输入6位验证码', 'error');
                return;
            }
            
            // 模拟登录过程
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
            btn.disabled = true;
            
            setTimeout(() => {
                showNotification('登录成功！欢迎回来', 'success');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }, 1500);
        }
        
        function loginWithPassword() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username.trim()) {
                showNotification('请输入手机号或用户名', 'error');
                return;
            }
            
            if (!password || password.length < 6) {
                showNotification('密码长度不能少于6位', 'error');
                return;
            }
            
            // 模拟登录过程
            const btn = event.target;
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';
            btn.disabled = true;
            
            setTimeout(() => {
                showNotification('登录成功！欢迎回来', 'success');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1000);
            }, 1500);
        }
        
        function showForgotPassword() {
            showNotification('请联系客服重置密码：400-1234-5678', 'info');
        }
        
        function wechatLogin() {
            showNotification('正在跳转到微信登录...', 'info');
        }
        
        function alipayLogin() {
            showNotification('正在跳转到支付宝登录...', 'info');
        }
        
        // 通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                ${message}
            `;
            
            // 添加通知样式
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--accent-green)' : type === 'error' ? 'var(--accent-red)' : 'var(--secondary-purple)'};
                color: white;
                padding: 16px 20px;
                border-radius: 12px;
                font-size: 14px;
                font-weight: 500;
                box-shadow: var(--shadow-lg);
                z-index: 1000;
                animation: slideIn 0.3s ease-out;
                backdrop-filter: blur(20px);
                display: flex;
                align-items: center;
                gap: 8px;
                max-width: 300px;
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }
        
        // 添加滑入滑出动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        // 输入框动画效果
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.01)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
        
        // 手机号格式化
        document.getElementById('phoneNumber').addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });
        
        // 验证码输入限制
        document.getElementById('smsCode').addEventListener('input', function() {
            this.value = this.value.replace(/\D/g, '');
        });
    </script>
</body>
</html> 
