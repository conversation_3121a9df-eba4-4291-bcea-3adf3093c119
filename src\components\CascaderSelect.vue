<template>
  <div class="cascader-select" :class="{ 'is-open': isOpen, 'is-disabled': disabled }">
    <div 
      class="cascader-trigger" 
      @click="toggleDropdown"
      :tabindex="disabled ? -1 : 0"
      @keydown.enter="toggleDropdown"
      @keydown.space.prevent="toggleDropdown"
      @keydown.esc="closeDropdown"
    >
      <span class="cascader-value" :class="{ 'is-placeholder': !displayText }">
        {{ displayText || placeholder }}
      </span>
      <i class="cascader-arrow fas fa-chevron-down"></i>
    </div>
    
    <transition name="dropdown">
      <div v-if="isOpen" class="cascader-dropdown">
        <div class="dropdown-content">
          <div class="cascader-panels">
            <!-- 省份面板 -->
            <div class="cascader-panel">
              <div class="panel-header">省份</div>
              <div class="panel-options">
                <div 
                  v-for="province in provinceOptions" 
                  :key="province.value"
                  class="cascader-option"
                  :class="{ 
                    'is-selected': province.value === selectedProvince?.value,
                    'is-active': province.value === selectedProvince?.value
                  }"
                  @click="selectProvince(province)"
                >
                  <span class="option-label">{{ province.label }}</span>
                  <i v-if="province.value === selectedProvince?.value" class="option-check fas fa-check"></i>
                </div>
                <div v-if="provinceOptions.length === 0" class="panel-empty">
                  暂无省份数据
                </div>
              </div>
            </div>
            
            <!-- 城市面板 -->
            <div v-if="selectedProvince" class="cascader-panel">
              <div class="panel-header">
                城市
                <span v-if="cityOptions.length > 0" class="panel-count">({{ cityOptions.length }})</span>
              </div>
              <div class="panel-options">
                <div v-if="isLoadingCities" class="panel-loading">
                  <i class="fas fa-spinner fa-spin"></i>
                  <span>正在加载城市数据...</span>
                </div>
                <div 
                  v-for="city in cityOptions" 
                  :key="city.value"
                  class="cascader-option"
                  :class="{ 
                    'is-selected': city.value === selectedCity?.value,
                    'is-active': city.value === selectedCity?.value
                  }"
                  @click="selectCity(city)"
                >
                  <span class="option-label">{{ city.label }}</span>
                  <i v-if="city.value === selectedCity?.value" class="option-check fas fa-check"></i>
                </div>
                <div v-if="!isLoadingCities && cityOptions.length === 0" class="panel-empty">
                  <i class="fas fa-info-circle"></i>
                  <div class="empty-text">
                    <div>暂无城市数据</div>
                    <small>加载失败或该省份没有城市数据</small>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 区县面板 -->
            <div v-if="selectedCity" class="cascader-panel">
              <div class="panel-header">
                区县
                <span v-if="districtOptions.length > 0" class="panel-count">({{ districtOptions.length }})</span>
              </div>
              <div class="panel-options">
                <div v-if="isLoadingDistricts" class="panel-loading">
                  <i class="fas fa-spinner fa-spin"></i>
                  <span>正在加载区县数据...</span>
                </div>
                <div 
                  v-for="district in districtOptions" 
                  :key="district.value"
                  class="cascader-option"
                  :class="{ 
                    'is-selected': district.value === selectedDistrict?.value,
                    'is-active': district.value === selectedDistrict?.value
                  }"
                  @click="selectDistrict(district)"
                >
                  <span class="option-label">{{ district.label }}</span>
                  <i v-if="district.value === selectedDistrict?.value" class="option-check fas fa-check"></i>
                </div>
                <div v-if="!isLoadingDistricts && districtOptions.length === 0" class="panel-empty">
                  <i class="fas fa-info-circle"></i>
                  <div class="empty-text">
                    <div>暂无区县数据</div>
                    <small>该城市可能没有区县级行政区划</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import type { DictionaryItem } from '@/api/dictionary'
import DictionaryAPI from '@/api/dictionary'

interface Props {
  modelValue: {
    province?: string
    city?: string
    district?: string
  }
  provinceOptions: DictionaryItem[]
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: { province?: string, city?: string, district?: string }): void
  (e: 'change', value: { province?: string, city?: string, district?: string }, labels: { province?: string, city?: string, district?: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择省/市/区',
  disabled: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const isOpen = ref(false)
const cityOptions = ref<DictionaryItem[]>([])
const districtOptions = ref<DictionaryItem[]>([])
const isLoadingCities = ref(false)
const isLoadingDistricts = ref(false)

// 计算属性
const selectedProvince = computed(() => {
  return props.provinceOptions.find(p => p.value === props.modelValue.province)
})

const selectedCity = computed(() => {
  return cityOptions.value.find(c => c.value === props.modelValue.city)
})

const selectedDistrict = computed(() => {
  return districtOptions.value.find(d => d.value === props.modelValue.district)
})

const displayText = computed(() => {
  const parts = []
  if (selectedProvince.value) parts.push(selectedProvince.value.label)
  if (selectedCity.value) parts.push(selectedCity.value.label)
  if (selectedDistrict.value) parts.push(selectedDistrict.value.label)
  return parts.join(' / ')
})

// 方法
const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
}

const closeDropdown = () => {
  isOpen.value = false
}

const selectProvince = async (province: DictionaryItem) => {
  // 重置城市和区县
  cityOptions.value = []
  districtOptions.value = []
  
  const newValue = {
    province: province.value,
    city: undefined,
    district: undefined
  }
  
  emit('update:modelValue', newValue)
  
  // 加载城市数据
  if (province.value) {
    isLoadingCities.value = true
    try {
      const response = await DictionaryAPI.getCitiesByProvinceId(province.value)
      if (response.code === 200 && response.data) {
        cityOptions.value = response.data
      }
    } catch (err) {
      console.warn('加载城市数据失败:', err)
      // 提供默认数据
      if (province.value === '510000') {
        cityOptions.value = [
          { value: '510100', label: '成都市', code: '510100', type: 'city', sort: 1, isDefault: false, status: 'active' },
          { value: '510700', label: '绵阳市', code: '510700', type: 'city', sort: 2, isDefault: false, status: 'active' }
        ]
      }
    } finally {
      isLoadingCities.value = false
    }
  }
  
  emitChange()
}

const selectCity = async (city: DictionaryItem) => {
  console.log('选择城市:', city.label, '城市ID:', city.value)
  
  // 重置区县
  districtOptions.value = []
  
  const newValue = {
    ...props.modelValue,
    city: city.value,
    district: undefined
  }
  
  emit('update:modelValue', newValue)
  
  // 加载区县数据
  if (city.value) {
    isLoadingDistricts.value = true
    console.log('开始加载区县数据，城市ID:', city.value)
    
    try {
      const response = await DictionaryAPI.getDistrictsByCityId(city.value)
      console.log('区县数据API响应:', response)
      
      if (response.code === 200 && response.data) {
        districtOptions.value = response.data
        console.log(`成功加载${city.label}的区县数据:`, response.data.length, '个区县')
        console.log('区县列表:', response.data.map(d => d.label).join(', '))
      } else {
        console.warn('区县数据API返回错误:', response)
      }
    } catch (err) {
      console.warn('加载区县数据失败:', err)
      // 提供默认数据
      if (city.value === '510100') {
        console.log('使用成都市默认区县数据')
        districtOptions.value = [
          { value: '510104', label: '锦江区', code: '510104', type: 'district', sort: 1, isDefault: false, status: 'active' },
          { value: '510105', label: '青羊区', code: '510105', type: 'district', sort: 2, isDefault: false, status: 'active' },
          { value: '510106', label: '金牛区', code: '510106', type: 'district', sort: 3, isDefault: false, status: 'active' },
          { value: '510107', label: '武侯区', code: '510107', type: 'district', sort: 4, isDefault: false, status: 'active' },
          { value: '510108', label: '成华区', code: '510108', type: 'district', sort: 5, isDefault: false, status: 'active' }
        ]
      } else {
        console.log('该城市没有提供默认区县数据')
      }
    } finally {
      isLoadingDistricts.value = false
      console.log('区县数据加载完成，共', districtOptions.value.length, '个区县')
    }
  }
  
  emitChange()
}

const selectDistrict = (district: DictionaryItem) => {
  const newValue = {
    ...props.modelValue,
    district: district.value
  }
  
  emit('update:modelValue', newValue)
  emitChange()
  closeDropdown()
}

const emitChange = () => {
  const labels = {
    province: selectedProvince.value?.label,
    city: selectedCity.value?.label,
    district: selectedDistrict.value?.label
  }
  emit('change', props.modelValue, labels)
}

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.cascader-select')) {
    closeDropdown()
  }
}

// 监听器
watch(() => props.modelValue.province, async (newProvince) => {
  if (newProvince && props.modelValue.city) {
    // 如果有选中的省份和城市，重新加载城市数据
    try {
      const response = await DictionaryAPI.getCitiesByProvinceId(newProvince)
      if (response.code === 200 && response.data) {
        cityOptions.value = response.data
      }
    } catch (err) {
      console.warn('重新加载城市数据失败:', err)
    }
  }
})

watch(() => props.modelValue.city, async (newCity) => {
  if (newCity && props.modelValue.district) {
    // 如果有选中的城市和区县，重新加载区县数据
    try {
      const response = await DictionaryAPI.getDistrictsByCityId(newCity)
      if (response.code === 200 && response.data) {
        districtOptions.value = response.data
      }
    } catch (err) {
      console.warn('重新加载区县数据失败:', err)
    }
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.cascader-select {
  position: relative;
  width: 100%;
}

.cascader-trigger {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 14px 16px;
  color: var(--neutral-100);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.cascader-trigger:hover {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 20px rgba(200, 134, 13, 0.15);
}

.cascader-trigger:focus {
  outline: none;
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(200, 134, 13, 0.2);
}

.is-open .cascader-trigger {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 20px rgba(200, 134, 13, 0.2);
}

.is-disabled .cascader-trigger {
  opacity: 0.6;
  cursor: not-allowed;
  background: rgba(255, 255, 255, 0.02);
}

.cascader-value {
  flex: 1;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cascader-value.is-placeholder {
  color: var(--neutral-400);
}

.cascader-arrow {
  font-size: 12px;
  color: var(--neutral-400);
  transition: transform 0.3s ease;
  flex-shrink: 0;
}

.is-open .cascader-arrow {
  transform: rotate(180deg);
  color: var(--primary-gold);
}

.cascader-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-content {
  background: var(--gradient-card);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

.cascader-panels {
  display: flex;
  min-height: 200px;
  max-height: 300px;
}

.cascader-panel {
  flex: 1;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.cascader-panel:last-child {
  border-right: none;
}

.panel-header {
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-gold);
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.panel-count {
  font-size: 10px;
  color: var(--neutral-400);
  font-weight: 400;
}

.panel-options {
  flex: 1;
  overflow-y: auto;
}

.cascader-option {
  padding: 12px 16px;
  color: var(--neutral-100);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.cascader-option:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--primary-gold-light);
}

.cascader-option.is-selected {
  background: var(--primary-gold-alpha);
  color: var(--primary-gold);
}

.cascader-option.is-active {
  background: var(--primary-gold-alpha);
  color: var(--primary-gold);
}

.option-label {
  flex: 1;
  text-align: left;
}

.option-check {
  font-size: 12px;
  color: var(--primary-gold);
  flex-shrink: 0;
}

.panel-empty {
  padding: 20px 16px;
  text-align: center;
  color: var(--neutral-400);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.empty-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.empty-text small {
  font-size: 12px;
  color: var(--neutral-500);
  opacity: 0.8;
}

.panel-loading {
  padding: 20px 16px;
  text-align: center;
  color: var(--neutral-300);
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.panel-loading i {
  font-size: 16px;
  color: var(--primary-gold);
}

/* 下拉动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.dropdown-enter-to,
.dropdown-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}

/* 滚动条样式 */
.panel-options::-webkit-scrollbar {
  width: 4px;
}

.panel-options::-webkit-scrollbar-track {
  background: transparent;
}

.panel-options::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.panel-options::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式适配 */
@media (max-width: 640px) {
  .cascader-panels {
    flex-direction: column;
    max-height: 250px;
  }
  
  .cascader-panel {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .cascader-panel:last-child {
    border-bottom: none;
  }
  
  .panel-options {
    max-height: 80px;
  }
}
</style> 