<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>我的收藏 - 四川省数字资产发行平�?/title>
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sichuan-red': '#d4313b',
                        'sichuan-gold': '#daa520',
                        'sichuan-dark': '#1a1a1a',
                        'sichuan-gray': '#2a2a2a'
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            /* 太阳神鸟配色方案 */
            --primary-gold: #C8860D;           
            --primary-gold-light: #E8A317;     
            --primary-gold-dark: #A67109;      
            --primary-gold-alpha: rgba(200, 134, 13, 0.1);
            --secondary-blue: #1E3A8A;         
            --secondary-blue-light: #3B82F6;   
            --bg-primary: #0F0F23;             
            --bg-secondary: #1A1B3A;           
            --bg-tertiary: #252759;            
            --bg-card: rgba(37, 39, 89, 0.9);
            --neutral-100: #F8FAFC;            
            --neutral-200: #E2E8F0;            
            --neutral-400: #94A3B8;            
            --accent-red: #DC2626;             
            --accent-green: #059669;           
            --gradient-primary: linear-gradient(135deg, var(--primary-gold) 0%, var(--primary-gold-light) 100%);
            --gradient-hero: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
            --gradient-card: linear-gradient(145deg, var(--bg-card) 0%, rgba(26, 27, 58, 0.9) 100%);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--gradient-hero);
            color: var(--neutral-100);
        }
        .glass-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-sichuan-dark to-sichuan-gray">
    <!-- 顶部导航�?-->
    <header class="sticky top-0 z-50 backdrop-blur-lg bg-black/30 border-b border-white/10">
        <div class="max-w-md mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="index.html" class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </a>
                    <h1 class="text-lg font-bold text-white">我的收藏</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                    <button class="text-white/70 hover:text-white transition-colors">
                        <i class="fas fa-ellipsis-v text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-md mx-auto px-4 pb-20">
        <!-- 统计概览 -->
        <section class="py-6">
            <div class="glass-card rounded-2xl p-6">
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <div class="text-2xl font-bold text-sichuan-gold mb-1">12</div>
                        <div class="text-white/60 text-sm">总收�?/div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-green-400 mb-1">�?,890</div>
                        <div class="text-white/60 text-sm">总价�?/div>
                    </div>
                    <div>
                        <div class="text-2xl font-bold text-blue-400 mb-1">8</div>
                        <div class="text-white/60 text-sm">限量�?/div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 筛选选项 -->
        <section class="mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-white font-semibold">筛选分�?/h3>
                <button class="text-sichuan-gold text-sm">
                    <i class="fas fa-sort mr-1"></i>排序
                </button>
            </div>
            <div class="flex space-x-2 overflow-x-auto pb-2">
                <button class="whitespace-nowrap px-4 py-2 bg-sichuan-gold text-white rounded-full text-sm font-medium">
                    全部
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    古蜀文明
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    三星�?
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    金沙遗址
                </button>
                <button class="whitespace-nowrap px-4 py-2 glass-card text-white/70 rounded-full text-sm">
                    限量�?
                </button>
            </div>
        </section>

        <!-- 收藏网格 -->
        <section class="grid grid-cols-2 gap-4">
            <!-- 收藏卡片1 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="太阳神鸟" class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-sichuan-red text-white px-2 py-1 rounded-full">限量</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">太阳神鸟金饰</h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?99</span>
                        <span class="text-xs text-white/50">#0892</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-15</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 收藏卡片2 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="青铜面具" class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-green-500 text-white px-2 py-1 rounded-full">热门</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">三星堆青铜面�?/h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?99</span>
                        <span class="text-xs text-white/50">#0234</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-10</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 收藏卡片3 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="金沙遗址" class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-purple-500 text-white px-2 py-1 rounded-full">3D</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">金沙遗址全景</h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?99</span>
                        <span class="text-xs text-white/50">#0156</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-08</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 收藏卡片4 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="古蜀玉器" class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-blue-500 text-white px-2 py-1 rounded-full">精品</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">古蜀玉器收藏</h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?59</span>
                        <span class="text-xs text-white/50">#0067</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-05</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 收藏卡片5 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="蜀锦织�? class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-yellow-500 text-white px-2 py-1 rounded-full">新品</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">蜀锦织品纹�?/h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?9</span>
                        <span class="text-xs text-white/50">#0445</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-03</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 收藏卡片6 -->
            <div class="glass-card rounded-2xl overflow-hidden hover-scale">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=200&fit=crop" 
                         alt="巴蜀印章" class="w-full h-32 object-cover">
                    <div class="absolute top-2 right-2">
                        <span class="text-xs bg-indigo-500 text-white px-2 py-1 rounded-full">珍藏</span>
                    </div>
                    <div class="absolute top-2 left-2">
                        <button class="w-8 h-8 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70 transition-colors">
                            <i class="fas fa-heart text-red-400"></i>
                        </button>
                    </div>
                </div>
                <div class="p-4">
                    <h4 class="text-white font-semibold mb-2">巴蜀印章系列</h4>
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sichuan-gold font-bold">�?29</span>
                        <span class="text-xs text-white/50">#0321</span>
                    </div>
                    <div class="text-xs text-white/60 mb-3">购买�?2024-01-01</div>
                    <div class="flex space-x-2">
                        <button class="flex-1 bg-sichuan-gold/20 text-sichuan-gold py-2 rounded-lg text-xs font-medium hover:bg-sichuan-gold/30 transition-colors">
                            查看详情
                        </button>
                        <button class="px-3 py-2 glass-card rounded-lg text-xs">
                            <i class="fas fa-share"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 空状态（当没有收藏时显示，这里隐藏） -->
        <section class="hidden text-center py-12">
            <div class="glass-card rounded-2xl p-8">
                <div class="w-16 h-16 bg-sichuan-gold/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-gem text-sichuan-gold text-2xl"></i>
                </div>
                <h3 class="text-white font-semibold mb-2">暂无收藏</h3>
                <p class="text-white/60 text-sm mb-6">去市场逛逛，收藏喜欢的数字藏品吧</p>
                <a href="market.html" class="bg-sichuan-gold text-white px-8 py-3 rounded-full text-sm font-medium inline-block hover:bg-sichuan-gold/80 transition-colors">
                    去逛�?
                </a>
            </div>
        </section>
    </main>

    <!-- 底部导航�?-->
    <nav class="fixed bottom-0 left-0 right-0 z-50 backdrop-blur-lg bg-black/30 border-t border-white/10">
        <div class="max-w-md mx-auto px-4 py-2">
            <div class="flex items-center justify-around">
                <a href="index.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs">首页</span>
                </a>
                <a href="market.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-store text-lg mb-1"></i>
                    <span class="text-xs">市场</span>
                </a>
                <a href="collection.html" class="flex flex-col items-center py-2 px-3 text-sichuan-gold">
                    <i class="fas fa-gem text-lg mb-1"></i>
                    <span class="text-xs">收藏</span>
                </a>
                <a href="profile.html" class="flex flex-col items-center py-2 px-3 text-white/70 hover:text-white transition-colors">
                    <i class="fas fa-user text-lg mb-1"></i>
                    <span class="text-xs">我的</span>
                </a>
            </div>
        </div>
    </nav>

    <script>
        // 分类筛�?
        document.querySelectorAll('section:nth-child(3) button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有active状�?
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.classList.remove('bg-sichuan-gold', 'text-white');
                    btn.classList.add('glass-card', 'text-white/70');
                });
                
                // 添加active状�?
                this.classList.remove('glass-card', 'text-white/70');
                this.classList.add('bg-sichuan-gold', 'text-white');
            });
        });

        // 收藏按钮
        document.querySelectorAll('.fa-heart').forEach(heart => {
            heart.parentElement.addEventListener('click', function(e) {
                e.stopPropagation();
                heart.classList.toggle('text-red-400');
                heart.classList.toggle('text-white/50');
            });
        });

        // 查看详情按钮
        document.querySelectorAll('button:contains("查看详情")').forEach(button => {
            button.addEventListener('click', function() {
                alert('查看藏品详情');
            });
        });

        // 分享按钮
        document.querySelectorAll('.fa-share').forEach(share => {
            share.parentElement.addEventListener('click', function(e) {
                e.stopPropagation();
                alert('分享藏品');
            });
        });
    </script>
</body>
</html> 
