<template>
  <button 
    :class="buttonClasses"
    @click="$emit('click', $event)"
  >
    <i v-if="icon" :class="icon"></i>
    <slot></slot>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'primary' | 'secondary' | 'accent'
  size?: 'small' | 'normal' | 'large'
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'primary',
  size: 'normal'
})

defineEmits<{
  click: [event: MouseEvent]
}>()

const buttonClasses = computed(() => [
  'btn',
  `btn-${props.type}`,
  props.size !== 'normal' && `btn-${props.size}`,
  props.icon && 'btn-icon'
])
</script> 