{"openapi": "3.0.1", "info": {"title": "川文数服", "description": "", "version": "1.0.0"}, "tags": [{"name": "文件服务"}], "paths": {"/file/cosUpload": {"post": {"summary": "通用上传", "x-apifox-folder": "文件服务", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["文件服务"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "description": "文件", "example": "file://D:\\工作\\TestData\\测试文件\\3333.jpg", "format": "binary"}, "attachmentObjectTypeCode": {"type": "string", "description": "文件类型code", "example": "assetCover"}, "fileDesc": {"type": "string", "description": "文件描述", "example": "测试图片"}}}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"msg": {"type": "string"}, "code": {"type": "integer"}}, "required": ["msg", "code"], "x-apifox-ignore-properties": [], "x-apifox-orders": ["msg", "code"]}, "examples": {"1": {"summary": "成功示例", "value": {"code": 200, "msg": "操作成功", "data": {"attachId": 1725, "attachmentObjectTypeCode": "assetCover", "fileName": "3333.jpg", "mimeType": "image/jpeg", "fileSize": "522824", "fileDesc": "测试图片", "createDate": "2025-01-14T16:49:49.632", "filePath": "asset-cover/9094174b-ae63-447e-bc3e-734748304537.jpg", "fileUrl": "https://trade.sccdex.com/asset-cover/9094174b-ae63-447e-bc3e-734748304537.jpg", "fileScalePath": null, "hashCode": "ef07ca3f42b39519b7ee87361f961170", "statusCd": "1000", "statusDate": "2025-01-14T16:49:49.632", "createStaff": 101, "updateStaff": null, "updateDate": null, "remark": null}}}}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-226548017-run", "security": []}}, "/file/preUpload": {"post": {"summary": "签名URL上传", "x-apifox-folder": "文件服务", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["文件服务"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVjMGQ3MGUzLTg0YTMtNDBhZS1hNWQ0LWVjMjlkZWVkOTVhNSJ9.gBYLJUxMvVSjehxY7ZOutAcuk_2cqcZS4mI5hEXTwTf9_5VNjFTwKLSa1ezVKlb-6-UzHLAmNL6Jhh1JzBOGyA", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"attachmentObjectTypeCode": {"type": "string", "description": "附件所属对象类型"}, "originalFilename": {"type": "string", "description": "附件源文件名称"}, "fileSize": {"type": "string", "description": "附件大小"}, "fileDesc": {"type": "string", "description": "附件描述"}, "md5": {"type": "string", "description": "附件md5"}}, "x-apifox-orders": ["attachmentObjectTypeCode", "originalFilename", "fileSize", "fileDesc", "md5"], "required": ["attachmentObjectTypeCode", "originalFilename", "fileSize", "fileDesc", "md5"], "x-apifox-ignore-properties": []}, "example": {"attachmentObjectTypeCode": "assetCover", "originalFilename": "测试文件", "fileSize": "2222", "fileDesc": "fugiat", "md5": "ef07ca3f42b39519b7ee87361f961170"}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/5331762/apis/api-232841942-run", "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [{"url": "https://ts.sccdex.com", "description": "测试环境"}]}