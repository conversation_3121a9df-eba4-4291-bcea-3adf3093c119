<template>
  <div class="custom-select" :class="{ 'is-open': isOpen, 'is-disabled': disabled }">
    <div 
      class="select-trigger" 
      @click="toggleDropdown"
      :tabindex="disabled ? -1 : 0"
      @keydown.enter="toggleDropdown"
      @keydown.space.prevent="toggleDropdown"
      @keydown.esc="closeDropdown"
      @keydown.down.prevent="navigateOptions(1)"
      @keydown.up.prevent="navigateOptions(-1)"
    >
      <span class="select-value" :class="{ 'is-placeholder': !selectedOption }">
        {{ displayValue }}
      </span>
      <i class="select-arrow fas fa-chevron-down"></i>
    </div>
    
    <transition name="dropdown">
      <div v-if="isOpen" class="select-dropdown">
        <div class="dropdown-content">
          <div 
            v-for="(option, index) in options" 
            :key="option.value"
            class="select-option"
            :class="{ 
              'is-selected': option.value === modelValue,
              'is-highlighted': highlightedIndex === index 
            }"
            @click="selectOption(option)"
            @mouseenter="highlightedIndex = index"
          >
            <span class="option-label">{{ option.label }}</span>
            <i v-if="option.value === modelValue" class="option-check fas fa-check"></i>
          </div>
          <div v-if="options.length === 0" class="select-empty">
            暂无数据
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import type { DictionaryItem } from '@/api/dictionary'

interface Props {
  modelValue: string
  options: DictionaryItem[]
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string, option: DictionaryItem | null): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
  disabled: false
})

const emit = defineEmits<Emits>()

// 响应式数据
const isOpen = ref(false)
const highlightedIndex = ref(-1)

// 计算属性
const selectedOption = computed(() => {
  return props.options.find(option => option.value === props.modelValue)
})

const displayValue = computed(() => {
  return selectedOption.value?.label || props.placeholder
})

// 方法
const toggleDropdown = () => {
  if (props.disabled) return
  isOpen.value = !isOpen.value
  if (isOpen.value) {
    // 高亮当前选中项
    const selectedIndex = props.options.findIndex(option => option.value === props.modelValue)
    highlightedIndex.value = selectedIndex >= 0 ? selectedIndex : 0
  }
}

const closeDropdown = () => {
  isOpen.value = false
  highlightedIndex.value = -1
}

const selectOption = (option: DictionaryItem) => {
  emit('update:modelValue', option.value)
  emit('change', option.value, option)
  closeDropdown()
}

const navigateOptions = (direction: number) => {
  if (!isOpen.value || props.options.length === 0) return
  
  const newIndex = highlightedIndex.value + direction
  if (newIndex >= 0 && newIndex < props.options.length) {
    highlightedIndex.value = newIndex
  } else if (direction > 0) {
    highlightedIndex.value = 0
  } else {
    highlightedIndex.value = props.options.length - 1
  }
}

// 点击外部关闭下拉框
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.custom-select')) {
    closeDropdown()
  }
}

// 监听器
watch(() => props.options, () => {
  if (isOpen.value && highlightedIndex.value >= props.options.length) {
    highlightedIndex.value = Math.max(0, props.options.length - 1)
  }
})

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.custom-select {
  position: relative;
  width: 100%;
}

.select-trigger {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 14px 16px;
  color: var(--neutral-100);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
}

.select-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-tech);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.select-trigger:hover::before {
  opacity: 1;
}

.select-trigger:hover {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 20px rgba(200, 134, 13, 0.15);
}

.select-trigger:focus {
  outline: none;
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(200, 134, 13, 0.2);
}

.is-open .select-trigger {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 4px 20px rgba(200, 134, 13, 0.2);
}

.is-open .select-trigger::before {
  opacity: 0.7;
}

.is-disabled .select-trigger {
  cursor: not-allowed;
  opacity: 0.5;
  background: rgba(255, 255, 255, 0.02);
}

.select-value {
  flex: 1;
  text-align: left;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.select-value.is-placeholder {
  color: var(--neutral-400);
  font-weight: 400;
}

.select-arrow {
  font-size: 12px;
  color: var(--neutral-400);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.is-open .select-arrow {
  transform: rotate(180deg);
  color: var(--primary-gold);
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
}

.dropdown-content {
  background: var(--gradient-card);
  border: 1px solid rgba(200, 134, 13, 0.2);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 4px 20px rgba(200, 134, 13, 0.15);
  max-height: 240px;
  overflow-y: auto;
}

.dropdown-content::-webkit-scrollbar {
  width: 6px;
}

.dropdown-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dropdown-content::-webkit-scrollbar-thumb {
  background: var(--primary-gold);
  border-radius: 3px;
}

.dropdown-content::-webkit-scrollbar-thumb:hover {
  background: var(--primary-gold-light);
}

.select-option {
  padding: 12px 16px;
  color: var(--neutral-100);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.select-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--gradient-primary);
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: -1;
}

.select-option:hover::before,
.select-option.is-highlighted::before {
  opacity: 0.1;
}

.select-option:hover,
.select-option.is-highlighted {
  background: rgba(200, 134, 13, 0.1);
  color: var(--primary-gold-light);
}

.select-option.is-selected {
  background: rgba(200, 134, 13, 0.15);
  color: var(--primary-gold);
  font-weight: 600;
}

.select-option.is-selected::before {
  opacity: 0.2;
}

.option-label {
  flex: 1;
  text-align: left;
}

.option-check {
  font-size: 12px;
  color: var(--primary-gold);
  animation: checkmark-appear 0.3s ease;
}

.select-empty {
  padding: 16px;
  text-align: center;
  color: var(--neutral-400);
  font-size: 13px;
}

/* 动画效果 */
.dropdown-enter-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dropdown-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-4px) scale(0.98);
}

@keyframes checkmark-appear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .select-trigger {
    padding: 12px 14px;
    font-size: 13px;
  }
  
  .select-option {
    padding: 10px 14px;
    font-size: 13px;
  }
  
  .dropdown-content {
    max-height: 200px;
  }
}
</style> 