<template>
  <AppPageHeader :title="'实名认证'" @back="$router.back()" />

  <div class="verify-page" :style="{ paddingTop: '40px' }">
    <main class="main-content">


      <!-- 认证状态显示 -->
      <div v-if="verificationStatus.isVerified" class="status-card verified">
        <div class="status-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="status-content">
          <div class="status-title">认证已通过</div>
          <div class="status-desc">您已完成实名认证</div>
          <div class="verify-info">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{ verificationStatus.idName }}</span>
            </div>
            <div class="info-item">
              <span class="label">证件号：</span>
              <span class="value">{{ verificationStatus.idNo }}</span>
            </div>
            <div class="info-item">
              <span class="label">认证时间：</span>
              <span class="value">{{ formatDate(verificationStatus.statusDate) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证表单 -->
      <div v-else class="verify-form">
        <!-- 认证类型选择 -->
        <div class="form-section">
          <div class="section-title">认证类型</div>
          <div class="verify-type-tabs">
            <button 
              class="type-tab" 
              :class="{ active: verifyForm.identifyType === 'person' }"
              @click="switchVerifyType('person')"
            >
              <i class="fas fa-user"></i>
              个人认证
            </button>
            <button 
              class="type-tab" 
              :class="{ active: verifyForm.identifyType === 'enterprise' }"
              @click="switchVerifyType('enterprise')"
            >
              <i class="fas fa-building"></i>
              企业认证
            </button>
          </div>
          
          <!-- 证件类型选择 -->
          <div class="form-group" style="margin-top: 20px;">
            <label class="form-label">证件类型</label>
            <CustomSelect
              v-model="verifyForm.idType"
              :options="verifyForm.identifyType === 'person' ? personIdTypeOptions : idTypeOptions"
              placeholder="请选择证件类型"
              @change="onIdTypeChange"
            />
          </div>
        </div>

        <!-- 证件照片上传 -->
        <!-- <div class="form-section">
          <div class="section-title">
            {{ verifyForm.identifyType === 'person' ? '证件照片' : '营业执照' }}
          </div>
          <div class="upload-area">
            <div v-if="verifyForm.identifyType === 'person'" class="upload-item">
              <div class="upload-card" @click="uploadIdCard('front')">
                <div v-if="imagePreview.front" class="image-preview">
                  <img :src="imagePreview.front" alt="身份证正面" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">身份证正面</div>
                </div>
              </div>
            </div>
            <div v-if="verifyForm.identifyType === 'person'" class="upload-item">
              <div class="upload-card" @click="uploadIdCard('back')">
                <div v-if="imagePreview.back" class="image-preview">
                  <img :src="imagePreview.back" alt="身份证反面" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">身份证反面</div>
                </div>
              </div>
            </div>
            <div v-if="verifyForm.identifyType === 'enterprise'" class="upload-item full-width">
              <div class="upload-card" @click="uploadIdCard('business')">
                <div v-if="imagePreview.business" class="image-preview">
                  <img :src="imagePreview.business" alt="营业执照" />
                  <div class="image-overlay">
                    <i class="fas fa-edit"></i>
                    <span>重新上传</span>
                  </div>
                </div>
                <div v-else class="upload-placeholder">
                  <i class="fas fa-camera"></i>
                  <div class="upload-text">营业执照照片</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 个人认证表单 -->
        <div v-if="verifyForm.identifyType === 'person'" class="person-form">
          <div class="form-section">
            <div class="section-title">基本信息</div>
            
            <div class="form-group">
              <label class="form-label">真实姓名</label>
              <input 
                v-model="verifyForm.idName" 
                type="text" 
                class="form-input"
                placeholder="请输入您的真实姓名"
                maxlength="20"
                :readonly="fieldLocked.idName"
              />
            </div>

            <div class="form-group">
              <label class="form-label">证件号码</label>
              <input 
                v-model="verifyForm.idNo" 
                type="text" 
                class="form-input"
                placeholder="请输入证件号码"
                maxlength="30"
                :readonly="fieldLocked.idNo"
              />
            </div>

            <!-- <div class="form-group">
              <label class="form-label">证件到期时间</label>
              <div v-if="verifyForm.businessTerm === 'permanent' || verifyForm.businessTerm === '长期' || verifyForm.businessTerm === '长期有效'" class="permanent-expiry">
                <input 
                  value="长期有效" 
                  type="text" 
                  class="form-input permanent-input"
                  readonly
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToDateInput"
                  type="button"
                >
                  改为日期
                </button>
              </div>
              <div v-else class="date-expiry">
                <input 
                  v-model="verifyForm.businessTerm" 
                  type="date" 
                  class="form-input"
                  placeholder="请选择证件到期时间"
                  :readonly="fieldLocked.businessTerm"
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToPermanent"
                  type="button"
                >
                  设为长期
                </button>
              </div>
            </div> -->

            <div class="form-group">
              <label class="form-label">手机号码</label>
              <div class="phone-input-group">
                <input 
                  v-model="verifyForm.phone" 
                  type="tel" 
                  class="form-input"
                  placeholder="请输入手机号"
                  maxlength="11"
                  :readonly="fieldLocked.phone"
                />
                <button 
                  class="sms-btn" 
                  :disabled="smsCountdown > 0"
                  @click="sendSmsCode"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">验证码</label>
              <input 
                v-model="verifyForm.smsCode" 
                type="text" 
                class="form-input"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
            </div>

            <!-- <div class="form-group">
              <label class="form-label">详细地址</label>
              <div class="address-group">
                <CustomSelect
                  v-model="verifyForm.province"
                  :options="provinceOptions"
                  placeholder="请选择省份"
                  class="address-select"
                  @change="onProvinceChange"
                />
                <CustomSelect
                  v-model="verifyForm.city"
                  :options="cityOptions"
                  placeholder="请选择城市"
                  class="address-select"
                  :disabled="!verifyForm.province"
                />
              </div>
              <input 
                v-model="verifyForm.address" 
                type="text" 
                class="form-input"
                placeholder="请输入详细地址"
                maxlength="100"
                :readonly="fieldLocked.address"
              />
            </div> -->
          </div>


        </div>

        <!-- 企业认证表单 -->
        <div v-else class="enterprise-form">
          <div class="form-section">
            <div class="section-title">企业信息</div>
            
            <div class="form-group">
              <label class="form-label">企业名称</label>
              <input 
                v-model="verifyForm.idName" 
                type="text" 
                class="form-input"
                placeholder="请输入企业全称"
                maxlength="50"
                :readonly="fieldLocked.idName"
              />
            </div>

            <div class="form-group">
              <label class="form-label">统一社会信用代码</label>
              <input 
                v-model="verifyForm.idNo" 
                type="text" 
                class="form-input"
                placeholder="请输入统一社会信用代码"
                maxlength="30"
                :readonly="fieldLocked.idNo"
              />
            </div>

            <div class="form-group">
              <label class="form-label">法人姓名</label>
              <input 
                v-model="verifyForm.legalName" 
                type="text" 
                class="form-input"
                placeholder="请输入法人姓名"
                maxlength="20"
                :readonly="fieldLocked.legalName"
              />
            </div>

            <!-- <div class="form-group">
              <label class="form-label">营业地址</label>
              <div class="address-group">
                <CustomSelect
                  v-model="verifyForm.enterpriseProvince"
                  :options="provinceOptions"
                  placeholder="省份"
                  class="address-select"
                  @change="onEnterpriseProvinceChange"
                />
                <CustomSelect
                  v-model="verifyForm.enterpriseCity"
                  :options="enterpriseCityOptions"
                  placeholder="城市"
                  class="address-select"
                  :disabled="!verifyForm.enterpriseProvince"
                  @change="onEnterpriseCityChange"
                />
                <CustomSelect
                  v-model="verifyForm.enterpriseDistrict"
                  :options="enterpriseDistrictOptions"
                  placeholder="区县"
                  class="address-select"
                  :disabled="!verifyForm.enterpriseCity"
                />
              </div>
              <input 
                v-model="verifyForm.registrationPlace" 
                type="text" 
                class="form-input"
                placeholder="请输入详细营业地址"
                maxlength="100"
                :readonly="fieldLocked.address"
                style="margin-top: 12px;"
              />
            </div> -->

            <!-- <div class="form-group">
              <label class="form-label">经营范围</label>
              <textarea 
                v-model="verifyForm.businessScope" 
                class="form-textarea"
                placeholder="请输入主营业务范围"
                maxlength="200"
                :readonly="fieldLocked.businessScope"
              ></textarea>
            </div> -->

            <!-- <div class="form-group">
              <label class="form-label">营业期限</label>
              <div v-if="verifyForm.businessTerm === 'permanent' || verifyForm.businessTerm === '长期' || verifyForm.businessTerm === '长期有效'" class="permanent-expiry">
                <input 
                  value="长期有效" 
                  type="text" 
                  class="form-input permanent-input"
                  readonly
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToDateInput"
                  type="button"
                >
                  改为日期
                </button>
              </div>
              <div v-else class="date-expiry">
                <input 
                  v-model="verifyForm.businessTerm" 
                  type="date" 
                  class="form-input"
                  placeholder="请选择营业期限到期时间"
                  :min="todayDate"
                  :readonly="fieldLocked.businessTerm"
                />
                <button 
                  v-if="!fieldLocked.businessTerm"
                  class="change-expiry-btn"
                  @click="changeToPermanent"
                  type="button"
                >
                  设为长期
                </button>
              </div>
            </div> -->

            <div class="form-group">
              <label class="form-label">手机号码</label>
              <div class="phone-input-group">
                <input 
                  v-model="verifyForm.phone" 
                  type="tel" 
                  class="form-input"
                  placeholder="请输入手机号"
                  maxlength="11"
                  :readonly="fieldLocked.phone"
                />
                <button 
                  class="sms-btn" 
                  :disabled="smsCountdown > 0"
                  @click="sendSmsCode"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">验证码</label>
              <input 
                v-model="verifyForm.smsCode" 
                type="text" 
                class="form-input"
                placeholder="请输入短信验证码"
                maxlength="6"
              />
            </div>
            
            <!-- <div class="form-group">
              <label class="form-label">企业邮箱（选填）</label>
              <input 
                v-model="verifyForm.enterpriseEmail" 
                type="email" 
                class="form-input"
                placeholder="请输入企业邮箱（选填）"
                maxlength="50"
              />
            </div> -->
          </div>


        </div>

        <!-- 提交按钮 -->
        <button 
          class="submit-btn" 
          :disabled="!canSubmit || isSubmitting"
          @click="submitVerification"
        >
          <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
          {{ isSubmitting ? '提交中...' : '提交认证' }}
        </button>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useNotification } from '@/composables/useNotification'
import UserAPI from '@/api/user'
import AuthAPI from '@/api/auth'
import DictionaryAPI from '@/api/dictionary'
import type { DictionaryItem } from '@/api/dictionary'
// OCR和上传相关导入已移除，因为不再需要证件上传功能
import CustomSelect from '@/components/CustomSelect.vue'
import AppPageHeader from '@/components/AppPageHeader.vue'

// 响应式数据
const router = useRouter()
const { success, error, info } = useNotification()

// 认证状态
const verificationStatus = ref({
  isVerified: false,
  idName: '',
  idNo: '',
  statusDate: '',
  identifyType: 'person' as 'person' | 'enterprise'
})

// 表单数据
const verifyForm = reactive({
  identifyType: 'person' as 'person' | 'enterprise',
  idType: '0',
  idName: '',
  idNo: '',
  phone: '',
  smsCode: '',
  legalName: '', // 企业认证需要
  // 移除不需要的字段：
  // idCard: '', // 证件照片filePath - 已移除上传功能
  // businessTerm: '', // 证件到期时间 - 已移除
  // address: '', // 地址 - 已移除
  // registrationPlace: '', // 营业地址 - 已移除
  // registrationArea: '', // 注册区域 - 已移除
  // businessScope: '', // 经营范围 - 已移除
  // enterprisePhone: '', // 企业电话 - 已移除
  // enterpriseEmail: '', // 企业邮箱 - 已移除
  // province: '', // 省份 - 已移除
  // city: '', // 城市 - 已移除
  // enterpriseProvince: '', // 企业省份 - 已移除
  // enterpriseCity: '', // 企业城市 - 已移除
  // enterpriseDistrict: '' // 企业区县 - 已移除
})

// 短信验证码倒计时
const smsCountdown = ref(0)
const isSubmitting = ref(false)

// 字典数据
const idTypeOptions = ref<DictionaryItem[]>([]) // 企业证件类型
const personIdTypeOptions = ref<DictionaryItem[]>([]) // 个人证件类型

// 字段锁定状态
const fieldLocked = reactive({
  idName: false,        // 姓名/企业名称
  idNo: false,          // 身份证号/统一社会信用代码
  phone: false,         // 手机号
  legalName: false      // 法人姓名
})

/**
 * 返回上一页
 */
const goBack = () => {
  router.back()
}

/**
 * 切换认证类型
 */
const switchVerifyType = (type: 'person' | 'enterprise') => {
  // 保存手机号码，切换类型时不应该丢失
  const currentPhone = verifyForm.phone
  const currentPhoneLocked = fieldLocked.phone
  
  verifyForm.identifyType = type
  // 重置表单数据
  Object.assign(verifyForm, {
    identifyType: type,
    idType: type === 'person' ? '0' : '',
    idName: '',
    idNo: '',
    phone: currentPhone, // 保持手机号码不变
    smsCode: '',
    legalName: ''
  })
  
  // 保持手机号码锁定状态
  fieldLocked.phone = currentPhoneLocked
}

/**
 * 证件类型变化处理
 */
const onIdTypeChange = (value: string, option: DictionaryItem | null) => {
  const typeText = verifyForm.identifyType === 'person' ? '个人' : '企业'
  console.log(`${typeText}证件类型变化:`, value, option?.label)
  // 这里可以根据证件类型的变化来调整表单验证规则或其他逻辑
}

// 省份变化处理函数已移除，因为不再需要地址选择功能

// 企业地址变化处理函数已移除，因为不再需要地址选择功能

// 企业地址级联选择变化处理函数已移除，现在使用分离的下拉选择

/**
 * 发送短信验证码
 */
const sendSmsCode = async () => {
  if (!verifyForm.phone) {
    error('请输入手机号')
    return
  }

  if (!/^1[3-9]\d{9}$/.test(verifyForm.phone)) {
    error('请输入正确的手机号')
    return
  }

  try {
    await AuthAPI.sendSmsCode({
      phone: verifyForm.phone,
      scene: 'identify'
    })
    
    success(`验证码已发送到手机号 ${verifyForm.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')}，请注意查收！`)
    
    // 开始倒计时
    smsCountdown.value = 60
    const timer = setInterval(() => {
      smsCountdown.value--
      if (smsCountdown.value <= 0) {
        clearInterval(timer)
      }
    }, 1000)
  } catch (err: any) {
    error(err.response?.data?.msg || '验证码发送失败')
  }
}

// OCR相关函数已移除，因为不再需要证件上传功能

/**
 * 检查是否可以提交
 */
const canSubmit = computed(() => {
  // 基础验证：姓名/企业名称、证件号码、手机号、验证码
  const basicValid = verifyForm.idName && 
                    verifyForm.idNo && 
                    verifyForm.phone && 
                    verifyForm.smsCode

  if (verifyForm.identifyType === 'person') {
    // 个人认证：只需要基础验证
    return basicValid
  } else {
    // 企业认证：基础验证 + 法人姓名
    return basicValid && verifyForm.legalName
  }
})

// 格式化函数已移除，因为不再需要证件到期时间处理

// 地址相关函数已移除，因为不再需要地址选择功能

/**
 * 提交认证
 */
const submitVerification = async () => {
  if (!canSubmit.value) {
    error('请完善所有必填信息')
    return
  }

  isSubmitting.value = true

  try {
    // 构建符合API期望的数据格式
    const submitData = {
      ...verifyForm,
      idCard: '', // 添加必需的字段，但设为空值
      address: '', // 添加必需的字段，但设为空值
      businessTerm: '', // 添加必需的字段，但设为空值
      registrationPlace: '', // 添加必需的字段，但设为空值
      registrationArea: '', // 添加必需的字段，但设为空值
      businessScope: '', // 添加必需的字段，但设为空值
      enterprisePhone: '', // 添加必需的字段，但设为空值
      enterpriseEmail: '', // 添加必需的字段，但设为空值
      province: '', // 添加必需的字段，但设为空值
      city: '' // 添加必需的字段，但设为空值
    }
    
    console.log('提交实名认证数据:', submitData)
    
    await UserAPI.submitVerification(submitData)
    
    success('实名认证提交成功，请等待审核')
    
    // 重新加载认证状态
    setTimeout(() => {
      loadVerificationStatus()
    }, 1000)
    
  } catch (err: any) {
    error(err.response?.data?.msg || '提交失败，请重试')
  } finally {
    isSubmitting.value = false
  }
}

/**
 * 格式化日期
 */
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

// 证件到期时间相关函数已移除，因为不再需要证件到期时间处理

/**
 * 加载字典数据
 */
const loadDictionaryData = async () => {
  try {
    // 并行加载所有字典数据
    const [idTypeResponse, personIdTypeResponse] = await Promise.all([
      DictionaryAPI.getIdTypes(),
      DictionaryAPI.getPersonIdTypes()
    ])
    
    // 加载企业证件类型
    if (idTypeResponse.code === 200 && idTypeResponse.data) {
      idTypeOptions.value = idTypeResponse.data
    }
    
    // 加载个人证件类型
    if (personIdTypeResponse.code === 200 && personIdTypeResponse.data) {
      personIdTypeOptions.value = personIdTypeResponse.data
    }
  } catch (err) {
    console.warn('Failed to load dictionary data:', err)
    // 设置默认值
    idTypeOptions.value = [
      { 
        value: '1', 
        label: '统一社会信用代码',
        code: '1',
        type: 'id_type',
        sort: 1,
        isDefault: true,
        status: 'active'
      },
      { 
        value: '2', 
        label: '营业执照注册号',
        code: '2',
        type: 'id_type',
        sort: 2,
        isDefault: false,
        status: 'active'
      },
      { 
        value: '3', 
        label: '组织机构代码',
        code: '3',
        type: 'id_type',
        sort: 3,
        isDefault: false,
        status: 'active'
      }
    ]
    
    // 设置个人证件类型默认值
    personIdTypeOptions.value = [
      { 
        value: '0', 
        label: '身份证', 
        code: '0', 
        type: 'id_type_person', 
        sort: 1, 
        isDefault: true, 
        status: 'active' 
      },
      { 
        value: '1', 
        label: '护照', 
        code: '1', 
        type: 'id_type_person', 
        sort: 2, 
        isDefault: false, 
        status: 'active' 
      },
      { 
        value: '2', 
        label: '军官证', 
        code: '2', 
        type: 'id_type_person', 
        sort: 3, 
        isDefault: false, 
        status: 'active' 
      }
    ]
  }
}

/**
 * 加载用户信息并填入手机号
 */
const loadUserInfo = async () => {
  try {
    // 首先尝试从localStorage获取用户信息
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      const user = JSON.parse(userInfo)
      if (user.phone || user.phonenumber) {
        verifyForm.phone = user.phone || user.phonenumber
        fieldLocked.phone = true // 锁定手机号字段
        return
      }
    }

    // 如果localStorage中没有手机号，调用API获取
    const response = await AuthAPI.getUserInfo()
    if (response.code === 200 && response.user) {
      const userData = response.user
      if (userData.phonenumber) {
        verifyForm.phone = userData.phonenumber
        fieldLocked.phone = true // 锁定手机号字段
        // 同时更新localStorage中的用户信息，统一使用phone字段
        const userToSave = { ...userData, phone: userData.phonenumber }
        localStorage.setItem('userInfo', JSON.stringify(userToSave))
      }
    }
  } catch (err) {
    console.warn('获取用户信息失败:', err)
    // 获取失败不影响其他功能，用户可以手动输入手机号
  }
}

/**
 * 加载认证状态
 */
const loadVerificationStatus = async () => {
  try {
    const response = await UserAPI.getVerificationStatus()
    if (response.code === 200 && response.data) {
      const data = response.data
      verificationStatus.value = {
        isVerified: data.statusCd === '1000',
        idName: data.idName || '',
        idNo: data.idNo || '',
        statusDate: data.statusDate || '',
        identifyType: data.identifyType || 'person'
      }
    }
  } catch (err) {
    // 如果获取失败，说明未认证，保持默认状态
    console.warn('Failed to load verification status:', err)
  }
}

// 生命周期钩子
onMounted(async () => {
  // 并行加载各种数据
  await Promise.all([
    loadVerificationStatus(),
    loadDictionaryData(),
    loadUserInfo() // 添加用户信息加载
  ])
})

// 组件卸载时清理已移除，因为不再需要图片预览功能
</script>

<style scoped>
.verify-page {
  background: var(--gradient-hero);
  color: var(--neutral-100);
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-content {
  padding: 0 16px 40px 16px;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  position: sticky;
  top: 0;
  background: var(--gradient-hero);
  z-index: 10;
  position: relative;
}

.back-btn {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--neutral-100);
  font-size: 18px;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.3s;
  z-index: 1;
}

.back-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-align: center;
}

.status-card {
  background: var(--gradient-card);
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.status-card.verified {
  border-color: rgba(52, 211, 153, 0.3);
  background: linear-gradient(135deg, rgba(52, 211, 153, 0.1), rgba(16, 185, 129, 0.05));
}

.status-icon {
  font-size: 48px;
  color: #10B981;
  margin-bottom: 16px;
}

.status-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--neutral-100);
}

.status-desc {
  font-size: 14px;
  color: var(--neutral-400);
  margin-bottom: 20px;
}

.verify-info {
  text-align: left;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  color: var(--neutral-400);
  font-size: 14px;
}

.value {
  color: var(--neutral-100);
  font-size: 14px;
  font-weight: 500;
}

.verify-form {
  margin: 20px 0;
}

.form-section {
  background: var(--gradient-card);
  border-radius: 12px;
  padding: 20px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--neutral-100);
  margin-bottom: 16px;
}

.verify-type-tabs {
  display: flex;
  gap: 12px;
}

.type-tab {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 16px;
  color: var(--neutral-400);
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.type-tab.active {
  background: var(--gradient-primary);
  border-color: var(--primary-gold);
  color: white;
}

.type-tab i {
  font-size: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: var(--neutral-300);
  margin-bottom: 8px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: var(--neutral-100);
  font-size: 16px;
  transition: all 0.3s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(200, 134, 13, 0.2);
}

.form-input::placeholder,
.form-select::placeholder,
.form-textarea::placeholder {
  color: var(--neutral-500);
}

/* 只读字段样式 */
.form-input:read-only,
.form-select:read-only,
.form-textarea:read-only {
  background: rgba(200, 134, 13, 0.1);
  border-color: rgba(200, 134, 13, 0.3);
  color: var(--primary-gold);
  cursor: not-allowed;
}

.form-input:read-only::placeholder,
.form-select:read-only::placeholder,
.form-textarea:read-only::placeholder {
  color: rgba(200, 134, 13, 0.5);
}

.phone-input-group {
  display: flex;
  gap: 12px;
}

.phone-input-group .form-input {
  flex: 1;
}

.sms-btn {
  background: var(--primary-gold);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
}

.sms-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

.sms-btn:not(:disabled):hover {
  background: #B8860B;
}

.address-group {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.address-select {
  flex: 1;
  min-width: 0; /* 允许flex项目缩小到内容宽度以下 */
}

/* 地址选择器样式适配 */
.address-group .custom-select {
  flex: 1;
  min-width: 0; /* 确保可以缩小并显示省略号 */
}

.upload-area {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.upload-item {
  flex: 1;
  min-width: 120px;
}

.upload-item.full-width {
  flex: none;
  width: 100%;
}

.upload-card {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-card:hover {
  border-color: var(--primary-gold);
  background: rgba(255, 255, 255, 0.08);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.upload-card i {
  font-size: 32px;
  color: var(--neutral-400);
  margin-bottom: 12px;
}

.upload-text {
  font-size: 14px;
  color: var(--neutral-400);
}

.image-preview {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 120px;
  border-radius: 8px;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 8px;
  color: white;
  font-size: 14px;
}

.image-overlay i {
  font-size: 24px;
  margin-bottom: 8px;
  color: var(--primary-gold);
}

.upload-card:hover .image-overlay {
  opacity: 1;
}

.submit-btn {
  width: 100%;
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
}

/* 证件到期时间相关样式 */
.permanent-expiry,
.date-expiry {
  display: flex;
  gap: 12px;
  align-items: center;
}

.permanent-input {
  flex: 1;
  background: rgba(52, 211, 153, 0.1) !important;
  border-color: rgba(52, 211, 153, 0.3) !important;
  color: #10B981 !important;
}

.change-expiry-btn {
  background: rgba(255, 255, 255, 0.1);
  color: var(--neutral-300);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
  white-space: nowrap;
  flex-shrink: 0;
}

.change-expiry-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--primary-gold);
  color: var(--primary-gold);
}

.submit-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(218, 165, 32, 0.3);
}

/* 响应式优化 - 地址选择器 */
@media (max-width: 640px) {
  .address-group {
    gap: 8px;
  }
  
  .address-select {
    min-width: 80px; /* 在移动端设置最小宽度 */
  }
}

@media (max-width: 480px) {
  .address-group {
    flex-direction: column;
    gap: 12px;
  }
  
  .address-select {
    min-width: unset;
  }
}
</style> 